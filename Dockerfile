#syntax=docker/dockerfile:1.4

FROM node:24-alpine AS node
WORKDIR /app
ENV PUBLIC_API_BASE_URL=/
COPY package*.json vite.config.js ./
RUN npm ci
COPY assets assets/
RUN npm run build
RUN npm prune --production

# Base FrankenPHP image
FROM gitlab.alienor.net:5050/dev-docker/web_php/frankenphp_base:8.4-frankenphp AS frankenphp_base

# ~~~~~ CONFIGURE PHP-CS-FIXER ~~~~~~~ \
# See https://github.com/PHP-CS-Fixer/PHP-CS-Fixer?tab=readme-ov-file#supported-php-versions
# Ne supporte pas encore PHP 8.4 :(
ENV PHP_CS_FIXER_IGNORE_ENV=1

# Install redis extension
RUN pecl install -o -f redis \
    && rm -rf /tmp/pear \
    && docker-php-ext-enable redis

FROM gitlab.alienor.net:5050/dev-docker/web_php/frankenphp_dev:8.4-frankenphp AS frankenphp_dev

# ~~~~~ CONFIGURE PHP-CS-FIXER ~~~~~~~ \
# See https://github.com/PHP-CS-Fixer/PHP-CS-Fixer?tab=readme-ov-file#supported-php-versions
# Ne supporte pas encore PHP 8.4 :(
ENV PHP_CS_FIXER_IGNORE_ENV=1

# Install redis extension
RUN pecl install -o -f redis \
    && rm -rf /tmp/pear \
    && docker-php-ext-enable redis

COPY --link --chmod=755 frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
ENTRYPOINT ["docker-entrypoint"]

FROM frankenphp_base AS frankenphp_prod

# Copy assets
COPY --from=node /app/public/build public/build/

ENV APP_ENV=prod
ENV FRANKENPHP_CONFIG="import worker.Caddyfile"

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

COPY --link frankenphp/conf.d/20-app.prod.ini $PHP_INI_DIR/conf.d/
COPY --link frankenphp/worker.Caddyfile /etc/caddy/worker.Caddyfile

# prevent the reinstallation of vendors at every changes in the source code
COPY --link composer.* symfony.* ./
RUN set -eux; \
    composer install --no-cache --prefer-dist --no-dev --no-autoloader --no-scripts --no-progress

# copy sources
COPY --link . ./
RUN rm -Rf frankenphp/

RUN set -eux; \
    mkdir -p var/cache var/log; \
    composer dump-autoload --classmap-authoritative --no-dev; \
    composer run-script --no-dev post-install-cmd; \
    chmod +x bin/console; sync;

COPY --link --chmod=755 frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
ENTRYPOINT ["docker-entrypoint"]
CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile" ]
