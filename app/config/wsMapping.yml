### ClientsService ####
Client:
  tags:
#    MouvementPoint:
#      type: 1n
#      parser: MouvementPoint
#      doNotSend: true
#      class: ZeFid\PortailClientBundle\Entity\MouvementPoint
    enfant:
      type: 1n
      parser: Enfant
      class: ZeFid\PortailClientBundle\Entity\Enfant
    animal:
      type: mn
      parser: Animal
      class: ZeFid\PortailClientBundle\Entity\Animal
      filterKey: id
      filterValue: id
    magasin:
      type: n1
      parser: Magasin
      class: ZeFid\PortailClientBundle\Entity\Magasin
      doNotSend: true
    categorieArticle:
      type: mn
      parser: CategorieArticle
      class: ZeFid\PortailClientBundle\Entity\CategorieArticle
      filterKey: id
      filterValue: id
  attributes:
    id:
      cle: CODECARTE
      type: integer
    codeCarte:
      cle: CODECARTE
      type: ean13
    dateSuppression:
      cle: DATESUPPRESSION
      type: date
      doNotSend: true
    site:
      cle: SITE
      type: string
    magasinId:
      cle: MAGASIN
      type: string
    codeCivilite:
      cle: CODECIVILITE
      type: integer
      doNotSend: true
    civilite:
      cle: CIVILITE
      type: string
    nom:
      cle: NOM
      type: string
    prenom:
      cle: PRENOM
      type: string
    numero:
      cle: NUMERO
      type: string
    codepostal:
      cle: CODEPOSTAL
      type: string
    escalier:
      cle: ESCALIER
      type: string
    batiment:
      cle: BATIMENT
      type: string
    voie:
      cle: VOIE
      type: string
    lieuDit:
      cle: LIEUDIT
      type: string
    ville:
      cle: VILLE
      type: string
    dateNaissance:
      cle: DATENAISSANCE
      type: date
    npai:
      cle: NPAI
      type: boolean
      doNotSend: true
    npaiEmail:
      cle: NPAIEMAIL
      type: boolean
      doNotSend: true
    npaiSms:
      cle: NPAISMS
      type: boolean
      doNotSend: true
    telephoneFixe:
      cle: TELEPHONEFIXE
      type: string
    telephoneMobile:
      cle: TELEPHONEMOBILE
      type: string
    email:
      cle: EMAIL
      type: string
    dateCreation:
      cle: DATECREATION
      type: date
      doNotSend: true
    codePaysClient:
      cle: CODEPAYSCLIENT
      type: string
    envoieSmsInterne:
      cle: ENVOIESMSINTERNE
      type: boolean
      doNotSend: true
    envoieEmailInterne:
      cle: ENVOIEEMAILINTERNE
      type: boolean
      doNotSend: true
    envoieSmsExterne:
      cle: ENVOIESMSEXTERNE
      type: boolean
      doNotSend: true
    envoieEmailExterne:
      cle: ENVOIEEMAILEXTERNE
      type: boolean
      doNotSend: true
    envoiCourrierInterne:
      cle: ENVOIECOURRIERINTERNE
      type: boolean
      doNotSend: true
    envoiCourrierExterne:
      cle: ENVOIECOURRIEREXTERNE
      type: boolean
      doNotSend: true
    montantCarte:
      cle: MONTANTCART
      type: float
      doNotSend: true
    dateDernierAchat:
      cle: DATEDERNIERACHAT
      type: date
      doNotSend: true
    montantDernierAchat:
      cle: MONTANTDERNIERACHAT
      type: float
      doNotSend: true
    situationFamiliale:
      cle: SITUATIONFAMILIALE
      type: string
    x:
      cle: X
      type: float
      doNotSend: true
    y:
      cle: Y
      type: float
      doNotSend: true
    datePrevueNaissance:
      cle: DATEPREVUENAISSANCE
      type: date
    bebe:
      cle: BEBE
      type: string
    dateDernierChq:
      cle: DATEDERNIERCHEQUE
      type: date
      doNotSend: true
    montantDernierCheque:
      cle: MONTANTDERNIERCHEQUE
      type: string
      doNotSend: true
    numDernierCheque:
      cle: NODERNIERCHEQUE
      type: string
      doNotSend: true
    dateFinValiditeDernierChq:
      cle: DATEVALIDITEDERNIERCHEQUE
      type: date
      doNotSend: true
    dateEncaisDernierCheques:
      cle: DATEENCAISDERNIERCHEQUES
      type: date
      doNotSend: true
    animalAutre:
      cle: ANIMALDETAIL
      type: string
    dateDernierChqAnniv:
      cle: DATEDERNIERANNIV
      type: date
      doNotSend: true
    numDernierChqAnniv:
      cle: NODERNIERANNIV
      type: string
      doNotSend: true
    dateFinValiditeDernierChqAnniv:
      cle: DATEVALIDITEDERNIERANNIV
      type: date
      doNotSend: true
    dateDernierChequeBvnu:
      cle: DATEDERNIERBIENVENUE
      type: date
      doNotSend: true
    dateFinValiditeDernierChequeBvnu:
      cle: DATEVALIDITEDERNIERBIENVENUE
      type: date
      doNotSend: true
    noDernierChequeBvnu:
      cle: NODERNIERBIENVENUE
      type: string
      doNotSend: true
    dateEncasDernierChequeBvnu:
      cle: DATEENCAISDERNIERBIENVENUE
      type: date
      doNotSend: true
    numeroDernierBonAchat:
      cle: NODERNIERBONACHAT
      type: string
      doNotSend: true
    dateDernierBonAchat:
      cle: DATEDERNIERBONACHAT
      type: date
      doNotSend: true
    dateValiditeDernierBonAchat:
      cle: DATEVALIDITEDERNIERBONACHAT
      type: date
      doNotSend: true
    montantDernierBonAchat:
      cle: MONTANTDERNIERBONACHAT
      type: string
      doNotSend: true

Magasin:
  attributes:
    id:
      cle: MAGASIN
      type: string
    site:
      cle: SITE
      type: string
    magasin:
      cle: MAGASIN
      type: string
    libelle:
      cle: LIBELLE
      type: string
    adresse1:
      cle: ADRESSE1
      type: string
    adresse2:
      cle: ADRESSE2
      type: string
    codePostal:
      cle: CODEPOSTAL
      type: string
    ville:
      cle: VILLE
      type: string
    longitude:
      cle: LONGITUDE
      type: string
    latitude:
      cle: LATITUDE
      type: string
    email:
      cle: EMAIL
      type: string
    siteWebMag:
      cle: SITEWEBMAG
      type: string

MouvementPoint:
    attributes:
      dateOperation:
        cle: DATEOPERATION
        type: date
      operation:
        cle: OPERATION
        type: string
      libelle:
        cle: LIBELLE
        type: string
      nbPoints:
        cle: NBPOINTS
        type: float

Pkpass:
  attributes:
    idUser:
      cle: client
      type: integer
    codeCarte:
      cle: codecarte
      type: integer
    idEnseigne:
      cle: enseigne
      type: string
    codepostal:
      cle: codepostal
      type: string
      doNotSend: true
    DATENAISSANCE:
      cle: DATENAISSANCE
      type: date
    token:
      cle: token
      type: string
    url:
      cle: url
      type: string
    urlQrCode:
      cle: url-img
      type: string

Enfant:
  attributes:
    prenom:
      cle: PRENOM
      type: string
    dateNaissance:
      cle: DATENAISSANCE
      type: date
    genre:
      cle: GENRE
      type: string

CategorieArticle:
  attributes:
    id:
      cle: CODECATEGORIE
      type: string
    libelle:
      cle: LIBELLECATEGORIE
      type: string
      doNotSend: true

Animal:
  attributes:
    id:
      cle: IDANIMAL
      type: string
    libelle:
      cle: LIBELLEANIMAL
      type: string
      doNotSend: true

ClientActivate:
  attributes:
    codeCarte:
      cle: CODECARTE
      type: string
    codeAcces:
      cle: CODEACCES
      type: string
    programme:
      cle: PROGRAMME
      type: string