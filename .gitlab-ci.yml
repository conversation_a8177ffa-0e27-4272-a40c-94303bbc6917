default:
  tags:
    - anetdev

# TODO mettre en place un build d'une image contenant keycloak + les .jar des thèmes
# refaire le job de test pour mettre cette image en service et faire un curl sur le healthcheck

stages:
  - test

test-theme:
  stage: test
  image: docker:latest
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - apk add --no-cache curl
    - mkdir -p $HOME/.docker
    - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
  script:
    - docker compose -f compose.yaml -f compose.prod.yaml up -d
    - echo "⏳ Attente du démarrage de Keycloak..."
    - sleep 20  # temps pour laisser Keycloak démarrer
    - docker run --rm --network $(docker network ls --filter name=keycloak_default -q) curlimages/curl:latest \
      curl -f http://keycloak:8080/health/ready
    - echo "✅ Keycloak is healthy with the theme applied"