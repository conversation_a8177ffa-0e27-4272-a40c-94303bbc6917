{% block content %}
{% set icons = [] %}
{% for key,colonne in colonnes %}
    {% if column == colonne %}
        {% if way == 'ASC' %}
            {% set icons = icons|merge({(key) : 'fa-sort-up'}) %}
        {% else %}
            {% set icons = icons|merge({(key) : 'fa-sort-down'}) %}
        {% endif %}
    {% else %}
        {% set icons = icons|merge({(key) : 'fa-sort'}) %}
    {% endif %}
{% endfor %}
{% if showForm %}
<div class="page-header">
    <div class="pageWidth pbxl">
    {{ form_start(searchForm) }}
        <div id="search-form" class="row centered-divs mbxl">
            <div class="left mrxl">
                <div class="input-group right">
                    {{ form_row(searchForm.nom) }}
                </div>
            </div>
            {% if searchForm.rrrm is defined %}
            <div class="left mrxl">
                <div class="input-group">
                    {{ form_row(searchForm.rrrm) }}
                </div>
            </div>
            {% endif %}
            {% if searchForm.distributeur is defined %}
            <div class="left mrxl">
                <div class="input-group">
                    {{ form_row(searchForm.distributeur) }}
                </div>
            </div>
            {% endif %}
            <div class="left mrxl">
                <div class="input-group">
                    {{ form_row(searchForm.abonne) }}
                </div>
            </div>
            <div class="left">
                <div class="input-group">
                    {{ form_row(searchForm.ville) }}
                </div>
            </div>
        </div>
        <div class="text-center">
        {{ form_row(searchForm.search) }}
        </div>
    {{ form_end(searchForm) }}
    </div>
</div>
{% endif %}
<div id="jobs-items" class=""
    data-column="{{ column }}"
    data-way="{{ way }}"
    data-current-page="{{ page }}"
    data-nb-total-pages="{{ nbTotalPages }}">
{% if items is defined and items | length %}
    <div class="row collapse table relative">
        {{ block('loaderPage', 'blocks.html.twig') }}
        <table class="rq-table mtxl">
            <thead>
                <tr>
        {% for key,colonne in colonnes %}
                    <th>
                        <a href="#!" data-action="sort" data-column-name="{{ colonne }}" data-sort-way="{{ way }}">
                            <i class="fas {{ icons[key] }} fa-white fa-small"></i>
                            {{ colonne }}
                        </a>
                    </th>
        {% endfor %}
                </tr>
            </thead>
            <tbody>
        {% for item in items %}
            <tr>
                {% for value in item %}
                    {% if value == "true" %}
                    <td class="text-center"><i class="fas fa-check fa-green fa-big"></i>
                    {% elseif value == "false" %}
                    <td class="text-center"><i class="fas fa-xmark fa-red fa-big"></i>
                    {% else %}
                    <td>{{ value }}
                    {% endif %}
                    </td>
                {% endfor %}
            </tr>
        {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="row mtl">
        <div class="modal-errors mbxxl">{{ "global.noResult" | trans }}</div>
    </div>
{% endif %}
</div>
{% if showForm and nbTotalPages > 1 %}
<div class="text-center mtxl">
    <div id="pagination-nav" data-page="{{ page }}" data-nb-pages="5">
        <a href="#!" class="btn btn--smallBlue pagination-first" data-page="1"{% if page == 1 %}style="display:none"{% endif %}><<</a>
        <a href="#!" class="btn btn--smallBlue pagination-previous" data-page=""{% if page == 1 %}style="display:none"{% endif %}><</a>
    {% for i in 1..nbTotalPages %}
        <a href="#!" class="btn btn--smallBlue pagination-num{% if i == 1 %} active{% endif %}" data-page="{{ i }}"{% if i > 3 %}style="display:none"{% endif %}>{{ i }}</a>
    {% endfor %}
        <a href="#!" class="btn btn--smallBlue pagination-next" data-page="{{ page + 1 }}"{% if nbTotalPages < 2 %}style="display:none"{% endif %}>></a>
        <a href="#!" class="btn btn--smallBlue pagination-last" data-page="{{ nbTotalPages }}"{% if nbTotalPages < 2 %}style="display:none"{% endif %}>>></a>
    </div>
</div>
{% endif %}
{% endblock %}