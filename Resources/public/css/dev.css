div[class^=tx-]:hover {
	outline: 1px dotted rgba(0, 0, 0, 0.8);
}
div[class^=tx-]:hover:before {
	content: attr(class);
	position: fixed;
	right: 0;
	bottom: 0;
	color: white;
	z-index: 1;
	padding: 5px;
	font-size: 14px;
	font-weight: bold;
	font-family: Monospace;
	background: rgba(0, 0, 0, 0.7);
}

div[class~=csc-text]:hover {
	outline: 1px dotted rgba(255, 0, 0, 0.8);
}
div[class~=csc-text]:hover:before {
	content: "Bloc Texte";
	position: fixed;
	left: 0;
	bottom: 0;
	color: white;
	z-index: 1;
	padding: 5px;
	font-size: 14px;
	font-weight: bold;
	font-family: Monospace;
	background: rgba(255, 0, 0, 0.7);
}

div[class~=csc-textpic]:hover {
	outline: 1px dotted rgba(255, 0, 0, 0.8);
}
div[class~=csc-textpic]:hover:before {
	content: "Bloc Texte / Image";
	position: fixed;
	left: 0;
	bottom: 0;
	color: white;
	z-index: 1;
	padding: 5px;
	font-size: 14px;
	font-weight: bold;
	font-family: Monospace;
	background: rgba(255, 0, 0, 0.7);
}