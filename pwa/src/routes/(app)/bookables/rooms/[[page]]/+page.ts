import type { PageLoad } from '../../../../../../.svelte-kit/types/src/routes';
import {getQueryParameterFromUrl, type Pagination} from "$lib/utils";
import {type Bookable_read_room} from "$lib/zodios";

/**
 * - Fait un appel à la validation d'email du compte avec le token
 * - Si une erreur est retournée affichage de l'erreur
 * - Sinon redirection sur le formulaire d'authentification
 */
export const load = (async ({ params, parent }) => {
    const { zodiosClient } = await parent();

    const page: number = params.page && /\d/.test(params.page) ? parseInt(params.page) : 1;
    let pagination: Pagination | null = null;
    let rooms: Bookable_read_room[] = [];
    try {
        const response = await zodiosClient.roomSearch({queries: { page }});
        rooms = response['hydra:member'];
        if (response['hydra:view']) {
            pagination = {
                currentPage: page,
                firstPage: parseInt(getQueryParameterFromUrl(response['hydra:view']['hydra:first'], 'page')),
                lastPage: parseInt(getQueryParameterFromUrl(response['hydra:view']['hydra:last'], 'page')),
                totalItems: parseInt(response['hydra:totalItems']),
            }
        }
    } catch (e) {
        console.error(e);
        return;
    }

    return {
        pagination,
        rooms,
    };
}) satisfies PageLoad;
