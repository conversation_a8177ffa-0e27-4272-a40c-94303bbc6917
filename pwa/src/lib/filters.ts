import {get} from "svelte/store";
import {profile} from "$lib/stores";

type ApiFilters = {
    weekNumber: number,
    weekYear: number,
    minStartTime?: string,
    maxEndTime?: string,
};

type BookingApiFilters = ApiFilters & {
    ownerIdentifiers?: string,
};

type TimeSlotApiFilters = ApiFilters;

export type RoomBookingApiFilters = BookingApiFilters & {
    roomIdentifiers?: string,
    minRoomCapacity?: number,
};

export type RoomTimeSlotApiFilters = TimeSlotApiFilters & {
    roomIdentifiers?: string,
    minRoomCapacity?: number,
};

export type VehicleBookingApiFilters = BookingApiFilters & {
    vehicleIdentifiers?: string,
    minVehicleCapacity?: number,
};

export type VehicleTimeSlotApiFilters = TimeSlotApiFilters & {
    vehicleIdentifiers?: string,
    minVehicleCapacity?: number,
};

function buildApiFilters(weekNumber: number, weekYear: number, minStartTime?: string, maxEndTime?: string): ApiFilters {
    let filters: ApiFilters = {
        weekNumber: weekNumber,
        weekYear: weekYear,
    }

    if (minStartTime !== undefined) {
        filters['minStartTime'] = minStartTime;
    }
    if (maxEndTime !== undefined) {
        filters['maxEndTime'] = maxEndTime;
    }

    return filters;
}

function buildBookingApiFilters(filters: ApiFilters, authenticatedOwnerBookingsOnly: boolean = false): BookingApiFilters {
    if(authenticatedOwnerBookingsOnly) {
        return {
            ...filters,
            ownerIdentifiers: [get(profile).id].join(',')
        };
    }
    return filters;
}

function buildTimeSlotApiFilters(filters: ApiFilters): TimeSlotApiFilters {
    return buildBookingApiFilters(filters, false);
}

export function buildRoomBookingApiFilters(
    weekNumber: number,
    weekYear: number,
    startTime?: string,
    endTime?: string,
    selectedRooms?: any[],
    authenticatedOwnerBookingsOnly?: boolean,
): RoomBookingApiFilters {
    let filters: RoomBookingApiFilters = buildBookingApiFilters(buildApiFilters(weekNumber, weekYear, startTime, endTime), authenticatedOwnerBookingsOnly);
    if(selectedRooms && selectedRooms.length > 0) {
        filters['roomIdentifiers'] = selectedRooms.join(',');
    }

    return filters;
}

export function buildRoomTimeSlotBookingApiFilters(
    weekNumber: number,
    weekYear: number,
    startTime?: string,
    endTime?: string,
    selectedRooms?: any[],
    capacity?: number,
): RoomTimeSlotApiFilters {
    let filters: RoomTimeSlotApiFilters = buildTimeSlotApiFilters(buildApiFilters(weekNumber, weekYear, startTime, endTime));
    if(capacity !== undefined) {
        filters['minRoomCapacity'] = capacity;
    }
    if(selectedRooms != undefined && selectedRooms.length > 0) {
        filters['roomIdentifiers'] = selectedRooms.join(',');
    }

    return filters;
}

export function buildVehicleBookingApiFilters(
    weekNumber: number,
    weekYear: number,
    startTime?: string,
    endTime?: string,
    selectedVehicles?: any[],
    authenticatedOwnerBookingsOnly?: boolean,
): VehicleBookingApiFilters {
    let filters: VehicleBookingApiFilters = buildBookingApiFilters(buildApiFilters(weekNumber, weekYear, startTime, endTime), authenticatedOwnerBookingsOnly);
    if(selectedVehicles && selectedVehicles.length > 0) {
        filters['vehicleIdentifiers'] = selectedVehicles.join(',');
    }

    return filters;
}

export function buildVehicleTimeSlotBookingApiFilters(
    weekNumber: number,
    weekYear: number,
    startTime?: string,
    endTime?: string,
    selectedVehicles?: any[],
): VehicleTimeSlotApiFilters {
    let filters: VehicleTimeSlotApiFilters = buildTimeSlotApiFilters(buildApiFilters(weekNumber, weekYear, startTime, endTime));
    if(selectedVehicles != undefined && selectedVehicles.length > 0) {
        filters['vehicleIdentifiers'] = selectedVehicles.join(',');
    }

    return filters;
}
