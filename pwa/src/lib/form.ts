import translation from '$lib/i18n/zod-messages.json';
import type { endpoints } from '$lib/zodios';
import type { ZodiosResponseByAlias } from '@zodios/core';
import { AxiosError, type AxiosResponse } from 'axios';
import {type FormPathLeaves, type Infer} from 'sveltekit-superforms';
import { superForm, defaults, type FormOptions } from 'sveltekit-superforms/client';
import type { AnyZodObject, ZodEffects } from 'zod';
import { z } from 'zod';
import { zod } from 'sveltekit-superforms/adapters';
// @ts-ignore
import type {ZodValidation} from "sveltekit-superforms/dist/adapters/zod";
type UnwrapEffects<T> = T extends ZodEffects<infer U> ? UnwrapEffects<U> : T extends AnyZodObject ? T : never;

// Zod translations
import i18next from 'i18next';
import { zodI18nMap } from 'zod-i18n-map';

let errorMapGlobal = {};

i18next.init({
  lng: 'fr',
  resources: {
    fr: { zod: translation }
  }
});

type UnprocessableEntityError<ZodSchemaType extends ZodValidation<AnyZodObject>> = AxiosError & {
  response: AxiosResponse<{
    violations: {
      code: string;
      propertyPath: FormPathLeaves<z.infer<ZodSchemaType>>;
      message: string;
    }[];
  }>;
};

function isUnprocessableEntity<ZodSchemaType extends ZodValidation<AnyZodObject>>(
  error: AxiosError
): error is UnprocessableEntityError<ZodSchemaType> {
  return error?.response?.status === 422 || false;
}

type createZodiosFormOptions<T extends ZodValidation<AnyZodObject>, Alias extends string> = {
    id: string;
  schema: T;
  onSubmitted: (
    data: Infer<T>
  ) => Promise<ZodiosResponseByAlias<typeof endpoints, Alias>>;
  initialData?: z.infer<T> | undefined | null;
  messages?: CustomErrorMap<T>;
  options?: Partial<FormOptions<z.infer<T>, unknown>>;
};

/**
 * Creates a form with superform linked to a zodios api endpoint.
 * The endpoint is automatically called when the form is submitted.
 * Symfony errors are automatically rendered for each field.
 * @template T - The type of the Zod validation schema
 * @template Alias - The type of the field aliases
 * @param {createZodiosFormOptions<T, Alias>} options - The options for creating the form
 */
export function createZodiosForm<T extends ZodValidation<AnyZodObject>, Alias extends string>({
                                                                                                  id,
  schema,
  onSubmitted,
  initialData = {},
  messages,
  options
}: createZodiosFormOptions<T, Alias>) {
  messages = messages || {};
  const sForm = superForm(defaults(initialData, zod(schema)), {
      id: id, // https://superforms.rocks/concepts/multiple-forms
    SPA: true,
    invalidateAll: false,
    // @ts-ignore Je sais pas comment faire matcher le type avec T
    validators: zod(schema),
    errorSelector: '.is-invalid', // classe d'erreur bootstrap
    //
    dataType: 'json',
    resetForm: false,
    validationMethod: "oninput",
    async onUpdate({ form, cancel }) {
      if (form.valid && form.posted) {
          try {
              await onSubmitted(form.data);
          } catch (error) {
              console.error(error);
              cancel();
              if (error instanceof AxiosError && (isUnprocessableEntity<T>(error) || error.response?.status === 400)) {
                  if (error.response?.data?.detail) {
                      sForm.message.set(error.response.data.detail);
                  }
                  if (error.response?.data?.violations) {
                      error.response.data.violations.forEach((v) => {
                          sForm.errors.update((errors) => {
                              // Compliqué mais sinon typescript était pas content
                              errors = {
                                  ...errors,
                                  [v.propertyPath]: [...((errors[v.propertyPath] as string[]) || []), v.message]
                              };
                              return errors;
                          });
                      });
                  }
              } else if (error instanceof ServerError) {
                  sForm.message.set(error.message);
              } else {
                  sForm.message.set("Une erreur interne est survenue");
              }
          }
      } else {
        cancel();
      }
    },
    taintedMessage: null,
    ...options
  });

  // Potientiel écrasement si plusieurs formulaire avec les mêmes noms de champs sont chargés en même temps
  errorMapGlobal = { ...errorMapGlobal, ...messages };
  z.setErrorMap(setErrors(errorMapGlobal));

  return sForm;
}

type CustomErrorMapLeave = string | { [key in z.ZodIssueCode]?: string };

export type CustomErrorMap<T extends ZodValidation<AnyZodObject>> = {
  [key in FormPathLeaves<z.infer<T>>]?: CustomErrorMapLeave;
};

/**
 * Permet de surcharger les messages d'erreur
 *
 * `[field]` permet de surcharger tous les messages d'erreur en une seule erreur unique
 * `[z.ZodIssueCode.*]` permet de choisir quel message d'erreur surcharger
 *
 * Exemple :
 * {
 *     pin: 'Veuillez respectez le format suivant : 4 chiffres', // tous les messages d'erreur sont surchargés pour la propriété `pin`
 *     firstname: { // seule les erreurs too_small et invalid_string sont surchargés pour la propriété `firstname`
 *         [z.ZodIssueCode.too_small]: 'La chaine de caractère est trop courte'
 *         [z.ZodIssueCode.invalid_string]: 'La chaine de caractère est invalide'
 *     }
 * }
 */
function setErrors<T extends ZodValidation<AnyZodObject>>(
  errorMap: CustomErrorMap<T>
): z.ZodErrorMap {
  return (issue: z.ZodIssueOptionalMessage, ctx) => {
    let message = null;

    (Object.keys(errorMap) as Array<keyof typeof errorMap>).forEach((path) => {
      if (issue.path[0] === path && path in errorMap) {
        const v = errorMap[path] as CustomErrorMapLeave;

        if (typeof v === 'string') {
          message = { message: v };
          return;
        }

        (Object.keys(v) as Array<keyof typeof v>).forEach((code) => {
          if (issue.code === code) {
            message = { message: v[code] };
          }
        });
      }
    });

    return message || zodI18nMap(issue, ctx);
  };
}

export class ServerError extends Error {
  constructor(msg: string) {
    super(msg);
  }
}
