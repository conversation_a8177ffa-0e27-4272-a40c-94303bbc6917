// permet d'éviter de faire des appels à la même fonction en cas d'appels trop rapides
let timeout: ReturnType<typeof setTimeout>;
export const debounce = (callback: Function, wait = 300) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => callback(), wait);
};

export type Pagination = {
    currentPage: number;
    firstPage: number;
    lastPage: number;
    totalItems: number;
};

export const getQueryParameterFromUrl = (url: string, parameter: string): string | null => {
    const urlSearchParams = new URLSearchParams(url.substring(url.indexOf("?") + 1));
    return urlSearchParams.get(parameter);
};
