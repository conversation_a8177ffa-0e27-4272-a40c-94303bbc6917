<script lang="ts">
    import RoomBookingInterface from "$lib/components/booking/bookable/room/RoomBookingInterface.svelte";
    import VehicleBookingInterface from "$lib/components/booking/bookable/vehicle/VehicleBookingInterface.svelte";
    import {bookableType, bookingStatus} from "$lib/stores.js";
</script>

{#if $bookingStatus === "reserved"}
    {#if $bookableType === "room"}
        <RoomBookingInterface/>
    {:else if $bookableType === "vehicle"}
        <VehicleBookingInterface/>
    {:else}
        <div>Type de réservable non supporté</div>
    {/if}
{:else}
    {#if $bookableType === "room"}
        <RoomBookingInterface/>
    {:else if $bookableType === "vehicle"}
        <VehicleBookingInterface/>
    {:else}
        <div>Type de réservable non supporté</div>
    {/if}
{/if}
