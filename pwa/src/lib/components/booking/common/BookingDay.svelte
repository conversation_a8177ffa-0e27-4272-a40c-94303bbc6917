<script lang="ts">
    import {swipe} from 'svelte-gestures';
    import {addDays, eachHourOfInterval, endOfDay, format, getHours, isPast, isToday, startOfDay} from "date-fns";
    import {activeDay, hasActiveFilters} from "$lib/stores";
    import ReservedTimeSlot from "$lib/components/booking/common/time-slot/ReservedTimeSlot.svelte";
    import AvailableTimeSlot from "$lib/components/booking/common/time-slot/AvailableTimeSlot.svelte";
    import {afterUpdate, createEventDispatcher, onMount} from "svelte";
    import NothingToDisplay from "$lib/components/booking/common/NothingToDisplay.svelte";

    export let timeSlots: {
        num: number,
        day: string,
        dayStatus: string,
        bookingStatus: string,
        rooms: { name: string, color: string, horaires: { start: string, end: string }[] }[]
    }[];
    export let withHours = false;
    export let startOfDays: Date = startOfDay(new Date());
    export let endOfDays: Date = endOfDay(new Date());
    export let day: Date;


    let dispatch = createEventDispatcher();

    function onSwipe(event: Event) {
        const direction = event.detail.direction;
        if (['left', 'right'].includes(direction)) {
            const newActiveDay = direction === 'left' ? addDays($activeDay, 1) : addDays($activeDay, -1);
            dispatch('activeDayChanged', newActiveDay);
        }
    }

    function groupedByHour(timeSlots) {
        const hours = eachHourOfInterval({
            start: startOfDays,
            end: endOfDays
        }).reduce((ac,a) => ({...ac,['_'+format(a, 'HH:00')]:[]}),{});

        return timeSlots.reduce((acc, obj) => {
            const hourKey = '_' + format(new Date(obj.startDate), 'HH:00');
            acc[hourKey].push(obj);
            return acc;
        }, hours);
    }

    let timeSlotsPerHour = {};
    $: timeSlotsPerHour = groupedByHour(timeSlots)

    afterUpdate(() => {
        // Si l'affichage est en version mobile
        if (withHours === true) {
            const startDate = timeSlots && timeSlots[0] && timeSlots[0].startDate ? timeSlots[0].startDate : null;
            const isBookables = timeSlots && timeSlots[0] && timeSlots[0].bookable;
            if (isBookables && startDate !== null && isToday(startDate)) {
                // Si les éléments affichés sont des créneaux disponibles
                // et que le jour affiché est le jour actuel, alors scroll vers l'heure actuelle
                scrollToHour(format(new Date(), 'HH'));
            } else {
                // Sinon, scroll vers le haut de la page
                scrollTo({top: 0, behavior: "instant"});
            }
        }
    });

    function scrollToHour(hour: string) {
        const target = document.querySelector(`.hour[data-hour="${hour}"]`);
        if (target) {
            const headerOffset = 128; // taille du header du calendrier
            const targetPosition = target.getBoundingClientRect().top;
            const offsetPosition = targetPosition + window.pageYOffset - headerOffset;
            window.scrollTo({
                top: offsetPosition,
                behavior: "smooth"
            });
        }
    }

    function shouldDisplayNotingToDisplayMessage(day: Date) {
        if (timeSlots.length > 0) {
            return false;
        }

        let isBookings = timeSlots.some(timeSlot => typeof timeSlot?.booked !== "undefined");
        if (isBookings) {
            return $hasActiveFilters;
        } else {
            return !isPast(endOfDay(day)) && $hasActiveFilters;
        }
    }
</script>

<div class="daily-time-slots-container">
    {#if withHours}
        <div
            use:swipe={{ timeframe: 300, minSwipeDistance: 100, touchAction: 'pan-y' }}
            on:swipe={onSwipe}
            class="position-relative"
        >
            {#each Object.entries(timeSlotsPerHour) as [hour, timeSlots]}
                <div class="hour-container">
                <span class="hour"
                      data-hour={hour.replace('_', '').replace(':00', '')}>{hour.replace('_', '')}</span>
                    <div class="time-slots-container">
                        {#each timeSlots as timeSlot}
                            {#if timeSlot.booked}
                                <ReservedTimeSlot {timeSlot}/>
                            {:else}
                                <AvailableTimeSlot {timeSlot}/>
                            {/if}
                        {/each}
                    </div>
                </div>
            {/each}
            {#if shouldDisplayNotingToDisplayMessage(day)}
                <div class="mobile-info-block-container">
                    <div class="mobile-info-block">
                        <NothingToDisplay class="m-5 text-center"/>
                    </div>
                </div>
            {/if}
        </div>
    {:else}
        {#if shouldDisplayNotingToDisplayMessage(day)}
            <NothingToDisplay/>
        {:else}
            {#each timeSlots as timeSlot}
                {#if timeSlot.booked}
                    <ReservedTimeSlot {timeSlot}/>
                {:else}
                    <AvailableTimeSlot {timeSlot}/>
                {/if}
            {/each}
        {/if}
    {/if}
</div>

<style>
    .hour-container {
        display: flex;
    }
    .hour {
        color: #A4A4A4;
        margin-right: 8px;
        font-size: 12px;
        font-weight: 500;
        line-height: normal;
    }
    .time-slots-container {
        margin-top: 7px;
        border-top: 1px solid #A4A4A4;
        width: 100%;
        min-height: 56px;
        padding-top: 8px;
    }

    .mobile-info-block-container {
        position: absolute;
        top: 0;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-top: 50px;
        padding-left: 30px;
    }

    .mobile-info-block {
        width: 75%;
        background-color: white;
        display: flex;
        justify-content: center;
        align-items: center;
    }
</style>
