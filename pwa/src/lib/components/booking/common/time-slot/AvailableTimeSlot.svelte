<script lang="ts">
    import TimeSlot from "$lib/components/booking/common/time-slot/TimeSlot.svelte";
    import type {TimeSlot_jsonld_read} from "$lib/zodios.js";
    import AvailableTimeSlotModal from "$lib/components/modal/time-slot/AvailableTimeSlotModal.svelte";

    export let timeSlot: TimeSlot_jsonld_read;

    const showAvailableTimeSlotModal = () => {
        window.dispatchEvent(new CustomEvent('create-modal', {
            detail: {
                component: AvailableTimeSlotModal,
                props: {
                    timeSlot: timeSlot
                }
            }
        }));
    };
</script>

<TimeSlot
    name={timeSlot.bookable.name}
    startDate={timeSlot.startDate}
    endDate={timeSlot.endDate}
    color={timeSlot.bookable.color}
    on:click={showAvailableTimeSlotModal}
/>
