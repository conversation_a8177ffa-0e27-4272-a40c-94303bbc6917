<script lang="ts">
    import {startOfMonth, endOfMonth, addMonths, getDay, addDays, eachDayOfInterval} from 'date-fns';
    import CalendarFilterBody from "$lib/components/booking/common/CalendarFilterBody.svelte";
    import {monthViewDate} from "$lib/stores";
    import format from "$lib/date-fns-i18n";

    export let weekNumber: number;
    export let weekYear: number;

    // numero du jour du 1er jour du mois
    function getDayNumberOfFirstDayOfMonth(date: Date): number {
        let dayOfWeek: number = getDay(date) - 1;
        return dayOfWeek < 0 ? 7 : (dayOfWeek+1);
    }

    // numero du jour du dernier jour du mois
    function getDayNumberOfLastDayOfMonth(date: Date): number {
        let dayOfWeek: number = getDay(date) - 1;
        return dayOfWeek < 0 ? 7 : (dayOfWeek+1);
    }

    function chunkArray (arr: any[], size: number): any[] {
        return arr.length > size
            ? [arr.slice(0, size), ...chunkArray(arr.slice(size), size)]
            : [arr]
    }

    function setCalendar(monthViewDate: Date) {
        let startOfMonthDate: Date = startOfMonth(monthViewDate);
        let dayNumberOfFirstDayOfMonth: number = getDayNumberOfFirstDayOfMonth(startOfMonthDate);
        let endOfPrevMonthDate: Date = endOfMonth(addMonths(monthViewDate, -1));

        // 1er jour du mois affiché
        let firstDayOfCurrentMonth: Date = startOfMonthDate;
        if(dayNumberOfFirstDayOfMonth > 1) {
            let diffDays: number = -(dayNumberOfFirstDayOfMonth-2);
            firstDayOfCurrentMonth = addDays(endOfPrevMonthDate, diffDays);
        }

        let endOfMonthDate: Date = endOfMonth(monthViewDate);
        let dayNumberOfLastDayOfMonth: number = getDayNumberOfLastDayOfMonth(endOfMonthDate);
        let lastDayOfCurrentMonth: Date = endOfMonthDate;
        if(dayNumberOfLastDayOfMonth !== 7) {
            let diffDays: number = 7 - dayNumberOfLastDayOfMonth;
            lastDayOfCurrentMonth = addDays(endOfMonthDate, diffDays);
        }

        // semaines du mois à afficher
        let currentMonthView = eachDayOfInterval({
            start: firstDayOfCurrentMonth,
            end: lastDayOfCurrentMonth
        })
        return chunkArray(currentMonthView, 7);
    }
    let currentMonthViewArray: any[] = [];
    $: currentMonthViewArray = setCalendar($monthViewDate);

    function toPrev() {
        $monthViewDate = addMonths($monthViewDate, -1);
        currentMonthViewArray = setCalendar($monthViewDate);
    }

    function toNext() {
        $monthViewDate = addMonths($monthViewDate, +1);
        currentMonthViewArray = setCalendar($monthViewDate);
    }
</script>

<div id="calendar-filter">
    <h5>Semaines</h5>
    <div class="calendar-filter">
        <div class="calendar-head d-flex justify-content-center pb-4">
            <button class="switch-month" on:click={toPrev}>
                <i class="fa fa-chevron-left"></i>
            </button>
            <div class="calendar-month w-75">
                {format($monthViewDate, 'MMMM yyyy')}
            </div>
            <button class="switch-month" on:click={toNext}>
                <i class="fa fa-chevron-right"></i>
            </button>
        </div>

        <CalendarFilterBody bind:currentMonthViewArray bind:weekNumber bind:weekYear />

    </div>
</div>

<style>
    h5 {
        margin-top: 20px;
        margin-bottom: 20px;
    }
    .switch-month {
        background: transparent;
        border: none;
        color: var(--bs-black);
    }
    .calendar-head {
        font-size: 20px;
        font-weight: 600;
    }
    .calendar-month {
        color: var(--bs-gray-600);
        text-transform: capitalize;
    }
    .calendar-filter {
        font-size: 18px;
        text-align: center;
        font-weight: 500;
    }
</style>
