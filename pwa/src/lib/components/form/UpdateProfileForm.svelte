<script lang="ts">
    import {User_update as Schema, type User_update as SchemaType, type User_jsonld_read as UserType} from "$lib/zodios";
    import {profile, zodiosClient} from "$lib/stores";
    import {createZodiosForm, ServerError} from "$lib/form";
    import ModalInputGroup from "$lib/components/form/elements/ModalInputGroup.svelte";
    import Label from "$lib/components/form/elements/Label.svelte";
    import Input from "$lib/components/form/elements/Input.svelte";
    import {createEventDispatcher} from "svelte";
    import Select from "$lib/components/form/elements/Select.svelte";
    import Switch from "$lib/components/form/elements/Switch.svelte";

    export let isValid = false;
    export const submit = () => {
        form.submit();
    };

    const dispatch = createEventDispatcher();

    const submitFormData: (data: UserType) => any = async data => {
        const response = await $zodiosClient.updateProfile(data);
        dispatch('userUpdated', response);
        window.dispatchEvent(new CustomEvent('userUpdated', {detail: response}));
        return response;
    };

    const form = createZodiosForm({
        id: 'update-profile',
        schema: Schema,
        onSubmitted: submitFormData,
        initialData: {
            firstname: $profile?.firstname,
            lastname: $profile?.lastname,
            department: $profile?.department,
            preferences: $profile?.preferences,
        },
    });
    const {form: formData, errors, validateForm, enhance, message} = form;

    let field = form.options.validators?.jsonSchema?.properties?.department;
    let departments: string[] = field?.anyOf?.[0]?.enum ?? [];
    let departmentsOptions: { value: string, label: string }[] = [];
    departments.forEach(department => {
        departmentsOptions.push({value: department, label: department});
    });

    $: {
        if ($formData) {
            validateForm({update: false}).then(result => isValid = result.valid);
        }
    }
</script>

<form method="POST" use:enhance class="needs-validation container modal-form">
    {#if $message}
        <div class="row text-center mb-3">
            <span class="text-danger fw-bold">{@html $message}</span>
        </div>
    {/if}

    <ModalInputGroup {form} field="firstname">
        <Label slot="label" class="col-2 col-form-label col-form-label-sm">
            Prénom
        </Label>
        <Input slot="input" type="text" />
    </ModalInputGroup>

    <ModalInputGroup {form} field="lastname">
        <Label slot="label" class="col-2 col-form-label col-form-label-sm">
            Nom
        </Label>
        <Input slot="input" type="text" />
    </ModalInputGroup>

    <ModalInputGroup {form} field="department">
        <Label slot="label" class="col-2 col-form-label col-form-label-sm">
            Service
        </Label>
        <Select slot="input" options={departmentsOptions} />
    </ModalInputGroup>

    <div class="row mt-4">
        <ModalInputGroup {form} field="preferences.homePage">
            <Label slot="label" class="col-auto col-form-label col-form-label-sm">
                Vue préférée
            </Label>
            <Switch slot="input"
                    options={[{value: 'available-rooms', label: 'Disponibles'}, {value: 'reserved-rooms', label: 'Réservées'}]}/>
        </ModalInputGroup>
        <small class="help-text">Lors de l'ouverture de MymeetingRoom choisissez si vous préférez voir les salles
            disponibles ou les salles réservées.</small>
    </div>
</form>

<style>
    .help-text {
        font-size: 0.8rem;
        font-style: italic;
    }
</style>
