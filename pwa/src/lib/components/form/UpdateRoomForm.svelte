<script lang="ts">
    import {
        type Bookable_read_room as RoomType,
    } from "$lib/zodios";
    import {zodiosClient} from "$lib/stores";
    import {createZodiosForm, ServerError} from "$lib/form";
    import ModalInputGroup from "$lib/components/form/elements/ModalInputGroup.svelte";
    import Label from "$lib/components/form/elements/Label.svelte";
    import Input from "$lib/components/form/elements/Input.svelte";
    import {createEventDispatcher} from "svelte";
    import {z} from "zod";
    import InputFile from "$lib/components/form/elements/InputFile.svelte";
    import InputNumber from "$lib/components/form/elements/InputNumber.svelte";

    export let isValid = false;
    export const submit = () => {
        form.submit();
    };
    export let onRoomUpdated: (response: RoomType) => void = response => {};
    export let room: RoomType;

    const dispatch = createEventDispatcher();

    const submitProfilePicture = async (file: File) => {
        try {
            return await $zodiosClient.bookablePictureChange({file: file}, {params: {id: room.id}});
        } catch (e) {
            console.error(e);
        }
    };

    const submitFormData: (data: RoomType) => any = async data => {
        if (data.picture) {
            await submitProfilePicture(data.picture);
            delete data.picture;
        }
        const response = await $zodiosClient.roomUpdate(data, {params: {id: data.id}});
        onRoomUpdated(response);
        return response;
    };

    const MAX_UPLOAD_SIZE = 1024 * 1024 * 3; // 3MB
    const ACCEPTED_FILE_TYPES = ['image/png', 'image/jpeg'];

    // TODO : replacer par le schema de Bookable_update_room quand il sera développé
    const RoomSchema = z.object({});

    const Schema = RoomSchema.extend({
        picture: z
            .instanceof(File)
            .optional()
            .refine(file => {
                return !file || file.size <= MAX_UPLOAD_SIZE;
            }, 'File size must be less than 3MB')
            .refine(file => {
                return !file || ACCEPTED_FILE_TYPES.includes(file?.type);
            }, 'File must be a PNG')
    });

    const form = createZodiosForm({
        id: `update-room-${room.id}`,
        schema: Schema,
        onSubmitted: submitFormData,
        initialData: {
            ...room,
        },
    });
    const {form: formData, errors, validateForm, enhance, message} = form;

    $: {
        if ($formData) {
            validateForm({update: false}).then(result => isValid = result.valid);
        }
    }


</script>

<form method="POST" use:enhance class="needs-validation container modal-form">
    {#if $message}
        <div class="row text-center mb-3">
            <span class="text-danger fw-bold">{@html $message}</span>
        </div>
    {/if}

    <ModalInputGroup {form} field="name">
        <Label slot="label" class="col-auto col-form-label col-form-label-sm">
            Nom
        </Label>
        <Input slot="input" type="text" />
    </ModalInputGroup>

    <ModalInputGroup {form} field="color">
        <Label slot="label" class="col-auto col-form-label col-form-label-sm">
            Couleur
        </Label>
        <Input slot="input" type="color" />
    </ModalInputGroup>

    <ModalInputGroup {form} field="capacity">
        <Label slot="label" class="col-auto col-form-label col-form-label-sm">
            Capacité
        </Label>
        <InputNumber slot="input" type="number"/>
    </ModalInputGroup>

    <ModalInputGroup {form} field="picture">
        <Label slot="label" class="col-auto col-form-label col-form-label-sm">
            Photo
        </Label>
        <InputFile slot="input"/>
    </ModalInputGroup>
</form>
