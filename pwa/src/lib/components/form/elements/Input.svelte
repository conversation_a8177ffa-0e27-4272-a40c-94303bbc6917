<script lang="ts" context="module">
    type T = Record<string, unknown>;
</script>

<script lang="ts" generics="T extends Record<string, unknown>">
    import {formFieldProxy, type FormPathLeaves, type SuperForm} from "sveltekit-superforms";
    import {getContext} from 'svelte';

    export let form: SuperForm<T> | null = null;
    export let field: FormPathLeaves<T> | null = null;
    export let type: string;
    export let label: string | null = null;
    export let showLabel: boolean | null = null;
    export let beforeInputIcone: string | null = null;

    const context = getContext<{form: SuperForm<T>, field: FormPathLeaves<T>, label: string, showLabel: boolean}>('inputGroupChild');
    if (!form || !field || !label || !showLabel) {
        form = context.form;
        field = context.field;
        label = context.label;
        showLabel = context.showLabel;
    }

    const { value, constraints, errors } = formFieldProxy(form, field);
</script>
{#if beforeInputIcone}
<span class="input-group-text rounded-0 border-0 bg-transparent">
    <i class="{beforeInputIcone}"></i>
</span>
{/if}
<input aria-label={showLabel ? null : label} {...{ type }} bind:value={$value} {...$constraints} class="form-control rounded-start" class:is-invalid={$errors && $errors?.length > 0} {...$$restProps}/>
