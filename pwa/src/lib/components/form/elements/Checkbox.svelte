<script lang="ts" context="module">
    type T = Record<string, unknown>;
</script>

<script lang="ts" generics="T extends Record<string, unknown>">
    import {formFieldProxy, type FormPathLeaves, type SuperForm} from "sveltekit-superforms";
    import {getContext} from 'svelte';

    export let form: SuperForm<T> | null = null;
    export let field: FormPathLeaves<T> | null = null;
    export let label: string = '';

    const context = getContext<{form: SuperForm<T>, field: FormPathLeaves<T>, label: string, showLabel: boolean}>('inputGroupChild');
    if (!form || !field) {
        form = context.form;
        field = context.field;
    }

    const { value, constraints, errors } = formFieldProxy(form, field);
</script>
<div class="form-check">
    <input class="form-check-input" type="checkbox" bind:checked={$value} id="flexCheckDefault">
    <label class="form-check-label col-form-label-sm p-0" for="flexCheckDefault">
        {label}
    </label>
</div>
