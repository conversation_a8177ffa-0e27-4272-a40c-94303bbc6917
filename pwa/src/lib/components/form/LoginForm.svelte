<script lang="ts">
    import { zodiosClient } from '$lib/stores';
    import {auth_Body, type auth_Body as LoginType, Authentication_Signup_jsonld} from "$lib/zodios";
    import InputGroup from "$lib/components/form/elements/InputGroup.svelte";
    import Input from "$lib/components/form/elements/Input.svelte";
    import Password from "$lib/components/form/elements/Password.svelte";
    import {createZodiosForm, ServerError} from "$lib/form";
    import {login} from "$lib/auth";
    import {AxiosError} from "axios";

    const loginFromData: (data: LoginType) => any = async data => {
        try {
            await login(data)
        } catch (e) {
            if (e instanceof AxiosError && e?.response?.status === 401) {
                throw new ServerError('E-mail ou mot de passe invalide');
            }

            throw e;
        }
    };

    let response: any = null;
    let schema = auth_Body;
    const form = createZodiosForm({
        id: 'login',
        schema: schema,
        onSubmitted: loginFromData,
        options: {
            async onUpdated() {
                if (response) {
                    console.log('OK', response);
                }
            }
        },
        initialData: {
            email: "",
            password: "",
        },
    })

    const {form: formData, errors, validateForm, enhance, message} = form;

</script>

<form method="POST" use:enhance class="w-100 container g-0 my-3">
    {#if $message}
        <div class="row text-center">
            <span class="text-danger fw-bold">{@html $message}</span>
        </div>
    {/if}
    <div class="row mt-3">
        <InputGroup {form} field="email" label="Identifiant" showLabel={false}>
            <Input type="email" tabindex="3" beforeInputIcone="fal fa-user" placeholder="Identifiant" autofocus />
        </InputGroup>
    </div>
    <div class="row mt-2">
        <InputGroup {form} field="password" label="Mot de passe" showLabel={false}>
            <Password tabindex="5" placeholder="Mot de passe" beforeInputIcone="fal fa-lock" />
        </InputGroup>
    </div>
    <div class="text-end">
        <small><a href="/reset-password">Mot de passe oublié</a></small>
    </div>
    <button class="btn btn-primary w-100 mt-3">Se connecter</button>
</form>

<style>
    small {
        font-size: 0.75rem;
    }
</style>
