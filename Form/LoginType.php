<?php

namespace Alienor\AquitemWebServiceParserBundle\Form;

use Symfony\Component\Form\FormBuilderInterface;
use Alienor\AquitemWebServiceParserBundle\Form\BaseType;

use Symfony\Component\Form\CallbackValidator;
use Symfony\Component\Form\FormError;
use Symfony\Component\Validator\Constraints\NotBlank;

class LoginType extends BaseType {

	public function buildForm(FormBuilderInterface $builder, array $options)
	{
		$builder
			->add('login', 'Symfony\Component\Form\Extension\Core\Type\TextType', array(
					'required' => true,
					'attr' => array(
						'placeholder' => 'login.identifiant',
					),
					'constraints' => array(
           				new NotBlank(),
       			)
			))
			->add('password', 'Symfony\Component\Form\Extension\Core\Type\PasswordType', array(
					'required' => true,
					'attr' => array(
						'placeholder' => 'login.motDePasse',
					),
					'constraints' => array(
           				new NotBlank(),
       			)
			));
	}

	public function getName()
	{
		return 'login';
	}
	public function getBlockPrefix()
	{
		return 'login';
	}
}
