<?php

namespace Alienor\AquitemWebServiceParserBundle\Form\Field;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\Extension\Core\DataTransformer\BooleanToStringTransformer;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolverInterface;


class MultiRadiosType extends AbstractType {

	/**
	 * {@inheritdoc}
	 */
	public function buildForm(FormBuilderInterface $builder, array $options)
	{
		foreach ($options['radioList']['labels'] as $k => $label) {
			$builder->add($k, 'Symfony\Component\Form\Extension\Core\Type\ChoiceType', array(
				'choices_as_values' => true,
				'label' => $this->cleanLabelKey($label),
				'multiple'          => false,
				'expanded'          => true,
				'choices' => $options['choices'],
				'attr' => $options['attr'],
			));
		}
	}

	/**
	 * {@inheritdoc}
	 */
	public function buildView(FormView $view, FormInterface $form, array $options)
	{
	}

	/**
	 * {@inheritdoc}
	 */
	public function setDefaultOptions(OptionsResolverInterface $resolver)
	{
		$emptyData = function (FormInterface $form, $viewData) {
			return $viewData;
		};

		$resolver->setDefaults(array(
			'value'      => '1',
			'empty_data' => $emptyData,
			'compound'   => true,
			'radioList' => array(),
			'choices' => array(
				'multiradios.label.0',
				'multiradios.label.1'
			),
		));
	}

	/**
	 * Au moment de construire la vue, le label des champs est généré à l'aide du
	 * nom du formulaire concaténé au nom du champs
	 *
	 * (ex: utilisateur.login)
	 */
	public function finishView(FormView $view, FormInterface $form, array $options)
	{
		$children = $view->children;
		foreach ($children as $key => $child) {
			if (strpos($child->vars['label'], '.') === false && $child->vars['label'] === NULL) {
				$label = $form->getParent()->getName().'.'.$form->getName().'_'.$child->vars['name'];
				$child->vars['label'] = $label;
			}
			elseif (strpos($child->vars['label'], '.') === false) {
				$label = $form->getParent()->getName().'.'.$form->getName().'.'.$child->vars['label'];
				$child->vars['label'] = $label;
			}
		}
	}

	public function getName()
	{
		return 'multiradio';
	}

	protected function cleanLabelKey($label) {
		$label = ucwords(strtolower($label));
		$label = lcfirst(str_replace(" ", "", $label));
		return $label;
	}

}