<?php

namespace Alienor\TwigExtensionBundle\Twig;

class AlienorExtension extends \Twig_Extension
{
    protected $locale;

    private $locales = array(
        'fr' => 'fr_FR.UTF-8',
        'en' => 'en_US.UTF-8',
        'es' => 'es_ES.UTF-8',
    );

    public function __construct($container) {
		$this->container = $container;
    }

	public function getFunctions() {
		return array(

			'get_controller_name' => new \Twig_SimpleFunction('controllerNameFunction', array($this, 'controllerNameFunction')),
			'get_action_name' => new \Twig_SimpleFunction('actionNameFunction', array($this, 'actionNameFunction')),
		);
	}

    public function getFilters()
    {
        return array(
            new \Twig_SimpleFilter('unserialize', array($this, 'unserializeFilter')),
            new \Twig_SimpleFilter('base64encode', array($this, 'base64EncodeFilter')),
            new \Twig_SimpleFilter('base64decode', array($this, 'base64EncodeFilter')),
            new \Twig_SimpleFilter('md5', array($this, 'md5Filter')),
            new \Twig_SimpleFilter('sha1', array($this, 'sha1Filter')),
            new \Twig_SimpleFilter('strftime', array($this, 'strftimeFilter')),
            new \Twig_SimpleFilter('tellink', array($this, 'telLinkFilter')),
            new \Twig_SimpleFilter('phone', array($this, 'phoneFormat')),
        );
    }

    public function phoneFormat($phone)
    {
        return chunk_split($this->telLinkFilter($phone),2,' ');
    }

    public function unserializeFilter(array $serialized)
    {
        return unserialize($serialized);
    }

    /**
     * $data Les données à encoder.
     *
     * @param type $data
     * @return type
     */
    public function base64EncodeFilter($data)
    {
        return base64_encode($data);
    }

    /**
     * $data Les données à décoder.
     * $strict Retourne FALSE si l'entrée contient des caractères hors de l'alphabet base64.
     *
     * @param type $data
     * @param type $strict
     * @return type
     */
    public function base64DecodeFilter($data, $strict = false)
    {
        return base64_decode($data, $strict);
    }

    /**
     * @param type $data
     * @return type
     */
    public function md5Filter($data)
    {
        return md5($data);
    }

    /**
     * @param type $data
     * @return type
     */
    public function sha1Filter($data)
    {
        return sha1($data);
    }

	public function getLocaleByRequest() {
		if ($this->locale === null) {
			$this->locale = $this->container->get('request')->get('_locale');
		}
	}

    /**
     * @param type $data
     * @return type
     */
    public function strftimeFilter($data, $format = '%d-%m-%Y')
    {
		$this->getLocaleByRequest();
		// switch de locale pour affichage de la date
		$lcall = setlocale(LC_ALL, 0);

		if (isset($this->locales[$this->locale])) {
			setlocale(LC_ALL, $this->locales[$this->locale]);
		}
        if ($data instanceof \DateTime) {
            $data = $data->format('U');
        }

        $ret = strftime($format, $data);
		setlocale(LC_ALL, $lcall);
		return $ret;
    }

	public function telLinkFilter($data) {
		return str_replace(array(' ', '.', '-'), ' ', $data);
	}

	/**
	 * Get current controller name
	 */
	public function controllerNameFunction() {
		$pattern = "#Controller\\\([a-zA-Z]*)Controller#";
		$matches = array();
		preg_match($pattern, $this->container->get('request')->get('_controller'), $matches);
		return strtolower($matches[1]);
	}

	/**
	 * Get current action name
	 */
	public function actionNameFunction()
	{
		$pattern = "#::([a-zA-Z]*)Action#";
		$matches = array();
		preg_match($pattern, $this->container->get('request')->get('_controller'), $matches);

		return $matches[1];
	}

    public function getName()
    {
        return 'alienor_extension';
    }

}