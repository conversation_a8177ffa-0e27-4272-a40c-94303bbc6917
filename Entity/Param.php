<?php

namespace Alienor\AquitemWebServiceParserBundle\Entity;

/**
 * Param
 */
class Param extends AbstractSimpleRelation {

    protected $valeur;

	/**
	 * Set valeur
	 *
	 * @param string $valeur
	 * @return \Alienor\AquitemWebServiceParserBundle\Entity\Param
	 */
	public function setValeur($valeur)
	{
		$this->valeur = $valeur;
		return $this;
	}

	/**
	 * Get valeur
	 *
	 * @return string
	 */
	public function getValeur()
	{
		return $this->valeur;
	}
}