const Encore = require('@symfony/webpack-encore');
var path = require('path');
const FosRouting = require('fos-router/webpack/FosRouting');

// Manually configure the runtime environment if not already configured yet by the "encore" command.
// It's useful when you use tools that rely on webpack.config.js file.
if (!Encore.isRuntimeEnvironmentConfigured()) {
    Encore.configureRuntimeEnvironment(process.env.NODE_ENV || 'dev');
}

Encore
    // directory where compiled assets will be stored
    .setOutputPath('public/build/')
    // public path used by the web server to access the output path
    .setPublicPath('/build')
    // only needed for CDN's or subdirectory deploy
    //.setManifestKeyPrefix('build/')

    /*
     * ENTRY CONFIG
     *
     * Each entry will result in one JavaScript file (e.g. app.js)
     * and one CSS file (e.g. app.css) if your JavaScript imports CSS.
     */
    //.addEntry('app', './assets/app.js')


	// Configuration pour traiter les fichiers JavaScript ES6/ES Modules
	.addRule({
		test: /\.m?js$/, // Inclure les fichiers ES6 ou ES Modules
		exclude: /node_modules\/(?!tom-select)/, // Exclure tout sauf tom-select
		use: {
			loader: 'babel-loader',
			options: {
				presets: [
					[
						'@babel/preset-env',
						{
							useBuiltIns: 'entry',
							corejs: 3,
						},
					],
				],
			},
		},
	})

	.addEntry('css', './assets/js/css.js')
	.addEntry('homepage-css', './assets/js/homepage-css.js')
	.addEntry('css-mosaico', './assets/js/css-mosaico.js')
	.addEntry('css-emojis', './assets/js/css-emojis.js')
	.addEntry('css-file-manager', './assets/js/css-file-manager.js')
	.addEntry('css-tom-select', './assets/js/css-tom-select.js')
	.addEntry('app', './assets/js/app.js')
	.addEntry('app-stripo', './assets/js/app-stripo.js')
	.addEntry('selection-common', './assets/js/selection-common.js')
	.addEntry('selection-process', './assets/js/selection-process.js')
	.addEntry('selection-liste', './assets/js/selection-liste.js')
	.addEntry('selection-exploration', './assets/js/selection-exploration.js')
	.addEntry('selections-hors-fid', './assets/js/selections-hors-fid.js')
	.addEntry('selection-quotas', './assets/js/selection-quotas.js')
	.addEntry('operation-common', './assets/js/operation-common.js')
	.addEntry('operation-liste', './assets/js/operation-liste.js')
	.addEntry('operation-selection', './assets/js/operation-selection.js')
	.addEntry('operation-sms', './assets/js/operation-sms.js')
	.addEntry('operation-courrier', './assets/js/operation-courrier.js')
	.addEntry('operation-budget', './assets/js/operation-budget.js')
	.addEntry('operation-perso-email-ckeditor', './assets/js/operation-perso-email-ckeditor.js')
	.addEntry('operation-perso-email-mosaico', './assets/js/operation-perso-email-mosaico.js')
	.addEntry('operation-perso-email-stripo', './assets/js/operation-perso-email-stripo.js')
	.addEntry('operation-perso-email-images', './assets/js/operation-perso-email-images.js')
	.addEntry('operation-perso-email-parametres', './assets/js/operation-perso-email-parametres.js')
	.addEntry('operation-perso-email-tags', './assets/js/operation-perso-email-tags.js')
	.addEntry('operation-perso-email-utm', './assets/js/operation-perso-email-utm.js')
	.addEntry('operation-perso-email-liens', './assets/js/operation-perso-email-liens.js')
	.addEntry('operation-perso-email-previsu', './assets/js/operation-perso-email-previsu.js')
	.addEntry('operation-modele-email-droits-tarifs', './assets/js/operation-modele-email-droits-tarifs.js')
	.addEntry('operation-modele-email-ckeditor', './assets/js/operation-modele-email-ckeditor.js')
	.addEntry('operation-modele-email-mosaico', './assets/js/operation-modele-email-mosaico.js')
	.addEntry('operation-modele-email-stripo', './assets/js/operation-modele-email-stripo')
	.addEntry('operation-modele-email-images', './assets/js/operation-modele-email-images.js')
	.addEntry('operation-modele-email-parametres', './assets/js/operation-modele-email-parametres.js')
	.addEntry('operation-modele-email-tags', './assets/js/operation-modele-email-tags.js')
	.addEntry('operation-modele-email-utm', './assets/js/operation-modele-email-utm.js')
	.addEntry('operation-modele-email-liens', './assets/js/operation-modele-email-liens.js')
	.addEntry('operation-modele-email-previsu', './assets/js/operation-modele-email-previsu.js')
	.addEntry('operation-planning', './assets/js/operation-planning.js')
	.addEntry('operation-offre', './assets/js/operation-offre.js')
	.addEntry('operation-options', './assets/js/operation-options.js')
	.addEntry('operation-selection-horsfid', './assets/js/operation-selection-horsfid.js')
	.addEntry('modele', './assets/js/modele.js')
	.addEntry('participation', './assets/js/participation.js')
	.addEntry('souscription', './assets/js/souscription.js')
	.addEntry('ckeditor', './assets/js/ckeditor.js')
	// .addEntry('ckeditor-images', './assets/js/ckeditor-images.js')
	.addEntry('operation-images', './assets/js/operation-images.js')
	.addEntry('ckeditor-images-pnr', './assets/js/ckeditor-images-pnr.js')
	.addEntry('ckeditor-images-mesoigner', './assets/js/ckeditor-images-mesoigner.js')
	// .addEntry('ckeditor-images-enseigne', './assets/js/ckeditor-images-enseigne.js')
	// .addEntry('stripo-images', './assets/js/stripo-images.js')
	.addEntry('stripo-images-pnr', './assets/js/stripo-images-pnr.js')
	.addEntry('stripo-images-mesoigner', './assets/js/stripo-images-mesoigner.js')
	// .addEntry('stripo-images-common', './assets/js/stripo-images-common.js')
	.addEntry('simuler-connexion', './assets/js/simuler-connexion.js')
	.addEntry('perso-sms-rich', './assets/js/perso-sms-rich.js')
	.addEntry('portail-enseigne', './assets/js/portail-enseigne.js')
	.addEntry('operation-notification', './assets/js/operation-notification.js')
	.addEntry('regles', './assets/js/regles.js')
	.addEntry('app-amabis', './assets/js/amabis/app-amabis.js')
	.addEntry('operation-images-css', './assets/js/operation-images-css.js')
	.addEntry('ckeditor-files', './assets/js/ckeditor-files.js')
	.addEntry('file-manager', './assets/js/file-manager.js')
	.addEntry('stripo-files', './assets/js/stripo-files.js')
	.addEntry('operation-modele-email-previsu-stripo', './assets/js/operation-modele-email-previsu-stripo.js')
	.addEntry('regle-selection', './assets/js/regle-selection.js')
	.addEntry('homepage', './assets/js/homepage.js')
	.addEntry('contacts-enseigne', './assets/js/contacts-enseigne.js')
	.addEntry('css-cartes-cadeaux', './assets/js/css-cartes-cadeaux.js')
	.addEntry('cartes-cadeaux', './assets/js/cartes-cadeaux.js')
	.addEntry('gallery', './assets/js/gallery.js')
	.addEntry('contacts-gallery', './assets/js/contacts-gallery.js')
	.addEntry('wallet', './assets/js/wallet.js')
	.addEntry('wallet-images', './assets/js/wallet-images.js')
	.addEntry('notification', './assets/js/notification.js')
	.addEntry('notification-images', './assets/js/notification-images.js')
	.addEntry('jobs', './assets/js/jobs.js')
	.addEntry('css-jobs', './assets/js/css-jobs.js')
	.addEntry('operation-wallet-liste', './assets/js/operation-wallet-liste.js')
	.addEntry('operation-wallet-notification', './assets/js/operation-wallet-notification.js')
	.addEntry('operation-wallet-selection', './assets/js/operation-wallet-selection.js')
	.addEntry('operation-wallet', './assets/js/operation-wallet.js')
	.addEntry('operation-wallet-budget', './assets/js/operation-wallet-budget.js')
	.addEntry('operation-wallet-planning', './assets/js/operation-wallet-planning.js')
	.addEntry('operation-wallet-options', './assets/js/operation-wallet-options.js')
	.addEntry('points-de-vente', './assets/js/points-de-vente.js')
	.addEntry('operation-audio', './assets/js/operation-audio.js')

    // When enabled, Webpack "splits" your files into smaller pieces for greater optimization.
    .splitEntryChunks()

    // will require an extra script tag for runtime.js
    // but, you probably want this, unless you're building a single-page app
    .enableSingleRuntimeChunk()

    /*
     * FEATURE CONFIG
     *
     * Enable & configure other features below. For a full
     * list of features, see:
     * https://symfony.com/doc/current/frontend.html#adding-more-features
     */
    .cleanupOutputBeforeBuild()
    .enableBuildNotifications()
    .enableSourceMaps(!Encore.isProduction())
    // enables hashed filenames (e.g. app.abc123.css)
    //.enableVersioning(Encore.isProduction())
	.enableVersioning()

    // configure Babel
    // .configureBabel((config) => {
    //     config.plugins.push('@babel/a-babel-plugin');
    // })

    // enables and configure @babel/preset-env polyfills
    .configureBabelPresetEnv((config) => {
        config.useBuiltIns = 'usage';
        config.corejs = '3.23';
    })

    // enables Sass/SCSS support
    .enableSassLoader()

	.enableLessLoader()

    // uncomment if you use TypeScript
    //.enableTypeScriptLoader()

    // uncomment if you use React
    //.enableReactPreset()

    // uncomment to get integrity="..." attributes on your script & link tags
    // requires WebpackEncoreBundle 1.4 or higher
    //.enableIntegrityHashes(Encore.isProduction())

    // uncomment if you're having problems with a jQuery plugin
    .autoProvidejQuery()

	.autoProvideVariables({
		$: 'jquery',
		jQuery: 'jquery',
		'window.jQuery': 'jquery',
		'window.jQuery.ui': 'jquery-ui',
	})

    .addPlugin(new FosRouting())

	.copyFiles({
		from: './assets/img',
		to: 'img/[path][name].[hash:8].[ext]'
	})
;
let config = Encore.getWebpackConfig();

config.resolve = {
	extensions: ['.js'],
	alias: {
		'load-image': 'blueimp-load-image/js/load-image.js',
		'load-image-meta': 'blueimp-load-image/js/load-image-meta.js',
		'load-image-exif': 'blueimp-load-image/js/load-image-exif.js',
		'load-image-scale': 'blueimp-load-image/js/load-image-scale.js',
		'load-image-orientation': 'blueimp-load-image/js/load-image-orientation.js',
		'canvas-to-blob': 'blueimp-canvas-to-blob/js/canvas-to-blob.js',
		'jquery-ui/widget': 'jquery-ui/ui/widget.js',
		'jquery': path.join(__dirname, 'node_modules/jquery/src/jquery'),
		'jquery.validate': path.join(__dirname, 'public/js/vendor/jquery.validate.min'),
	},
	fallback: {
		fs: false,
	}
};

config.output.globalObject = 'this';
module.exports = config;
