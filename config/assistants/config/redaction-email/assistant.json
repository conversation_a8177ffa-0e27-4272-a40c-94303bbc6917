{"id": "redaction-email", "title": "Rédaction d'email", "description": "Assistant d'aide à la rédaction d'un email", "modelSelectionConfiguration": {"allowedCategories": ["normal"], "allowedLocations": ["internal"], "defaultModel": "gpt-4o"}, "pages": [{"id": "page-text-input", "title": "<PERSON><PERSON> du texte", "components": [{"id": "meeting-text-input", "component": "RichTextarea", "props": {"id": "meeting-text", "label": "Vos notes", "placeholder": "Écrivez vos notes ici...", "rows": 10, "required": true, "autofocus": true}, "bind": "meeting-text"}, {"id": "model-selector", "component": "ModelSelector", "props": {"id": "model", "label": "<PERSON><PERSON><PERSON><PERSON>", "required": true}, "bind": "model"}, {"id": "typology-selector", "component": "Select", "props": {"id": "typology", "label": "Format de réponse", "options": [{"value": "internal", "label": "Interne"}, {"value": "external", "label": "Externe"}], "defaultValue": "internal"}, "bind": "typology"}], "actions": [{"id": "back-button", "component": "<PERSON><PERSON>", "props": {"label": "Retour", "variant": "secondary"}, "action": {"type": "navigate", "target": "page-input-choice"}}, {"id": "submit-button", "component": "<PERSON><PERSON>", "props": {"label": "Proposer un email", "variant": "primary"}, "action": {"type": "submit", "apiCallId": "generate-email", "target": "page-result"}}], "apiCalls": [{"id": "generate-email", "endpoint": "invokeStream", "method": "POST", "requestMapping": {"content": ["meeting-text"], "typology": "typology", "modelId": "model"}, "responseMapping": {"report": "generated-email"}}]}, {"id": "page-result", "title": "<PERSON><PERSON>", "components": [{"id": "report-display", "component": "RichTextarea", "props": {"id": "generated-email", "label": "Email", "readonly": true, "rows": 15}, "bind": "generated-email"}], "actions": [{"id": "back-button", "component": "<PERSON><PERSON>", "props": {"label": "Retour", "variant": "secondary"}, "action": {"type": "navigate", "target": "dynamic-previous"}}, {"id": "copy-action", "component": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "icon": "fa fa-clipboard", "variant": "primary"}, "action": {"type": "copy", "target": "generated-email", "successMessage": "Copié dans le presse-papier !"}}]}]}