{"id": "compte-rendu", "title": "Générateur de compte-rendu de réunion", "description": "Assistant pour générer un compte-rendu structuré à partir de texte ou d'audio", "modelSelectionConfiguration": {"allowedCategories": ["normal"], "allowedLocations": ["internal"], "defaultModel": "gpt-4o"}, "pages": [{"id": "page-input-choice", "title": "Source du compte-rendu", "components": [{"id": "source-selection-prompt", "component": "Text", "props": {"content": "Choisissez votre source pour générer le compte-rendu :", "variant": "heading", "size": "medium"}}, {"id": "source-selection-buttons", "component": "ButtonGroup", "props": {"orientation": "horizontal", "gap": "medium", "alignment": "center"}, "children": [{"id": "text-source-button", "component": "<PERSON><PERSON>", "props": {"label": "Texte", "icon": "fa fa-file-text", "variant": "primary", "size": "large"}, "action": {"type": "navigate", "target": "page-text-input", "setValues": {"input-type": "text"}}}, {"id": "audio-source-button", "component": "<PERSON><PERSON>", "props": {"label": "Fichier audio", "icon": "fa fa-microphone", "variant": "primary", "size": "large"}, "action": {"type": "navigate", "target": "page-audio-upload", "setValues": {"input-type": "audio"}}}]}]}, {"id": "page-text-input", "title": "<PERSON><PERSON> du texte", "components": [{"id": "meeting-text-input", "component": "RichTextarea", "props": {"id": "meeting-text", "label": "Contenu de la réunion", "placeholder": "<PERSON><PERSON>z ici les notes ou la transcription de votre réunion...", "rows": 10, "required": true, "autofocus": true}, "bind": "meeting-text"}, {"id": "model-selector", "component": "ModelSelector", "props": {"id": "model", "label": "<PERSON><PERSON><PERSON><PERSON>", "required": true}, "bind": "model"}, {"id": "format-selector", "component": "Select", "props": {"id": "format", "label": "Format de réponse", "options": [{"value": "document", "label": "Document"}, {"value": "email", "label": "Email"}], "defaultValue": "document"}, "bind": "format"}, {"id": "length-selector", "component": "Select", "props": {"id": "length", "label": "Longueur de réponse", "options": [{"value": "synthetic", "label": "Synthétique"}, {"value": "detailed", "label": "Détaillée"}], "defaultValue": "synthetic"}, "bind": "length"}], "actions": [{"id": "back-button", "component": "<PERSON><PERSON>", "props": {"label": "Retour", "variant": "secondary"}, "action": {"type": "navigate", "target": "page-input-choice"}}, {"id": "submit-button", "component": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON><PERSON> le compte-rendu", "variant": "primary"}, "action": {"type": "submit", "apiCallId": "generate-cr", "target": "page-result"}}], "apiCalls": [{"id": "generate-cr", "endpoint": "invokeStream", "method": "POST", "requestMapping": {"inputType": "input-type", "content": ["meeting-text", "audio-file"], "format": "format", "length": "length", "modelId": "model"}, "responseMapping": {"report": "generated-report"}}]}, {"id": "page-audio-upload", "title": "Dépôt du fichier audio", "components": [{"id": "audio-file-uploader", "component": "FileUpload", "props": {"id": "audio-file", "label": "Fichier audio de la réunion", "accept": "audio/*", "required": true, "maxSize": 50000000, "dropzoneText": "Déposez votre fichier audio ici ou cliquez pour parcourir"}, "bind": "audio-file"}], "actions": [{"id": "back-button", "component": "<PERSON><PERSON>", "props": {"label": "Retour", "variant": "secondary"}, "action": {"type": "navigate", "target": "page-input-choice"}}, {"id": "submit-button", "component": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON><PERSON> le compte-rendu", "variant": "primary"}, "action": {"type": "submit", "apiCallId": "generate-audio-cr", "target": "page-result"}}], "apiCalls": [{"id": "generate-audio-cr", "endpoint": "transcript", "method": "POST", "requestMapping": {"inputType": "input-type", "publicUri": ["meeting-text", "audio-file"]}, "responseMapping": {"text": "generated-report"}}]}, {"id": "page-result", "title": "Compt<PERSON><PERSON><PERSON><PERSON><PERSON>", "components": [{"id": "report-display", "component": "RichTextarea", "props": {"id": "generated-report", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readonly": true, "rows": 15}, "bind": "generated-report"}], "actions": [{"id": "back-button", "component": "<PERSON><PERSON>", "props": {"label": "Retour", "variant": "secondary"}, "action": {"type": "navigate", "target": "dynamic-previous"}}, {"id": "copy-action", "component": "<PERSON><PERSON>", "props": {"label": "<PERSON><PERSON><PERSON>", "icon": "fa fa-clipboard", "variant": "primary"}, "action": {"type": "copy", "target": "generated-report", "successMessage": "Copié dans le presse-papier !"}}, {"id": "export-action", "component": "<PERSON><PERSON>", "props": {"label": "Exporter", "icon": "fa fa-download", "variant": "secondary"}, "action": {"type": "export", "target": "generated-report", "options": {"filename": "compte-rendu-reunion.txt", "format": "text"}}}]}]}