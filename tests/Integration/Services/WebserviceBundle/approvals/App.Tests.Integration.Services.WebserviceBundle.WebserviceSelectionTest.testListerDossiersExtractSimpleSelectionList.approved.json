[{"id": "509769", "libelle": "100 ans", "description": "", "dateCalcul": "2024-07-24 11:32:08", "tempsCalcul": "0", "tailleCalcul": "2", "selectionAvancee": "false", "quotasActifs": "false", "selectionAcompleter": "true", "selectionHorsFid": "false", "selectionExterne": "false", "fichierIdsValide": "true", "tailleApproximative": "", "archivee": "false", "archivable": "true", "modifiable": "false", "supprimable": "false"}, {"id": "515607", "libelle": "100000 ans", "description": "", "dateCalcul": "2024-07-24 11:32:08", "tempsCalcul": "0", "tailleCalcul": "2", "selectionAvancee": "false", "quotasActifs": "false", "selectionAcompleter": "true", "selectionHorsFid": "false", "selectionExterne": "false", "fichierIdsValide": "true", "tailleApproximative": "", "archivee": "false", "archivable": "false", "modifiable": "true", "supprimable": "true"}, {"id": "502657", "libelle": "4444 copie copie", "description": "", "dateCalcul": "2019-04-03 14:55:36", "tempsCalcul": "0", "tailleCalcul": "501", "selectionAvancee": "false", "quotasActifs": "false", "selectionAcompleter": "true", "selectionHorsFid": "false", "selectionExterne": "false", "fichierIdsValide": "true", "tailleApproximative": "", "archivee": "false", "archivable": "false", "modifiable": "true", "supprimable": "true"}, {"id": "500823", "libelle": "oop", "description": "Ceci est ma description", "dateCalcul": "2017-03-03 08:39:57", "tempsCalcul": "0", "tailleCalcul": "25380", "selectionAvancee": "false", "quotasActifs": "false", "selectionAcompleter": "true", "selectionHorsFid": "false", "selectionExterne": "false", "fichierIdsValide": "true", "tailleApproximative": "", "archivee": "false", "archivable": "true", "modifiable": "false", "supprimable": "false"}, {"id": "507675", "libelle": "OPU", "description": "", "dateCalcul": "2018-12-12 16:07:42", "tempsCalcul": "0", "tailleCalcul": "0", "selectionAvancee": "false", "quotasActifs": "false", "selectionAcompleter": "true", "selectionHorsFid": "false", "selectionExterne": "false", "fichierIdsValide": "true", "tailleApproximative": "", "archivee": "false", "archivable": "false", "modifiable": "true", "supprimable": "true"}, {"id": "509533", "libelle": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ceci est la description de mon opération", "dateCalcul": "2019-10-01 09:43:16", "tempsCalcul": "0", "tailleCalcul": "176", "selectionAvancee": "false", "quotasActifs": "false", "selectionAcompleter": "true", "selectionHorsFid": "false", "selectionExterne": "false", "fichierIdsValide": "true", "tailleApproximative": "", "archivee": "false", "archivable": "false", "modifiable": "true", "supprimable": "true"}, {"id": "502175", "libelle": "0123", "description": "", "dateCalcul": "2018-01-31 16:54:41", "tempsCalcul": "0", "tailleCalcul": "17", "selectionAvancee": "false", "quotasActifs": "false", "selectionAcompleter": "true", "selectionHorsFid": "false", "selectionExterne": "false", "fichierIdsValide": "true", "tailleApproximative": "", "archivee": "false", "archivable": "false", "modifiable": "true", "supprimable": "true"}]