<?php

namespace App\Tests\Integration\Services\WebserviceBundle;

use App\Services\WebserviceBundle\WebserviceSelection;
use ApprovalTests\Approvals;

class WebserviceSelectionTest extends WebserviceTestCase
{
	private function createWebserviceSelection(array $responses): WebserviceSelection
	{
		$webserviceClientProvider = $this->getWebserviceClientProvider($responses);
		return new WebserviceSelection($webserviceClientProvider);
	}

	public function testListerCriteresCriteriumList()
	{
		$responses = [
			$this->getMockResponseFromFixture('Selections/listerCriteres.json'),
		];
		$webserviceSelection = $this->createWebserviceSelection($responses);

		$response = $webserviceSelection->listerCriteres();

		Approvals::verifyJson(json_encode($response->responseManager->getCriteriumListFromCriteres()));
	}

	public function testListerCriteresOperatorsList()
	{
		$webserviceSelection = $this->createWebserviceSelection([
			$this->getMockResponseFromFixture('Selections/listerCriteres.json'),
		]);

		$response = $webserviceSelection->listerCriteres();

		Approvals::verifyJson(json_encode($response->responseManager->getOperatorsListFromCriteres()));
	}

	public function testListerCriteresAggregationsList()
	{
		$webserviceSelection = $this->createWebserviceSelection([
			$this->getMockResponseFromFixture('Selections/listerCriteres.json'),
		]);

		$response = $webserviceSelection->listerCriteres();

		Approvals::verifyJson(json_encode($response->responseManager->getAggregationsListFromCriteres()));
	}

	public function testListerDossiersExtractFolderList()
	{
		$webserviceSelection = $this->createWebserviceSelection([
			$this->getMockResponseFromFixture('Selections/listerDossiers.json'),
		]);

		$response = $webserviceSelection->listerDossiers();

		Approvals::verifyJson(json_encode($response->responseManager->extractFolderList()));
	}

	public function testListerDossiersExtractSimpleSelectionList()
	{
		$webserviceSelection = $this->createWebserviceSelection([
			$this->getMockResponseFromFixture('Selections/listerDossiers.json'),
		]);

		$response = $webserviceSelection->listerDossiers();

		Approvals::verifyJson(json_encode($response->responseManager->extractSimpleSelectionList()));
	}

	public function testListerDonneesExploration()
	{
		$webserviceSelection = $this->createWebserviceSelection([
			$this->getMockResponseFromFixture('Selections/listerDonneesExploration.json'),
		]);

		$response = $webserviceSelection->listerDonneesExploration();

		Approvals::verifyJson(json_encode($response->getValeurs()->toArray()));
	}

}
