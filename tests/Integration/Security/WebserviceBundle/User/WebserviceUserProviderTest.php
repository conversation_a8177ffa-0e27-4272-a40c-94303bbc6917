<?php

namespace App\Tests\Integration\Security\WebserviceBundle\User;

use App\Repository\RequeteurBundle\NotificationRepository;
use App\Security\WebserviceBundle\User\WebserviceUserProvider;
use App\Services\WebserviceBundle\SymfonySessionCookieJar;
use App\Services\WebserviceBundle\WebserviceCarteWallet;
use App\Services\WebserviceBundle\WebserviceContactsEnseigne;
use App\Services\WebserviceBundle\WebserviceExclusion;
use App\Services\WebserviceBundle\WebserviceIdentification;
use ApprovalTests\Approvals;
use PHPUnit\Framework\MockObject\MockBuilder;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpClient\MockHttpClient;
use Symfony\Component\HttpClient\Response\MockResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpFoundation\Session\Storage\MockFileSessionStorage;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Stopwatch\Stopwatch;

class WebserviceUserProviderTest extends WebTestCase
{
	public function getMockedSession(RequestStack|MockObject $requestStack = null): Session
	{
		$session = new Session(new MockFileSessionStorage());
		$currentRequest = new Request();

		$requestStack->method('getSession')->willReturn($session);
		$requestStack->method('getCurrentRequest')->willReturn($currentRequest);

		return $session;
	}


	public function testSimpleString(): void
	{
		$client = self::createClient();
		$container = self::getContainer();

		$requestStack = $this->getMockBuilder(\Symfony\Component\HttpFoundation\RequestStack::class)
			->disableOriginalConstructor()
			->getMock();

		$this->getMockedSession($requestStack);

		$responses = [
			new MockResponse(
				body: file_get_contents($container->getParameter('kernel.project_dir').'/tests/fixtures/user.json'),
				info: ['response_headers' => ['Set-Cookie' => 'JSESSIONID=1U8onciOByTFObI82qWaaaTb; Path=/aquitem']]
			),
			(static function ($method, $url, $options) use ($container) {
				dump($method, $url, $options);
				return new MockResponse(file_get_contents($container->getParameter('kernel.project_dir') . '/tests/fixtures/listerContact.json'));
			}),
		];

		$mockHttpClient = new MockHttpClient($responses);

		$cookieJar = new SymfonySessionCookieJar($requestStack, 'session_key');

		$webserviceIdentification = new WebserviceIdentification(
			$mockHttpClient,
			'http://localhost:8080/ws/Webservice',
			$cookieJar,
			$container->get(Stopwatch::class),
			$requestStack,
		);

		$webserviceContactsEnseigne = new WebserviceContactsEnseigne(
			$mockHttpClient,
			'http://localhost:8080/ws/Webservice',
			$cookieJar,
			$container->get(Stopwatch::class),
			$requestStack,
		);

		$webserviceUserProvider = new WebserviceUserProvider(
			$webserviceIdentification,
			$requestStack,
			$container->get(TokenStorageInterface::class),
			$container->get(WebserviceExclusion::class),
			$webserviceContactsEnseigne,
			$container->get(WebserviceCarteWallet::class),
			$container->get(NotificationRepository::class),
		);

		$user = $webserviceUserProvider->loadUserByUsernameAndPassword('admin', 'admin');

		Approvals::verifyJson($user->serialize());
	}
}
