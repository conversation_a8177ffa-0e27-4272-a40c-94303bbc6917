{"jsonResult": {"infos": [], "validations": [], "erreurs": [], "valeurs": [{"nom": "lesCategoriesDeCriteres", "valeurs": [{"id": "1165", "libelle": "Achats", "valeurs": [{"nom": "criteres", "valeurs": [{"id": "2042", "libelle": "01 Période", "description": "", "type": "periode", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "true", "valeurMinimum": "202302", "controleValeurImportee": "false"}, {"id": "2004", "libelle": "02 Achats Rayons", "description": "", "type": "valeur_predefinie", "idCritereLie": "2042", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "01", "valeurAffichee": "01 - BRICOLAGE"}, {"valeurReelle": "02", "valeurAffichee": "02 - JARDINAGE "}, {"valeurReelle": "03", "valeurAffichee": "03 - AMENAGEMENTS"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2003", "libelle": "03 Achats Groupes", "description": "", "type": "valeur_predefinie", "idCritereLie": "2042", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "011", "valeurAffichee": "011 - OUTILLAGE A MAIN "}, {"valeurReelle": "012", "valeurAffichee": "012 - OUTILLAGE ELECTRIQUE ET THERMIQUE"}, {"valeurReelle": "013", "valeurAffichee": "013 - CLOUTERIE-BOULONNERIE-VISSERIE"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2002", "libelle": "04 Achats Famille", "description": "", "type": "valeur_predefinie", "idCritereLie": "2042", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": " ", "valeurAffichee": ""}, {"valeurReelle": "0111", "valeurAffichee": "0111 - OUTILLAGE D’ATELIER"}, {"valeurReelle": "0112", "valeurAffichee": "0112 - MAÇONNERIE "}, {"valeurReelle": "0113", "valeurAffichee": "0113 - OUTILS DE PEINTRE: PINCEAUX, ROULEAUX,"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2043", "libelle": "05 Rayons, groupes, familles en valeurs liées", "description": "", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "ZZZZ", "valeurAffichee": "<xxx><xxx>"}, {"valeurReelle": "0735", "valeurAffichee": "ALIMENTATION ANIMALE <xxx>ALIMENTATION ANIMAUX FAMILIERS<xxx>ALIMENTATION OISEAUX DE CAGE ET VOLIERE"}, {"valeurReelle": "0736", "valeurAffichee": "ALIMENTATION ANIMALE <xxx>ALIMENTATION ANIMAUX FAMILIERS<xxx>ALIMENTATION OISEAUX DE LA NATURE"}, {"valeurReelle": "0732", "valeurAffichee": "ALIMENTATION ANIMALE <xxx>ALIMENTATION ANIMAUX FAMILIERS<xxx>ALIMENTATION POISSONS, REP<PERSON>LES"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2118", "libelle": "CA / Période", "description": "", "type": "number", "idCritereLie": "", "accepteAgregat": "true", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}]}]}, {"id": "1033", "libelle": "Choix des Stuctures/Enseigne/Magasins", "valeurs": [{"nom": "criteres", "valeurs": [{"id": "1211", "libelle": "01 - Structure", "description": "Permet de cibler les clients rattachés aux magasins d’une même structure ==› Utiliser la touche CTRL pour sélectionner plusieurs magasins", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "A01", "valeurAffichee": "A01-CARRE VERT"}, {"valeurReelle": "A02", "valeurAffichee": "A02-LUR BERRI"}, {"valeurReelle": "A03", "valeurAffichee": "A03-SYCODIS"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1212", "libelle": "02 - <PERSON><PERSON><PERSON>", "description": "Permet de cibler les clients rattachés à des magasins sous une ou plusieurs enseignes ==> Utiliser la touche CTRL pour sélectionner plusieurs enseignes ", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "1", "valeurAffichee": "01 - GAMM VERT"}, {"valeurReelle": "2", "valeurAffichee": "02 - GAMM VERT NATURE"}, {"valeurReelle": "3", "valeurAffichee": "03 - COMPTOIR DU VILLAGE"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1210", "libelle": "03 - <PERSON><PERSON><PERSON>", "description": "Permet de cibler les clients rattachés à un ou plusieurs magasins ==› Utiliser la touche CTRL pour sélectionner plusieurs magasins", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "A27302", "valeurAffichee": "*AIRVAULT"}, {"valeurReelle": "A14077", "valeurAffichee": "*ANGERS"}, {"valeurReelle": "A50408", "valeurAffichee": "*AUMALE"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2268", "libelle": "04 - Type de structure (edition cheques)", "description": "Permet de sélectionner les structures qui éditent leurs chèques via Aquitem et ceux qui le font depuis leur caisse", "type": "string", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2266", "libelle": "05 - Typologie clients", "description": "Permet de sélectionner les clients fidélisés ou non.", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "1", "valeurAffichee": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"valeurReelle": "0", "valeurAffichee": "Non fidélisé"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2267", "libelle": "06 - Typologie GV.fr", "description": "Permet de sélectionner les clients FIDELITE ou les PROSPECTS ayant donné ses informations personnelles via le site GAMM VERT, un jeu concours etc..", "type": "number", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2269", "libelle": "07 - Noms magasins", "description": "Permet de cibler les clients rattachés à un ou plusieurs magasins ==› Utiliser la touche CTRL pour sélectionner plusieurs magasins", "type": "string", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}]}]}, {"id": "1219", "libelle": "Comportement d’achat articles", "valeurs": [{"nom": "criteres", "valeurs": [{"id": "2271", "libelle": "01 - <PERSON><PERSON> d’a<PERSON>t (format : AAAAMM)", "description": "Permet de sélectionner des clients qui ont réalisé au moins un (1) achat sur une période donnée (format : AAAAMM)", "type": "periode", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "true", "valeurMinimum": "202302", "controleValeurImportee": "false"}, {"id": "2272", "libelle": "02 - Code article Zefid", "description": "Permet de sélectionner les clients ayant acheté certains articles sur une période", "type": "string", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2270", "libelle": "03 - Code article <PERSON><PERSON><PERSON> (Liste)", "description": "Permet de sélectionner les clients ayant acheté certains articles sur une période", "type": "string", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2273", "libelle": "04 - CA mensuel comportement achats articles", "description": "Permet de sélectionner des clients selon la CA mensuel Min/Max ou la Somme ou la Moyenne des CA mensuels.", "type": "number", "idCritereLie": "", "accepteAgregat": "true", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}]}]}, {"id": "1218", "libelle": "Comportement d’achat clients", "valeurs": [{"nom": "criteres", "valeurs": [{"id": "2275", "libelle": "01 - <PERSON><PERSON> d’a<PERSON>t (format : AAAAMM)", "description": "Permet de sélectionner des clients qui ont réalisé au moins un (1) achat sur une période donnée (format : AAAAMM)", "type": "periode", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "true", "valeurMinimum": "202302", "controleValeurImportee": "false"}, {"id": "2274", "libelle": "02 - Date de dernier achat", "description": "Permet de sélectionner les clients selon la date du dernier achat effectué.", "type": "date", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2276", "libelle": "03 - <PERSON><PERSON>", "description": "Permet de sélectionner des clients qui ont acheté au moins un produits dans le rayon sélectionné. Il est souvent utile de combiner ce critère avec Période d’achat. Utiliser la touche CTRL  pour une sélection multiple de rayon", "type": "string", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}]}]}, {"id": "1070", "libelle": "Opérations marketing", "valeurs": [{"nom": "criteres", "valeurs": [{"id": "1682", "libelle": "Periode mailing", "description": "", "type": "periode", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "true", "valeurMinimum": "202302", "controleValeurImportee": "false"}, {"id": "1681", "libelle": "Periode venteA", "description": "", "type": "periode", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "true", "valeurMinimum": "202302", "controleValeurImportee": "false"}, {"id": "2046", "libelle": "Type mailing", "description": "", "type": "valeur_predefinie", "idCritereLie": "1682", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "100", "valeurAffichee": "100 - Standard"}, {"valeurReelle": "103", "valeurAffichee": "103 - Anniversaire"}, {"valeurReelle": "105", "valeurAffichee": "105 - Bienvenue"}, {"valeurReelle": "174", "valeurAffichee": "174 - OP CHIOTS 1ER ENVOI"}, {"valeurReelle": "175", "valeurAffichee": "175 - OP CHIOTS 2EME ENVOI"}, {"valeurReelle": "176", "valeurAffichee": "176 - OP CHIOTS 3EME ENVOI"}, {"valeurReelle": "999", "valeurAffichee": "999 - Test TYPE MAILING INNEXISTANT"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}]}]}, {"id": "1012", "libelle": "Profils des clients", "valeurs": [{"nom": "criteres", "valeurs": [{"id": "1106", "libelle": "02 - Age", "description": "Permet de sélectionner les clients selon leur âge", "type": "number", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1107", "libelle": "03 - Code postal", "description": "Permet de sélectionner des clients selon la valeur du code postal.", "type": "string", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1108", "libelle": "04 - <PERSON><PERSON> courrier", "description": "Permet de sélectionner les clients contactables par Courrier", "type": "valeur_predefinie", "idCritereLie": "2266", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "1", "valeurAffichee": "Contactable"}, {"valeurReelle": "0", "valeurAffichee": "Non contactable"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2265", "libelle": "05 - Contactable par <PERSON><PERSON>", "description": "", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "1", "valeurAffichee": "Contactable "}, {"valeurReelle": "0", "valeurAffichee": "Non contactable "}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1109", "libelle": "05 - Contactable par SMS", "description": "Permet de sélectionner les clients contactables par SMS", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "1", "valeurAffichee": "Contactable"}, {"valeurReelle": "0", "valeurAffichee": "Non contactable"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1110", "libelle": "06 - Contactable par email", "description": "Permet de sélectionner les clients contactables par Email", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "1", "valeurAffichee": "Contactable"}, {"valeurReelle": "0", "valeurAffichee": "Non contactable"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1279", "libelle": "07 - <PERSON><PERSON>", "description": "", "type": "string", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1683", "libelle": "<PERSON><PERSON><PERSON>", "description": "", "type": "valeur_predefinie", "idCritereLie": "2004", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "1", "valeurAffichee": "1 - <PERSON><PERSON>"}, {"valeurReelle": "2", "valeurAffichee": "2 - <PERSON><PERSON>"}, {"valeurReelle": "3", "valeurAffichee": "3 - Ron<PERSON>urs"}, {"valeurReelle": "4", "valeurAffichee": "4 - <PERSON><PERSON><PERSON>"}, {"valeurReelle": "5", "valeurAffichee": "5 - <PERSON><PERSON><PERSON>"}, {"valeurReelle": "6", "valeurAffichee": "6 - <PERSON><PERSON><PERSON>"}, {"valeurReelle": "7", "valeurAffichee": "7 - <PERSON><PERSON>/Canards"}, {"valeurReelle": "8", "valeurAffichee": "8 - <PERSON><PERSON><PERSON>"}, {"valeurReelle": "9", "valeurAffichee": "9 - <PERSON><PERSON><PERSON>"}, {"valeurReelle": "10", "valeurAffichee": "10 - Pa<PERSON> d<PERSON>animaux"}, {"valeurReelle": "11", "valeurAffichee": "11 - <PERSON><PERSON>"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1678", "libelle": "CA 12 mois glissants", "description": "", "type": "number", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1680", "libelle": "Ca/periode", "description": "", "type": "number", "idCritereLie": "", "accepteAgregat": "true", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1778", "libelle": "Civilité", "description": "", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "inconnue", "valeurAffichee": "inconnue"}, {"valeurReelle": "INCONNUE", "valeurAffichee": "INCONNUE"}, {"valeurReelle": "M", "valeurAffichee": "M"}, {"valeurReelle": "M    ", "valeurAffichee": "M    "}, {"valeurReelle": "<PERSON>.", "valeurAffichee": "<PERSON>."}, {"valeurReelle": "MELLE", "valeurAffichee": "MELLE"}, {"valeurReelle": "MLLE", "valeurAffichee": "MLLE"}, {"valeurReelle": "MME", "valeurAffichee": "MME"}, {"valeurReelle": "MME  ", "valeurAffichee": "MME  "}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2005", "libelle": "ClientZZZ", "description": "", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "0", "valeurAffichee": "0"}, {"valeurReelle": "1", "valeurAffichee": "1"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2264", "libelle": "Contactable par SMS Frais d’Ici", "description": "", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "1", "valeurAffichee": "Contactable "}, {"valeurReelle": "0", "valeurAffichee": "Non contactable "}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1282", "libelle": "Date naissance", "description": "Date naissance", "type": "date", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2052", "libelle": "id client", "description": "", "type": "number", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "1514", "libelle": "no clients", "description": "", "type": "number", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2044", "libelle": "Recherche par NOM", "description": "", "type": "string", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2000", "libelle": "Segmentation RFM", "description": "", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "8", "valeurAffichee": "DORMANTS 6 MOIS"}, {"valeurReelle": "9", "valeurAffichee": "DORMANTS 12 MOIS"}, {"valeurReelle": "10", "valeurAffichee": "DORMANTS 18 MOIS"}, {"valeurReelle": "11", "valeurAffichee": "DORMANTS 24 MOIS"}, {"valeurReelle": "1", "valeurAffichee": "ESSAYEURS"}, {"valeurReelle": "7", "valeurAffichee": "NOUVEAUX"}, {"valeurReelle": "3", "valeurAffichee": "OCCASIONNELS GROS PANIERS"}, {"valeurReelle": "2", "valeurAffichee": "OCCASIONNELS PETITS PANIERS"}, {"valeurReelle": "5", "valeurAffichee": "REGULIERS GROS PANIERS"}, {"valeurReelle": "4", "valeurAffichee": "REGULIERS PETITS PANIERS"}, {"valeurReelle": "6", "valeurAffichee": "VIP"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2051", "libelle": "Tel mobile", "description": "", "type": "string", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}, {"id": "2050", "libelle": "Ville du magasin", "description": "", "type": "valeur_predefinie", "idCritereLie": "", "accepteAgregat": "false", "visible": "true", "valeurs": [{"nom": "valeursPredefinies", "valeurs": [{"valeurReelle": "ABBEVILLE", "valeurAffichee": "ABBEVILLE"}, {"valeurReelle": "ACIGNE", "valeurAffichee": "ACIGNE"}, {"valeurReelle": "AGONAC", "valeurAffichee": "AGONAC"}]}], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}]}]}, {"id": "1164", "libelle": "Selections statistiques", "valeurs": [{"nom": "criteres", "valeurs": [{"id": "1999", "libelle": "Sélections", "description": "", "type": "number", "idCritereLie": "", "accepteAgregat": "true", "visible": "true", "valeurs": [], "compatiblePromotion": "true", "controleValeur": "false", "valeurMinimum": "", "controleValeurImportee": "false"}]}]}]}, {"nom": "lesOperateurs", "valeurs": [{"type": "date", "valeurs": [{"id": "AFT", "libelle": "supérie<PERSON> ou <PERSON><PERSON>", "selected": "false"}, {"id": "BEF", "libelle": "inférieur ou égal", "selected": "false"}, {"id": "DIF", "libelle": "différent", "selected": "false"}, {"id": "EQU", "libelle": "<PERSON><PERSON>", "selected": "true"}, {"id": "INF", "libelle": "inférieur", "selected": "false"}, {"id": "SUP", "libelle": "supérieur", "selected": "false"}, {"id": "[[", "libelle": "entre val1 (inclus) et val2 (exclus)", "selected": "false"}, {"id": "[]", "libelle": "entre val1 (inclus) et val2 (inclus)", "selected": "false"}, {"id": "][", "libelle": "entre val1 (exclus) et val2 (exclus)", "selected": "false"}, {"id": "]]", "libelle": "entre val1 (exclus) et val2 (inclus)", "selected": "false"}]}, {"type": "number", "valeurs": [{"id": "AFT", "libelle": "supérie<PERSON> ou <PERSON><PERSON>", "selected": "false"}, {"id": "BEF", "libelle": "inférieur ou égal", "selected": "false"}, {"id": "DIF", "libelle": "différent", "selected": "false"}, {"id": "EQU", "libelle": "<PERSON><PERSON>", "selected": "true"}, {"id": "INF", "libelle": "inférieur", "selected": "false"}, {"id": "SUP", "libelle": "supérieur", "selected": "false"}, {"id": "[[", "libelle": "entre val1 (inclus) et val2 (exclus)", "selected": "false"}, {"id": "[]", "libelle": "entre val1 (inclus) et val2 (inclus)", "selected": "false"}, {"id": "][", "libelle": "entre val1 (exclus) et val2 (exclus)", "selected": "false"}, {"id": "]]", "libelle": "entre val1 (exclus) et val2 (inclus)", "selected": "false"}]}, {"type": "string_special", "valeurs": [{"id": "COM", "libelle": "commence par", "selected": "false"}, {"id": "CON", "libelle": "contient", "selected": "false"}, {"id": "DIF", "libelle": "différent de", "selected": "false"}, {"id": "EQU", "libelle": "égal à", "selected": "true"}, {"id": "FIN", "libelle": "finit par", "selected": "false"}, {"id": "NOC", "libelle": "ne contient pas", "selected": "false"}, {"id": "NOE", "libelle": "ne finit pas par", "selected": "false"}, {"id": "NOS", "libelle": "ne commence pas par", "selected": "false"}]}, {"type": "boolean", "valeurs": [{"id": "FALSE", "libelle": "est faux", "selected": "false"}, {"id": "TRUE", "libelle": "est vrai", "selected": "true"}]}, {"type": "string", "valeurs": [{"id": "COM", "libelle": "commence par", "selected": "false"}, {"id": "CON", "libelle": "contient", "selected": "false"}, {"id": "DIF", "libelle": "différent de", "selected": "false"}, {"id": "EQU", "libelle": "égal à", "selected": "true"}, {"id": "FIN", "libelle": "finit par", "selected": "false"}, {"id": "NOC", "libelle": "ne contient pas", "selected": "false"}, {"id": "NOE", "libelle": "ne finit pas par", "selected": "false"}, {"id": "NOS", "libelle": "ne commence pas par", "selected": "false"}]}, {"type": "periodeQuotidienne", "valeurs": [{"id": "AFT", "libelle": "supérie<PERSON> ou <PERSON><PERSON>", "selected": "false"}, {"id": "BEF", "libelle": "inférieur ou égal", "selected": "false"}, {"id": "DIF", "libelle": "différent", "selected": "false"}, {"id": "EQU", "libelle": "<PERSON><PERSON>", "selected": "true"}, {"id": "INF", "libelle": "inférieur", "selected": "false"}, {"id": "SUP", "libelle": "supérieur", "selected": "false"}, {"id": "[[", "libelle": "entre val1 (inclus) et val2 (exclus)", "selected": "false"}, {"id": "[]", "libelle": "entre val1 (inclus) et val2 (inclus)", "selected": "false"}, {"id": "][", "libelle": "entre val1 (exclus) et val2 (exclus)", "selected": "false"}, {"id": "]]", "libelle": "entre val1 (exclus) et val2 (inclus)", "selected": "false"}]}, {"type": "valeur_predefinie", "valeurs": [{"id": "DIF", "libelle": "différent de", "selected": "false"}, {"id": "EQU", "libelle": "égal à", "selected": "true"}]}, {"type": "periode", "valeurs": [{"id": "AFT", "libelle": "supérie<PERSON> ou <PERSON><PERSON>", "selected": "false"}, {"id": "BEF", "libelle": "inférieur ou égal", "selected": "false"}, {"id": "DIF", "libelle": "différent", "selected": "false"}, {"id": "EQU", "libelle": "<PERSON><PERSON>", "selected": "true"}, {"id": "INF", "libelle": "inférieur", "selected": "false"}, {"id": "SUP", "libelle": "supérieur", "selected": "false"}, {"id": "[[", "libelle": "entre val1 (inclus) et val2 (exclus)", "selected": "false"}, {"id": "[]", "libelle": "entre val1 (inclus) et val2 (inclus)", "selected": "false"}, {"id": "][", "libelle": "entre val1 (exclus) et val2 (exclus)", "selected": "false"}, {"id": "]]", "libelle": "entre val1 (exclus) et val2 (inclus)", "selected": "false"}]}]}, {"nom": "lesOperateursPourAgregats", "valeurs": [{"type": "date", "valeurs": [{"id": "", "libelle": "Aucun calcul", "selected": "true"}, {"id": "MAX", "libelle": "maximum", "selected": "false"}, {"id": "MIN", "libelle": "minimum", "selected": "false"}]}, {"type": "number", "valeurs": [{"id": "", "libelle": "Aucun calcul", "selected": "true"}, {"id": "AVG", "libelle": "moyenne", "selected": "false"}, {"id": "MAX", "libelle": "maximum", "selected": "false"}, {"id": "MIN", "libelle": "minimum", "selected": "false"}, {"id": "SUM", "libelle": "somme", "selected": "false"}]}]}, {"nom": "lesOperateursEnsemblistes", "valeurs": [{"id": "INTER", "libelle": "et", "selected": "false"}, {"id": "MINUS", "libelle": "sauf", "selected": "false"}, {"id": "UNION", "libelle": "ou", "selected": "true"}]}]}}