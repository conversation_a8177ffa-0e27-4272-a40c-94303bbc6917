# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
# DATABASE_URL="sqlite:///%kernel.project_dir%/var/data_%kernel.environment%.db"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/app?serverVersion=8.0.32&charset=utf8mb4"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/app?serverVersion=10.11.2-MariaDB&charset=utf8mb4"
DATABASE_URL="*****************************************/app?serverVersion=17.5&charset=utf8"
###< doctrine/doctrine-bundle ###

###> symfony/mailer ###
MAILER_DSN=smtp://mailer:1025
###< symfony/mailer ###

SERVER_NAME="ptia.localhost, localhost"
TASK_X_REMOTE_TASKFILES=1
SITE_BASE_SCHEME=https
SITE_BASE_HOST=ptia.localhost
MAILER_SENDER=<EMAIL>
MAILER_DEV_RECIPIENT='<EMAIL>'
ORCHESTRATION_API_KEY=
ORCHESTRATION_API_URL="https://orchestration.ia.alienor.net"
MINIO_USER='access1234'
MINIO_PASSWORD='secret1234'
MINIO_ENDPOINT='http://minio:9000'
MINIO_BUCKET_NAME='portail-tools-ia'

KEYCLOAK_HOSTNAME='https://accounts.alienor.net'
KEYCLOAK_REALM='dev-pp'
KEYCLOAK_CLIENT_ID='portail-tools-ia'
KEYCLOAK_ADMIN_USERNAME=
KEYCLOAK_ADMIN_PASSWORD=
