<script lang="ts">
import Select from '~/svelte/ui/Select.svelte';
import { getContext } from 'svelte';

let {
    id = '',
    label = '',
    options = [],
    value = '',
    defaultValue = '',
    required = false,
    disabled = false,
    oninput = () => {},
}: {
    id: string;
    label: string;
    options: { value: string; label: string }[];
    value: string;
    defaultValue: string;
    required: boolean;
    disabled: boolean;
    oninput: (event: CustomEvent<string>) => void;
} = $props();

const formSchema: any = getContext('formSchema');
const models = formSchema?.modelSelectionConfiguration?.authorizedModels ?? [];
const choices = models.map((model: any) => ({ value: model.id, label: model.name }));
const defaultModel = formSchema?.modelSelectionConfiguration?.defaultModel ?? null;
</script>

<div class="model-selector-container">
    <Select
        {id}
        {label}
        {required}
        {disabled}
        {value}
        defaultValue={defaultModel}
        {oninput}
        options={choices}
    />
</div>
