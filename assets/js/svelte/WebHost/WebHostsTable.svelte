<script lang="ts">
	import { onMount } from "svelte";
	import { writable } from 'svelte/store';
	import {
		createSvelteTable,
		flexRender,
		getCoreRowModel,
		getSortedRowModel,
		getFilteredRowModel,
		renderComponent
	} from '@tanstack/svelte-table';
	import type { ColumnDef, TableOptions, SortingState } from '@tanstack/svelte-table';

	import { getWebHosts } from "../../api";

	// Components
	import WebHostType from "./Cells/WebHostType.svelte";
	import WebHostEnvironment from "./Cells/WebHostEnvironment.svelte";
	import WebHostGitRepository from "./Cells/WebHostGitRepository.svelte";
	import WebHostVisibility from "./Cells/WebHostVisibility.svelte";
	import WebHostUrls from "./Cells/WebHostUrls.svelte";
	import WebHostHealth from "./Cells/WebHostHealth.svelte";
	import WebHostSSL from "./Cells/WebHostSSL.svelte";
	import WebHostActions from "./Cells/WebHostActions.svelte";
	import WebHostExpandButton from "./Cells/WebHostExpandButton.svelte";

	let loading = $state(false);
	let webHosts: WebHost[] = $state([]);
	let loadingErrors: object[] = $state([]);
	let searchValue: string = $state("");

	// TanStack Table state
	let sorting: SortingState = $state([]);
	let globalFilter = $state("");

	const setSorting = (updater: any) => {
		if (typeof updater === 'function') {
			sorting = updater(sorting);
		} else {
			sorting = updater;
		}
	};

	// Colonnes du tableau
	const columns: ColumnDef<WebHost>[] = [
		{
			id: "expand",
			header: "",
			enableSorting: false,
			cell: (props) => renderComponent(WebHostExpandButton, {
				webHost: props.row.original,
			}),
		},
		{
			id: "actions",
			header: "",
			enableSorting: false,
			cell: (props) => renderComponent(WebHostActions, {
				webHost: props.row.original,
			}),
		},
		{
			id: "name",
			accessorKey: "name",
			header: "Nom",
			cell: info => info.getValue(),
		},
		{
			id: "type",
			accessorFn: (webHost: WebHost) => webHost.configuration?.type || "",
			header: "Type",
			cell: (props) => renderComponent(WebHostType, {
				webHost: props.row.original,
			}),
		},
		{
			id: "environment",
			accessorKey: "environnement",
			header: "Environnement",
			cell: (props) => renderComponent(WebHostEnvironment, {
				webHost: props.row.original,
			}),
		},
		{
			id: "git",
			accessorKey: "gitlabRemoteUrl",
			header: "Dépôt Git",
			cell: (props) => renderComponent(WebHostGitRepository, {
				webHost: props.row.original,
			}),
		},
		{
			id: "visibility",
			accessorKey: "expectedVisibility",
			header: "Visibilité",
			cell: (props) => renderComponent(WebHostVisibility, {
				webHost: props.row.original,
			}),
		},
		{
			id: "urls",
			accessorFn: (webHost: WebHost) => webHost.urls.map(u => u.url).join(" "),
			header: "URL",
			enableSorting: false,
			cell: (props) => renderComponent(WebHostUrls, {
				webHost: props.row.original,
			}),
		},
		{
			id: "health",
			accessorFn: (webHost: WebHost) => {
				const statuses = webHost.urls.map(u => u.healthCheckReport.status).filter(Boolean);
				if (statuses.includes('CRITICAL')) return 'CRITICAL';
				if (statuses.includes('WARNING')) return 'WARNING';
				return 'OK';
			},
			header: "Santé",
			cell: (props) => renderComponent(WebHostHealth, {
				webHost: props.row.original,
			}),
		},
		{
			id: "ssl",
			accessorFn: (webHost: WebHost) => {
				const validStatuses = webHost.urls.map(u => u.sslCertificateReport.isValid);
				return validStatuses.includes(false) ? 'INVALID' : 'VALID';
			},
			header: "SSL",
			cell: (props) => renderComponent(WebHostSSL, {
				webHost: props.row.original,
			}),
		},
	];

	// Configuration TanStack Table
	const tableOptions = writable<TableOptions<WebHost>>({
		data: [],
		columns,
		state: {
			sorting,
		},
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		onSortingChange: setSorting,
		enableColumnFilter: false
	});

	const table = createSvelteTable(tableOptions);

	const handleSearch = (e: any) => {
		$table.setGlobalFilter(String(e?.target?.value))
	};

	// Mise à jour des données de la table
	$effect(() => {
		tableOptions.update(options => ({
			...options,
			data: webHosts,
			state: {
				...options.state,
				sorting,
			},
		}));
	});

	// Chargement des données
	const loadWebHosts = async () => {
		loading = true;
		loadingErrors = [];
		try {
			const result = await getWebHosts();
			webHosts = result.webHosts;
		} catch (error) {
			loadingErrors = [{ message: 'Erreur lors du chargement des hébergements' }];
			console.error('Erreur lors du chargement des hébergements:', error);
		} finally {
			loading = false;
		}
	};

	onMount(() => {
		loadWebHosts();
	});

	// Gestion du comptage des éléments
	let tableData = $derived($table.getRowModel().rows.map(row => row.original));
	let countsWebHosts = $derived(tableData.length);
</script>

{#if loadingErrors.length > 0}
	<div class="alert alert-danger">
		{#each loadingErrors as error}
			<p>{error.message}</p>
		{/each}
	</div>
{/if}

<div class="d-flex justify-content-between align-items-center mb-3">
	<div class="d-flex align-items-center">
		{#if !loading}
			<div class="me-3">
				{countsWebHosts} hébergement{countsWebHosts > 1 ? "s" : ""}
			</div>
		{/if}
	</div>

	<div class="d-flex align-items-center gap-2">
		<input
			type="text"
			class="form-control"
			placeholder="Rechercher..."
			bind:value={searchValue}
			oninput={handleSearch}
			style="width: 250px;"
		/>
		<a href="/webHosts/create" class="btn btn-primary">
			Ajouter un hébergement
		</a>
		<button
			class="btn btn-secondary"
			onclick={() => loadWebHosts()}
			disabled={loading}
		>
			{#if loading}
				<i class="fa-solid fa-circle-notch fa-spin"></i>
			{:else}
				<i class="fa-solid fa-arrows-rotate"></i>
			{/if}
			Actualiser
		</button>
	</div>
</div>

<div class="table-responsive">
	<div class="gridjs gridjs-container" style="width: 100%">
		<table class="gridjs-table table table-hover">
			<thead class="gridjs-thead table-dark">
				<tr class="gridjs-tr">
					{#each $table.getHeaderGroups() as headerGroup}
						{#each headerGroup.headers as header}
							<th class="gridjs-th" style={header.id === 'expand' ? 'width: 48px;' : ''}>
								{#if !header.isPlaceholder}
									<button
										class="btn btn-link text-white text-decoration-none p-0"
										class:cursor-pointer={header.column.getCanSort()}
										onclick={header.column.getToggleSortingHandler()}
									>
										<svelte:component this={flexRender(header.column.columnDef.header, header.getContext())}/>
										{#if header.column.getIsSorted() === 'asc'}
											<i class="fa-solid fa-sort-up ms-1"></i>
										{:else if header.column.getIsSorted() === 'desc'}
											<i class="fa-solid fa-sort-down ms-1"></i>
										{:else if header.column.getCanSort()}
											<i class="fa-solid fa-sort ms-1 opacity-50"></i>
										{/if}
									</button>
								{/if}
							</th>
						{/each}
					{/each}
				</tr>
			</thead>
			<tbody class="gridjs-tbody">
				{#if loading}
					<tr class="gridjs-tr">
						<td class="gridjs-td text-center" colspan={columns.length}>
							<i class="fa-solid fa-circle-notch fa-spin"></i>
							Chargement...
						</td>
					</tr>
				{:else if $table.getRowModel().rows.length === 0}
					<tr class="gridjs-tr">
						<td class="gridjs-td text-center" colspan={columns.length}>
							Aucun hébergement trouvé
						</td>
					</tr>
				{:else}
					{#each $table.getRowModel().rows as row (row.original.id)}
						<tr class="gridjs-tr table-light" data-id={row.original.id}>
							{#each row.getVisibleCells() as cell}
								<td class="gridjs-td">
									<svelte:component this={flexRender(cell.column.columnDef.cell, cell.getContext())}/>
								</td>
							{/each}
						</tr>
						<!-- Lignes d'expansion pour les URLs multiples -->
						{#if row.original.urls.length > 1}
							{#each row.original.urls as url}
								<tr class="gridjs-tr table-secondary collapse" data-web-host-id={row.original.id} data-url-id={url.id}>
									<td class="gridjs-td" colspan="7"></td>
									<td class="gridjs-td">
										{#if url.url}
											<a target="_blank" href={url.url}>{url.url}</a>
										{/if}
									</td>
									<td class="gridjs-td">
										{#if url.healthCheckReport.status}
											{#if url.healthCheckReport.status === 'WARNING'}
												<span class="badge bg-white">
													<i class="fa-solid fa-triangle-exclamation text-warning"></i>
												</span>
											{:else if url.healthCheckReport.status === 'CRITICAL'}
												<span class="badge bg-white">
													<i class="fa-solid fa-ban text-danger"></i>
												</span>
											{:else}
												<span class="badge bg-white">
													<i class="fa-solid fa-circle-check text-success"></i>
												</span>
											{/if}
										{/if}
									</td>
									<td class="gridjs-td">
										{#if url.sslCertificateReport.isValid !== undefined}
											{#if url.sslCertificateReport.isValid}
												<span class="badge bg-white">
													<i class="fa-solid fa-circle-check text-success"></i>
												</span>
											{:else}
												<span class="badge bg-white">
													<i class="fa-solid fa-ban text-danger"></i>
												</span>
											{/if}
										{/if}
									</td>
								</tr>
							{/each}
						{/if}
					{/each}
				{/if}
			</tbody>
		</table>
	</div>
</div>
