<script lang="ts">
	import { onMount } from "svelte";
	import { writable } from 'svelte/store';
	import {
		createSvelteTable,
		flexRender,
		getCoreRowModel,
		getSortedRowModel,
		getFilteredRowModel,
		getExpandedRowModel,
		renderComponent
	} from '@tanstack/svelte-table';
	import type { ColumnDef, TableOptions, SortingState, ExpandedState } from '@tanstack/svelte-table';

	import { getWebHosts } from "../../api";

	// Components
	import WebHostType from "./Cells/WebHostType.svelte";
	import WebHostEnvironment from "./Cells/WebHostEnvironment.svelte";
	import WebHostGitRepository from "./Cells/WebHostGitRepository.svelte";
	import WebHostVisibility from "./Cells/WebHostVisibility.svelte";
	import WebHostUrls from "./Cells/WebHostUrls.svelte";
	import WebHostHealth from "./Cells/WebHostHealth.svelte";
	import WebHostSSL from "./Cells/WebHostSSL.svelte";
	import WebHostActions from "./Cells/WebHostActions.svelte";
	import WebHostExpandButton from "./Cells/WebHostExpandButton.svelte";
	import WebHostUrlRow from "./Cells/WebHostUrlRow.svelte";

	let loading = $state(false);
	let webHosts: WebHost[] = $state([]);
	let loadingErrors: object[] = $state([]);
	let searchValue: string = $state("");

	// TanStack Table state
	let sorting: SortingState = $state([]);
	let globalFilter = $state("");
	let expanded: ExpandedState = $state({});

	const setSorting = (updater: any) => {
		if (typeof updater === 'function') {
			sorting = updater(sorting);
		} else {
			sorting = updater;
		}
	};

	const setExpanded = (updater: any) => {
		if (typeof updater === 'function') {
			expanded = updater(expanded);
		} else {
			expanded = updater;
		}
	};

	// Colonnes du tableau
	const columns: ColumnDef<WebHost>[] = [
		{
			id: "expand",
			header: "",
			enableSorting: false,
			cell: (props) => renderComponent(WebHostExpandButton, {
				webHost: props.row.original,
				row: props.row,
			}),
		},
		{
			id: "name",
			accessorKey: "name",
			header: "Nom",
			cell: info => info.getValue(),
		},
		{
			id: "type",
			accessorFn: (webHost: WebHost) => webHost.configuration?.type || "",
			header: "Type",
			cell: (props) => renderComponent(WebHostType, {
				webHost: props.row.original,
			}),
		},
		{
			id: "environment",
			accessorKey: "environnement",
			header: "Environnement",
			cell: (props) => renderComponent(WebHostEnvironment, {
				webHost: props.row.original,
			}),
		},
		{
			id: "git",
			accessorKey: "gitlabRemoteUrl",
			header: "Dépôt Git",
			cell: (props) => renderComponent(WebHostGitRepository, {
				webHost: props.row.original,
			}),
		},
		{
			id: "visibility",
			accessorKey: "expectedVisibility",
			header: "Visibilité",
			cell: (props) => renderComponent(WebHostVisibility, {
				webHost: props.row.original,
			}),
		},
		{
			id: "urls",
			accessorFn: (webHost: WebHost) => webHost.urls.map(u => u.url).join(" "),
			header: "URL",
			enableSorting: false,
			cell: (props) => renderComponent(WebHostUrls, {
				webHost: props.row.original,
			}),
		},
		{
			id: "health",
			accessorFn: (webHost: WebHost) => {
				const statuses = webHost.urls.map(u => u.healthCheckReport.status).filter(Boolean);
				if (statuses.includes('CRITICAL')) return 'CRITICAL';
				if (statuses.includes('WARNING')) return 'WARNING';
				return 'OK';
			},
			header: "Santé",
			cell: (props) => renderComponent(WebHostHealth, {
				webHost: props.row.original,
			}),
		},
		{
			id: "ssl",
			accessorFn: (webHost: WebHost) => {
				const validStatuses = webHost.urls.map(u => u.sslCertificateReport.isValid);
				return validStatuses.includes(false) ? 'INVALID' : 'VALID';
			},
			header: "SSL",
			cell: (props) => renderComponent(WebHostSSL, {
				webHost: props.row.original,
			}),
		},
		{
			id: "actions",
			header: "Détails",
			enableSorting: false,
			cell: (props) => renderComponent(WebHostActions, {
				webHost: props.row.original,
			}),
		},
	];

	// Configuration TanStack Table
	const tableOptions = writable<TableOptions<WebHost>>({
		data: [],
		columns,
		state: {
			sorting,
			expanded,
		},
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getExpandedRowModel: getExpandedRowModel(),
		onSortingChange: setSorting,
		onExpandedChange: setExpanded,
		enableColumnFilter: false,
		getRowCanExpand: (row) => row.original.urls.length > 1,
	});

	const table = createSvelteTable(tableOptions);

	const handleSearch = (e: any) => {
		$table.setGlobalFilter(String(e?.target?.value))
	};

	// Mise à jour des données de la table
	$effect(() => {
		tableOptions.update(options => ({
			...options,
			data: webHosts,
			state: {
				...options.state,
				sorting,
				expanded,
			},
		}));
	});

	// Chargement des données
	const loadWebHosts = async () => {
		loading = true;
		loadingErrors = [];
		try {
			const result = await getWebHosts();
			webHosts = result.webHosts;
		} catch (error) {
			loadingErrors = [{ message: 'Erreur lors du chargement des hébergements' }];
			console.error('Erreur lors du chargement des hébergements:', error);
		} finally {
			loading = false;
		}
	};

	onMount(() => {
		loadWebHosts();
	});

	// Gestion du comptage des éléments
	let tableData = $derived($table.getRowModel().rows.map(row => row.original));
	let countsWebHosts = $derived(tableData.length);

	let sortedColumn = $derived(sorting[0]?.id);
	let sortedDirection = $derived(sorting[0]?.desc ? 'desc' : 'asc');
</script>

{#if loadingErrors.length > 0}
	<div class="alert alert-danger d-flex align-items-center" role="alert">
		<i class="fa fa-info-circle me-2"></i>
		<div>
			{#each loadingErrors as error}
				{error.message}
			{/each}
		</div>
	</div>
{/if}

<div class="table-responsive">
	<div class="gridjs gridjs-container" style="width: 100%">
		<div class="d-flex align-items-center justify-content-between mb-3">
			<div class="d-flex align-items-center">
				{#if !loading}
					<div class="me-2">
						{countsWebHosts} hébergement{countsWebHosts > 1 ? "s" : ""}
					</div>
				{/if}
			</div>
			<div>
				<a href="/webHosts/create" class="btn btn-sm btn-info text-white me-2">
					Ajouter un hébergement
				</a>
				<button
					class="btn btn-sm btn-info text-white"
					onclick={() => loadWebHosts()}
					disabled={loading}
				>
					{#if loading}
						<i class="fa-solid fa-circle-notch fa-spin"></i>
					{:else}
						<i class="fa-solid fa-arrows-rotate"></i>
					{/if}
					Actualiser
				</button>
			</div>
		</div>
		<div class="d-flex align-items-center mb-3">
			<div class="gridjs-search me-2">
				<div class="input-group">
					<input
						bind:value={searchValue}
						oninput={handleSearch}
						type="text"
						placeholder="Rechercher..."
						class="gridjs-input gridjs-search-input"
					/>
					{#if searchValue !== ""}
						<span class="input-group-text"
							  type="button"
							  onclick={() => {
								  searchValue = "";
								  globalFilter = "";
								  $table.setGlobalFilter("");
							  }}>
							<i class="fa fa-times"></i>
						</span>
					{:else}
						<span class="input-group-text">
							<i class="fa fa-magnifying-glass"></i>
						</span>
					{/if}
				</div>
			</div>
		</div>
		<div class="gridjs-wrapper" style="height: auto">
		<table
			role="grid"
			class="gridjs-table"
			style="min-width: 100%; height: auto"
		>
			<thead class="gridjs-head">
				{#each $table.getHeaderGroups() as headerGroup}
					<tr class="gridjs-tr">
						{#each headerGroup.headers as header}
							<th
								onclick={header.column.getToggleSortingHandler()}
								class="gridjs-th"
								class:gridjs-th-sort={header.column.getCanSort()}
								class:cursor-pointer={header.column.getCanSort()}
								style={header.id === 'expand' ? 'width: 48px;' : ''}
							>
								<div class="gridjs-th-content">
									{@html header.column.columnDef.header}
								</div>
								{#if header.column.getCanSort()}
									<button
											title="Sort column"
											class="gridjs-sort gridjs-sort-{sortedColumn === header.column.id
												? sortedDirection
												: 'neutral'}"
									></button>
								{/if}
							</th>
						{/each}
					</tr>
				{/each}
			</thead>
			<tbody class="gridjs-tbody">
				{#if loading}
					<tr class="gridjs-tr">
						<td colspan="100%" class="gridjs-td">
							<div class="d-flex justify-content-center">
								<div class="spinner-border text-info m-5" role="status">
									<span class="visually-hidden">Loading...</span>
								</div>
							</div>
						</td>
					</tr>
				{:else if $table.getRowModel().rows.length === 0}
					<tr class="gridjs-tr">
						<td colspan="100%" class="gridjs-td text-center">
							Aucun hébergement trouvé
						</td>
					</tr>
				{:else}
					{#each $table.getRowModel().rows as row (row.original.id)}
						<tr class="gridjs-tr" data-id={row.original.id}>
							{#each row.getVisibleCells() as cell}
								<td class="gridjs-td">
									<svelte:component this={flexRender(cell.column.columnDef.cell, cell.getContext())}/>
								</td>
							{/each}
						</tr>
						<!-- Lignes d'expansion pour les URLs multiples -->
						{#if row.getIsExpanded() && row.original.urls.length > 1}
							{#each row.original.urls as url}
								<tr class="gridjs-tr" style="background-color: #f8f9fa;">
									<WebHostUrlRow {url} columnsCount={columns.length} />
								</tr>
							{/each}
						{/if}
					{/each}
				{/if}
			</tbody>
		</table>
		</div>
	</div>
</div>
