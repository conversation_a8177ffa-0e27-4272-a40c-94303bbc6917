<script lang="ts">
    interface Props {
        url: WebHostUrl;
        columnsCount: number;
    }

    let { url, columnsCount }: Props = $props();
</script>

<!-- Colonnes vides pour l'alignement -->
<td class="gridjs-td" colspan={columnsCount - 2}></td>

<!-- URL -->
<td class="gridjs-td">
    {#if url.url}
        <a target="_blank" href={url.url}>{url.url}</a>
    {/if}
</td>

<!-- Santé -->
<td class="gridjs-td">
    {#if url.healthCheckReport.status}
        <span class="badge bg-white">
            {#if url.healthCheckReport.status === 'WARNING'}
                <i class="fa-solid fa-triangle-exclamation text-warning"></i>
            {:else if url.healthCheckReport.status === 'CRITICAL'}
                <i class="fa-solid fa-ban text-danger"></i>
            {:else}
                <i class="fa-solid fa-circle-check text-success"></i>
            {/if}
        </span>
    {/if}
</td>

<!-- SSL -->
<td class="gridjs-td">
    {#if url.sslCertificateReport.isValid !== undefined}
        <span class="badge bg-white">
            {#if url.sslCertificateReport.isValid}
                <i class="fa-solid fa-circle-check text-success"></i>
            {:else}
                <i class="fa-solid fa-ban text-danger"></i>
            {/if}
        </span>
    {/if}
</td>
