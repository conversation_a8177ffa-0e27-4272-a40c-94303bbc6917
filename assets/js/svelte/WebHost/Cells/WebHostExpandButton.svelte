<script lang="ts">
    import type { Row } from '@tanstack/svelte-table';

    interface Props {
        webHost: WebHost;
        row: Row<WebHost>;
    }

    let { webHost, row }: Props = $props();

    const toggleExpanded = () => {
        row.toggleExpanded();
    };
</script>

{#if webHost.urls.length > 1}
    <button
        class="btn btn-sm btn-transparent"
        type="button"
        onclick={toggleExpanded}
    >
        {#if row.getIsExpanded()}
            <i class="fa-regular fa-square-minus"></i>
        {:else}
            <i class="fa-regular fa-square-plus"></i>
        {/if}
    </button>
{/if}
