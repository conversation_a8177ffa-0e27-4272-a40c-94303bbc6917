<script lang="ts">
    interface Props {
        webHost: WebHost;
    }

    let { webHost }: Props = $props();
</script>

{#if webHost.urls.length > 1}
    <button
        class="btn btn-sm btn-transparent"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="[data-web-host-id='{webHost.id}']"
        aria-expanded="false"
    >
        <i class="fa-regular fa-square-plus" data-toggle></i>
        <i class="fa-regular fa-square-minus" data-toggle></i>
    </button>
{/if}
