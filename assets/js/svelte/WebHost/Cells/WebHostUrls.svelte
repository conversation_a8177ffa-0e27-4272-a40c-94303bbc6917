<script lang="ts">
    interface Props {
        webHost: WebHost;
    }

    let { webHost }: Props = $props();

    const maxUrls = 2;
    let displayedUrls = $derived(webHost.urls.slice(0, maxUrls));
    let remainingUrls = $derived(webHost.urls.length - maxUrls);
</script>

<div>
    {#each displayedUrls as url}
        <a target="_blank" href={url.url}>{url.url}</a><br>
    {/each}
    {#if remainingUrls > 0}
        ... et {remainingUrls} autre{remainingUrls > 1 ? 's' : ''}
    {/if}
</div>
