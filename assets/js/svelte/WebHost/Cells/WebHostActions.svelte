<script lang="ts">
	interface Props {
		webHost: WebHost;
	}

	let { webHost }: Props = $props();

	const confirmDelete = () => {
		return confirm('Voulez-vous vraiment supprimer cet hébergement ?');
	};
</script>

<div class="d-flex gap-1">
	<div class="text-nowrap text-end">
		<a href="/webHosts/edit/{webHost.id}" class="btn btn-sm btn-info text-white" title="Modifier">
			<i class="fa-regular fa-pen"></i>
		</a>
		<a
			href="/webHosts/delete/{webHost.id}"
			class="btn btn-sm btn-danger text-white"
			onclick={confirmDelete}
			title="Supprimer"
		>
			<i class="fa-solid fa-trash-can"></i>
		</a>
	</div>
</div>
