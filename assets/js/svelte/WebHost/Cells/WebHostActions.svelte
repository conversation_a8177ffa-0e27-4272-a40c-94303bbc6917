<script lang="ts">
    interface Props {
        webHost: WebHost;
    }

    let { webHost }: Props = $props();

    const confirmDelete = () => {
        return confirm('Voulez-vous vraiment supprimer cet hébergement ?');
    };
</script>

<div class="d-flex gap-1">
    <a href="/webHosts/edit/{webHost.id}" class="text-dark text-decoration-none" title="Modifier">
        <i class="fa-regular fa-pen"></i>
    </a>
    <a 
        href="/webHosts/delete/{webHost.id}" 
        class="text-danger text-decoration-none" 
        onclick={confirmDelete}
        title="Supprimer"
    >
        <i class="fa-solid fa-trash-can"></i>
    </a>
</div>
