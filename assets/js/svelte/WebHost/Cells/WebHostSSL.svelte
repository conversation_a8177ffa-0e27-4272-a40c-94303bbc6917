<script lang="ts">
	interface Props {
		webHost: WebHost;
	}

	let { webHost }: Props = $props();

	let sslStatuses = $derived(() => {
		return webHost.urls
			.map((url) => url.sslCertificateReport.isValid)
			.filter((status) => status !== null && status !== undefined);
	});

	let overallSSLStatus = $derived(() => {
		if (sslStatuses.length === 0) return null;
		return sslStatuses.includes(false) ? 'INVALID' : 'VALID';
	});
</script>

{#if overallSSLStatus}
	<span class="badge bg-white">
		{#if overallSSLStatus === 'INVALID'}
			<i class="fa-solid fa-ban text-danger"></i>
		{:else}
			<i class="fa-solid fa-circle-check text-success"></i>
		{/if}
	</span>
{/if}
