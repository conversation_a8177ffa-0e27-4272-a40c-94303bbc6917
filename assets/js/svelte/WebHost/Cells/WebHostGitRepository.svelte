<script lang="ts">
    interface Props {
        webHost: WebHost;
    }

    let { webHost }: Props = $props();

    let gitlabUrl = $derived(() => {
        if (!webHost.gitlabRemoteUrl) return null;
        
        if (webHost.gitlabRemoteUrl.includes('**********************:')) {
            const remote = webHost.gitlabRemoteUrl.replace('**********************:', '').replace('.git', '');
            return `https://gitlab.alienor.net/${remote}`;
        } else if (webHost.gitlabRemoteUrl.includes('https://gitlab.alienor.net/')) {
            return webHost.gitlabRemoteUrl;
        }
        
        return null;
    });

    let isGitweb = $derived(() => {
        return webHost.gitlabRemoteUrl && (
            webHost.gitlabRemoteUrl.includes('ssh://<EMAIL>') ||
            webHost.gitlabRemoteUrl.includes('ssh://<EMAIL>')
        );
    });

    let title = $derived(() => {
        if (!webHost.gitlabRemoteUrl) return '';
        return `${webHost.gitlabRemoteUrl}:${webHost.gitlabActiveBranch || ''}`;
    });
</script>

{#if webHost.gitlabRemoteUrl}
    {#if gitlabUrl}
        <a target="_blank" class="d-flex align-items-center gap-2" href={gitlabUrl} title={title}>
            <img src="/images/gitlab.png" width="16px" height="16px" alt="Gitlab"> Gitlab
        </a>
    {:else if isGitweb}
        <span title={title}>Gitweb</span>
    {:else}
        {webHost.gitlabRemoteUrl}
    {/if}
{/if}
