import axios from 'axios';
import { formatBytes } from './utils';

const client = axios.create({
	baseURL: '/api',
	timeout: 10000
});

export async function getServices(
	force = false
): Promise<{ services: Service[]; errors: object[] }> {
	const { data } = await client.get<{ services: Service[]; errors: object[] }>('/services', {
		params: { force },
		timeout: 30000
	});
	return data;
}

export async function getWebHosts(): Promise<{ webHosts: WebHost[] }> {
	const { data } = await client.get<{ webHosts: WebHost[] }>("/webHosts/data");
	return data;
}

export async function getDetails(
	serverName: string,
	service: string
): Promise<{ details: Details }> {
	const { data } = await client.get<{ details: Details }>(`/details/${serverName}/${service}`);
	return data;
}

export async function redeployService(
	serverName: string,
	serviceId: string,
	tag?: string
): Promise<{ details: Details }> {
	const { data } = await client.get<{ details: Details }>(`/redeploy/${serverName}/${serviceId}`, {
		params: { tag }
	});
	return data;
}

export async function redeployServices(
	services: { serverName: string; serviceId: string }[],
	tag?: string
): Promise<{ status: string }> {
	const { data } = await client.post<{ status: string }>(`/redeploy`, services, {
		params: { tag }
	});
	return data;
}

export async function refreshServices(
	services: { serverName: string; serviceId: string }[]
): Promise<{ status: string }> {
	const { data } = await client.post<{ status: string }>(`/refresh`, services);
	return data;
}

export async function getService(serverName: string, serviceId: string): Promise<Service> {
	const { data } = await client.get<Service>(`/services/${serverName}/${serviceId}`);
	return data;
}

export async function getTags(image: string): Promise<string[]> {
	const { data } = await client.get<{ tags: string[] }>(`/registry/tags/${image}`);
	return data.tags;
}

export async function getComposerUsage(dependency: ComposerDependency): Promise<string[]> {
	const { data } = await client.post<string[]>(`/composer-usage`, {
		package: dependency.name,
		version: dependency.version
	});
	return data;
}

export async function downloadCsv(ids: string[]): Promise<string> {
	const { data } = await client.post<string>(`/download-csv`, {
		ids
	});
	return data;
}

export default client;
