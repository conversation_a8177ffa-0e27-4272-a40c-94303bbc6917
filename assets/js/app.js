const $ = require('jquery');
Window.prototype.$ = $;
global.$ = global.jQuery = $;

require('jquery-highlight');
require('../../public/js/foundation-6.4.2');
require('lodash');
require('jquery-ui');
require('jquery-ui/ui/i18n/datepicker-fr');
require('svg4everybody');
import '../../public/js/vendor/multi-select-master/js/jquery.multi-select';
require('../../public/js/vendor/jquery.quicksearch');

// provoque erreur de compilation donc désactivé (à priori plus utilisé).
//require('../../public/js/vendor/moment-with-locales');

require('moment');
require('blockui');
require('babel-polyfill');
require('harmony-reflect');

//require('../../public/js/vendor/object-path');
require('object-path');

//require('../../public/js/vendor/schemaobject');
require('schema-object');

const SchemaObjectLink = require('../../public/js/src/schema-object-link');
global.SchemaObjectLink = SchemaObjectLink;
require('../../public/js/vendor/jquery.validate.min');
const Mustache = require('../../public/js/vendor/mustache.min');
global.Mustache = Mustache;
require('../../public/js/vendor/clipboard.min');

const Routing = require('fos-router');
global.Routing = Routing;
const Translator = require('../../public/bundles/bazingajstranslation/js/translator.min');
global.Translator = Translator;
require('../../public/js/src/utils');

const API = require('../../public/js/src/api.js');
global.API = API;

require('../../public/js/src/form-builder');
require('../../public/js/src/requeteur');
require('../../public/js/src/modal');
require('../../public/js/common');
require('../../public/js/menu');

/* 
//require('../../public/js/vendor/multi-select-master/js/jquery.multi-select');

 */