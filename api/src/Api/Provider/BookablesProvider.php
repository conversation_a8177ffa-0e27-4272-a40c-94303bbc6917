<?php

namespace App\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\Pagination\TraversablePaginator;
use ApiPlatform\State\ProviderInterface;
use App\Api\Resource\Bookable\BookableResource;
use App\Core\Bookable\Application\DTO\BookableDTO;
use App\Core\Bookable\Application\UseCase\SearchBookable\SearchBookableHandler;

class BookablesProvider implements ProviderInterface
{
    public function __construct(
        private readonly SearchBookableHandler $useCase,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $resourceFQCN = $operation->getClass();
        assert(is_subclass_of($resourceFQCN, BookableResource::class));

        $currentPage = $context['filters']['page'] ?? 1;
        $itemsPerPage = $operation->getPaginationItemsPerPage();

        $searchBookableOutputDTO = $this->useCase->__invoke($resourceFQCN::toSearchBookableCommand($currentPage, $itemsPerPage));
        $resources = array_map(fn (BookableDTO $bookableDTO) => $resourceFQCN::fromBookableDTO($bookableDTO), $searchBookableOutputDTO->bookableDTOs);

        return new TraversablePaginator(
            new \ArrayIterator($resources),
            $currentPage,
            $itemsPerPage,
            $searchBookableOutputDTO->numberOfBookables
        );
    }
}
