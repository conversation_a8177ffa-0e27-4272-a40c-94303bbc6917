<?php

namespace App\Api\Resource\TimeSlot;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\GetCollection;
use App\Api\Filter\Booking\BookableIdentifiers\RoomIdentifiersFilter;
use App\Api\Filter\Booking\BookableMinCapacity\MinRoomCapacityFilter;
use App\Api\Filter\Booking\MaxEndTimeFilter;
use App\Api\Filter\Booking\MinStartTimeFilter;
use App\Api\Filter\Booking\WeekNumberFilter;
use App\Api\Filter\Booking\WeekYearFilter;
use App\Api\Provider\TimeSlotsProvider;
use App\Core\TimeSlot\Application\UseCase\SearchTimeSlot\SearchRoomTimeSlotCommand;

#[ApiResource(
    uriTemplate: '/rooms',
    shortName: 'TimeSlot',
    operations: [
        new GetCollection(
            openapiContext: ['summary' => 'Search rooms available time slots'],
            paginationEnabled: false,
            filters: [
                WeekNumberFilter::class,
                WeekYearFilter::class,
                MinStartTimeFilter::class,
                MaxEndTimeFilter::class,
                RoomIdentifiersFilter::class,
                MinRoomCapacityFilter::class,
            ],
            name: 'roomTimeSlotSearch',
            provider: TimeSlotsProvider::class,
        ),
    ],
    routePrefix: '/api/time-slots',
    normalizationContext: ['groups' => ['read']],
)]
class RoomTimeSlotResource extends TimeSlotResource
{
    public static function toSearchTimeSlotCommand(array $filters): SearchRoomTimeSlotCommand
    {
        return new SearchRoomTimeSlotCommand(
            weekNumber: $filters['weekNumber'],
            weekYear: $filters['weekYear'],
            minStartTime: $filters['minStartTime'] ?? null,
            maxEndTime: $filters['maxEndTime'] ?? null,
            minCapacity: $filters['minRoomCapacity'] ?? null,
            roomIdentifiers: isset($filters['roomIdentifiers']) ? explode(',', $filters['roomIdentifiers']) : null,
        );
    }
}
