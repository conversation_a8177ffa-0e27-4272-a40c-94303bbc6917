<?php

namespace App\Api\Resource\TimeSlot;

use App\Core\TimeSlot\Domain\Entity\ToBook;
use Symfony\Component\Serializer\Annotation\Groups;

class ToBookResource
{
    public function __construct(
        #[Groups(groups: ['read'])]
        public string $id,
        #[Groups(groups: ['read'])]
        public string $name,
        #[Groups(groups: ['read'])]
        public string $color,
        #[Groups(groups: ['read'])]
        public ?string $pictureUrl = null,
    ) {
    }

    public static function fromToBook(ToBook $roomToBook): self
    {
        return new self($roomToBook->id, $roomToBook->name, $roomToBook->color, $roomToBook->pictureUrl);
    }
}
