<?php

namespace App\Api\Processor\Bookable;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use App\Api\Resource\Bookable\BookableResource;
use App\Core\Bookable\Application\UseCase\UpdateBookable\UpdateBookableCommand;
use App\Core\Bookable\Application\UseCase\UpdateBookable\UpdateBookableHandler;
use App\Core\Bookable\Domain\Exception\AccessDenied\NotGrantedToUpdateBookable;
use App\Core\Bookable\Domain\Exception\BookableNotFound;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Webmozart\Assert\Assert;

class UpdateBookableProcessor implements ProcessorInterface
{
    public function __construct(
        private readonly UpdateBookableHandler $updateUserHandler,
    ) {
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): BookableResource
    {
        $bookableId = $uriVariables['id'];

        Assert::isInstanceOf($data, BookableResource::class);
        /** @var BookableResource $bookableResource */
        $bookableResource = $data;

        try {
            $bookableDTO = $this->updateUserHandler->__invoke(new UpdateBookableCommand(
                id: $bookableId,
                name: $bookableResource->name,
                color: $bookableResource->color,
                capacity: $bookableResource->capacity,
            ));
        } catch (BookableNotFound $e) {
            throw new NotFoundHttpException($e->getMessage());
        } catch (NotGrantedToUpdateBookable $e) {
            throw new AccessDeniedHttpException($e->getMessage());
        }

        return $bookableResource::class::fromBookableDTO($bookableDTO);
    }
}
