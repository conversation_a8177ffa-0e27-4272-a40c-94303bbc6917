<?php

declare(strict_types=1);

namespace App\Infrastructure\Database\Repository;

use App\Core\Bookable\Domain\Bookable;
use App\Core\Bookable\Domain\Repository\BookableRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Persistence\ObjectRepository;

final readonly class DatabaseBookableRepository implements BookableRepository
{
    private ObjectRepository $doctrineRepository;
    private ObjectManager $entityManager;

    public function __construct(
        ManagerRegistry $registry,
    ) {
        $this->doctrineRepository = $registry->getRepository(Bookable::class);
        $this->entityManager = $registry->getManager();
    }

    public function add(Bookable $bookable): void
    {
        $this->entityManager->persist($bookable);
        $this->entityManager->flush();
    }

    public function get(string $id): ?Bookable
    {
        return $this->doctrineRepository->find($id);
    }

    public function count(): int
    {
        return $this->doctrineRepository->count([]);
    }

    public function countByType(string $type): int
    {
        return $this->doctrineRepository->count(['type' => $type]);
    }

    public function findByType($type)
    {
        return $this->doctrineRepository->findBy(['type' => $type]);
    }

    public function update(Bookable $bookable): void
    {
        $this->entityManager->persist($bookable);
        $this->entityManager->flush();
    }

    public function delete(Bookable $bookable): void
    {
        $this->entityManager->remove($bookable);
        $this->entityManager->flush();
    }

    /** @return Bookable[] */
    public function search(int $page, int $itemsPerPage, string $type): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('b')
            ->from(Bookable::class, 'b')
            ->where('b.type = :type')
            ->setParameter('type', $type)
            ->addOrderBy('b.name.value', 'ASC')
            ->setFirstResult(($page - 1) * $itemsPerPage)
            ->setMaxResults($itemsPerPage)
            ->getQuery()
            ->getResult();
    }
}
