<?php

namespace App\Infrastructure\Symfony\EventSubscriber;

use App\Core\Bookable\Domain\Event\BookableCapacityChanged;
use App\Core\Bookable\Domain\Event\BookableColorChanged;
use App\Core\Bookable\Domain\Event\BookablePictureChanged;
use App\Core\Bookable\Domain\Event\BookableRemoved;
use App\Core\Bookable\Domain\Event\BookableRenamed;
use App\Core\Booking\Domain\Booking;
use App\Core\Booking\Domain\Service\SynchronizeBookedPicture;
use App\Core\User\Domain\Event\UserProfilePictureChanged;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class BookedEventSubscriber implements SynchronizeBookedPicture, EventSubscriberInterface
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            UserProfilePictureChanged::class => 'onUserProfilePictureChanged',
            BookablePictureChanged::class => 'onBookablePictureChanged',
            BookableRenamed::class => 'onBookableRenamed',
            BookableColorChanged::class => 'onBookableColorChanged',
            BookableCapacityChanged::class => 'onBookableCapacityChanged',
            BookableRemoved::class => 'onBookableRemoved',
        ];
    }

    public function onUserProfilePictureChanged(UserProfilePictureChanged $event): void
    {
        $this->updateOwnerProfilePicture($event->userId, $event->profilePicture);
    }

    public function onBookablePictureChanged(BookablePictureChanged $event): void
    {
        $this->updateBookedPicture($event->bookableId, $event->picture);
    }

    public function onBookableRenamed(BookableRenamed $event): void
    {
        $this->updateBookedName($event->bookableId, $event->name);
    }

    public function onBookableColorChanged(BookableColorChanged $event): void
    {
        $this->updateBookedColor($event->bookableId, $event->color);
    }

    public function onBookableCapacityChanged(BookableCapacityChanged $event): void
    {
        $this->updateBookedCapacity($event->bookableId, $event->capacity);
    }

    public function onBookableRemoved(BookableRemoved $event): void
    {
        $this->removeBookableRelatedBookings($event->bookableId);
    }

    public function updateOwnerProfilePicture(string $ownerId, string $profilePicture): void
    {
        $this->entityManager->getRepository(Booking::class)
            ->createQueryBuilder('b')
            ->update()
            ->set('b.owner.profilePicture', ':profilePicture')
            ->where('b.owner.id = :ownerId')
            ->setParameter('ownerId', $ownerId, 'ulid')
            ->setParameter('profilePicture', $profilePicture)
            ->getQuery()
            ->execute();
    }

    public function updateBookedPicture(string $bookedId, string $picture): void
    {
        $this->entityManager->getRepository(Booking::class)
            ->createQueryBuilder('b')
            ->update()
            ->set('b.booked.picture', ':picture')
            ->where('b.booked.id = :bookedId')
            ->setParameter('bookedId', $bookedId, 'ulid')
            ->setParameter('picture', $picture)
            ->getQuery()
            ->execute();
    }

    private function updateBookedName(string $bookedId, string $name): void
    {
        $this->entityManager->getRepository(Booking::class)
            ->createQueryBuilder('b')
            ->update()
            ->set('b.booked.name', ':name')
            ->where('b.booked.id = :bookedId')
            ->setParameter('bookedId', $bookedId, 'ulid')
            ->setParameter('name', $name)
            ->getQuery()
            ->execute();
    }

    private function updateBookedColor(string $bookedId, string $color): void
    {
        $this->entityManager->getRepository(Booking::class)
            ->createQueryBuilder('b')
            ->update()
            ->set('b.booked.color', ':color')
            ->where('b.booked.id = :bookedId')
            ->setParameter('bookedId', $bookedId, 'ulid')
            ->setParameter('color', $color)
            ->getQuery()
            ->execute();
    }

    private function updateBookedCapacity(string $bookedId, int $capacity): void
    {
        $this->entityManager->getRepository(Booking::class)
            ->createQueryBuilder('b')
            ->update()
            ->set('b.booked.capacity', ':capacity')
            ->where('b.booked.id = :bookedId')
            ->setParameter('bookedId', $bookedId, 'ulid')
            ->setParameter('capacity', $capacity)
            ->getQuery()
            ->execute();
    }

    private function removeBookableRelatedBookings(string $bookedId)
    {
        $this->entityManager->getRepository(Booking::class)
            ->createQueryBuilder('b')
            ->delete()
            ->where('b.booked.id = :bookedId')
            ->setParameter('bookedId', $bookedId, 'ulid')
            ->getQuery()
            ->execute();
    }
}
