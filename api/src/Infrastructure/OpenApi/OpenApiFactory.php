<?php

namespace App\Infrastructure\OpenApi;

use ApiPlatform\OpenApi\Factory\OpenApiFactoryInterface;
use ApiPlatform\OpenApi\OpenApi;
use Lexik\Bundle\JWTAuthenticationBundle\OpenApi\OpenApiFactory as JWTAuthenticationBundleOpenApiFactory;
use Symfony\Component\DependencyInjection\Attribute\AsDecorator;
use Symfony\Component\HttpFoundation\Response;

#[AsDecorator('coop_tilleuls_forgot_password.openapi.factory')]
readonly class OpenApiFactory implements OpenApiFactoryInterface
{
    public function __construct(
        private OpenApiFactoryInterface $decorated,
    ) {
    }

    public function __invoke(array $context = []): OpenApi
    {
        $authPath = '/api/auth';
        $jwtOpenApi = new JWTAuthenticationBundleOpenApiFactory($this->decorated, $authPath, 'email', 'password');
        $openApi = $jwtOpenApi();

        //        $openApi = $this->decorated->__invoke($context);
        $paths = $openApi->getPaths();

        // Surcharge de la route d'authentification généré par LexikJWT
        $authPost = $paths->getPath($authPath)->getPost();
        $authResponse = $authPost->getResponses()['200'];
        $paths->addPath($authPath, $paths->getPath($authPath)
            ->withPost(
                $authPost
                    ->withTags(['Authentication'])
                    ->withOperationId('auth')
                    ->withResponses([
                        Response::HTTP_OK => [
                            'description' => 'Return JSON Web Token',
                            'content' => [
                                'application/ld+json' => $authResponse['content']['application/json'],
                            ],
                        ],
                    ])
            )
        );

        // Surcharge du schema de reset de mot de passe pour inclure la validation
        $schemas = $openApi->getComponents()->getSchemas();
        $schemas['ForgotPassword:reset']['properties']['newPassword'] = $schemas['Authentication.Signup.jsonld']['properties']['password'];

        return $openApi;
    }
}
