<?php

declare(strict_types=1);

namespace App\Core\Common\Domain\ValueObject;

use Symfony\Component\Uid\Ulid as SymfonyUlid;

abstract class Ulid implements \Stringable
{
    private function __construct(protected string $value)
    {
        $this->ensureIsValidUlid($value);
    }

    public static function generate(): static
    {
        return new static(SymfonyUlid::generate());
    }

    public static function fromString(string $value): static
    {
        return new static($value);
    }

    public function value(): string
    {
        return $this->value;
    }

    public function equals(mixed $other): bool
    {
        return (string) $this === (string) $other;
    }

    public function __toString(): string
    {
        return $this->value();
    }

    private function ensureIsValidUlid(string $id): void
    {
        if (!SymfonyUlid::isValid($id)) {
            throw new \InvalidArgumentException(sprintf('The uuid "%s" is not valid.', $id));
        }
    }
}
