<?php

namespace App\Core\TimeSlot\Domain\ValueObject\ReservedTimeSlotFilters;

class ReservedVehicleTimeSlotFilters extends ReservedTimeSlotFilters
{
    public function __construct(
        int $weekNumber,
        int $weekYear,
        ?string $minStartTime = null,
        ?string $maxEndTime = null,
        ?int $minCapacity = null,
        private readonly ?array $vehicleIdentifiers = null,
    ) {
        parent::__construct($weekNumber, $weekYear, $minStartTime, $maxEndTime, $minCapacity);
    }

    public function maxEndDate(): ?\DateTimeImmutable
    {
        return $this->minStartDate()->modify('next Sunday')->setTime(23, 59, 59);
    }

    public function vehicleIdentifiers(): ?array
    {
        return $this->vehicleIdentifiers;
    }
}
