<?php

namespace App\Core\TimeSlot\Application\DTO;

use App\Core\TimeSlot\Domain\Entity\ToBook;
use App\Core\TimeSlot\Domain\TimeSlot;

class TimeSlotDTO
{
    public function __construct(
        public \DateTimeImmutable $startDate,
        public \DateTimeImmutable $endDate,
        public ToBook $toBook,
    ) {
    }

    public static function fromTimeSlot(TimeSlot $timeSlot): self
    {
        return new self(
            $timeSlot->startDate(),
            $timeSlot->endDate(),
            $timeSlot->toBook(),
        );
    }
}
