<?php

namespace App\Core\Booking\Application\UseCase\UpdateBooking;

use App\Core\Booking\Domain\BookingInterface;
use App\Core\Booking\Domain\Exception\AlreadyBookedAtPeriod\RoomAlreadyBookedAtPeriod;
use App\Core\Booking\Domain\Exception\BookedNotFound\RoomNotFound;
use App\Core\Booking\Domain\RoomBooking;
use App\Core\Booking\Domain\ValueObject\MeetingType;

final readonly class UpdateRoomBookingCommand implements UpdateBookingCommandInterface
{
    public function __construct(
        public string $bookingId,
        public string $applicantId,
        public string $applicantFirstname,
        public string $applicantLastname,
        public ?string $bookable = null,
        public ?\DateTimeImmutable $startDate = null,
        public ?\DateTimeImmutable $endDate = null,
        public ?int $attendees = null,
        public ?string $reason = null,
        public ?string $meetingType = null,
    ) {
    }

    public function bookedType(): string
    {
        return 'room';
    }

    public function bookingId(): string
    {
        return $this->bookingId;
    }

    public function applicantId(): string
    {
        return $this->applicantId;
    }

    public function applicantFirstname(): string
    {
        return $this->applicantFirstname;
    }

    public function applicantLastname(): string
    {
        return $this->applicantLastname;
    }

    public function bookableId(): ?string
    {
        return $this->bookable;
    }

    public function startDate(): ?\DateTimeImmutable
    {
        return $this->startDate;
    }

    public function endDate(): ?\DateTimeImmutable
    {
        return $this->endDate;
    }

    public function reason(): ?string
    {
        return $this->reason;
    }

    public function attendees(): ?string
    {
        return $this->attendees;
    }

    public function meetingType(): ?string
    {
        return $this->meetingType;
    }

    /**
     * @throws RoomAlreadyBookedAtPeriod
     */
    public function throwAlreadyBookedAtPeriodException(string $bookableName): void
    {
        throw new RoomAlreadyBookedAtPeriod($bookableName);
    }

    /**
     * @throws RoomNotFound
     */
    public function throwBookedNotFoundException(string $bookableId): void
    {
        throw new RoomNotFound($bookableId);
    }

    public function updateBookingExtraFields(RoomBooking|BookingInterface $booking): RoomBooking
    {
        // nombre de participants
        if ($this->attendees()) {
            if ($booking->attendees() !== $this->attendees()) {
                $booking->changeAttendees($this->attendees());
            }
        }

        // type de réunion
        if ($this->meetingType()) {
            if (!$booking->type()->isEqualTo($this->meetingType())) {
                $booking->changeMeetingType(MeetingType::from($this->meetingType()));
            }
        }

        return $booking;
    }
}
