<?php

namespace App\Core\Booking\Application\UseCase\CreateBooking;

use App\Core\Booking\Domain\Booking;
use App\Core\Booking\Domain\Entity\Booked;
use App\Core\Booking\Domain\Entity\Owner;
use App\Core\Booking\Domain\Exception\AlreadyBookedAtPeriod\RoomAlreadyBookedAtPeriod;
use App\Core\Booking\Domain\Exception\BookedNotFound\RoomNotFound;
use App\Core\Booking\Domain\RoomBooking;
use App\Core\Booking\Domain\ValueObject\MeetingType;

final readonly class CreateRoomBookingCommand implements CreateBookingCommandInterface
{
    public function __construct(
        public string $bookable,
        public \DateTimeImmutable $startDate,
        public \DateTimeImmutable $endDate,
        public int $attendees,
        public string $reason,
        public string $type,
        public bool $sendCalendarInvitation,
        public string $ownerId,
    ) {
    }

    public function toBooking(Owner $user, Booked $bookable, ?MeetingType $type = null): Booking
    {
        return new RoomBooking(
            $bookable,
            $user,
            $this->startDate,
            $this->endDate,
            $type,
            $this->reason,
            $this->attendees
        );
    }

    public function bookableId(): string
    {
        return $this->bookable;
    }

    public function bookedType(): string
    {
        return 'room';
    }

    public function ownerId(): string
    {
        return $this->ownerId;
    }

    public function type(): ?string
    {
        return $this->type;
    }

    public function startDate(): \DateTimeImmutable
    {
        return $this->startDate;
    }

    public function endDate(): \DateTimeImmutable
    {
        return $this->endDate;
    }

    public function sendCalendarInvitation(): bool
    {
        return $this->sendCalendarInvitation;
    }

    /**
     * @throws RoomAlreadyBookedAtPeriod
     */
    public function throwAlreadyBookedAtPeriodException(string $bookableName): void
    {
        throw new RoomAlreadyBookedAtPeriod($bookableName);
    }

    /**
     * @throws RoomNotFound
     */
    public function throwBookedNotFoundException(string $bookableId): void
    {
        throw new RoomNotFound($bookableId);
    }
}
