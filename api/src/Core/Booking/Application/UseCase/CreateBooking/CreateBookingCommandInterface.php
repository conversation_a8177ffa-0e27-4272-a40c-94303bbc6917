<?php

namespace App\Core\Booking\Application\UseCase\CreateBooking;

use App\Core\Booking\Domain\Booking;
use App\Core\Booking\Domain\Entity\Booked;
use App\Core\Booking\Domain\Entity\Owner;
use App\Core\Booking\Domain\Exception\AlreadyBookedAtPeriod\AlreadyBookedAtPeriod;
use App\Core\Booking\Domain\Exception\BookedNotFound\BookedNotFound;
use App\Core\Booking\Domain\ValueObject\MeetingType;

interface CreateBookingCommandInterface
{
    public function toBooking(Owner $user, Booked $bookable, ?MeetingType $type = null): Booking;

    public function bookableId(): string;

    public function bookedType(): string;

    public function ownerId(): string;

    public function type(): ?string;

    public function startDate(): \DateTimeImmutable;

    public function endDate(): \DateTimeImmutable;

    public function sendCalendarInvitation(): bool;

    /**
     * @throws AlreadyBookedAtPeriod
     */
    public function throwAlreadyBookedAtPeriodException(string $bookableName): void;

    /**
     * @throws BookedNotFound
     */
    public function throwBookedNotFoundException(string $bookableId): void;
}
