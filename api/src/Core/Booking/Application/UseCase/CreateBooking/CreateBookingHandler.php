<?php

namespace App\Core\Booking\Application\UseCase\CreateBooking;

use App\Core\Booking\Application\DTO\BookingDTO;
use App\Core\Booking\Domain\BookingRepository;
use App\Core\Booking\Domain\Entity\Booked;
use App\Core\Booking\Domain\Entity\Owner;
use App\Core\Booking\Domain\Exception\AlreadyBookedAtPeriod\AlreadyBookedAtPeriod;
use App\Core\Booking\Domain\Exception\BookedNotFound\BookedNotFound;
use App\Core\Booking\Domain\Exception\OwnerNotFound;
use App\Core\Booking\Domain\Service\BookedProvider;
use App\Core\Booking\Domain\Service\CalendarInvitationSender;
use App\Core\Booking\Domain\Service\OwnerProvider;
use App\Core\Booking\Domain\ValueObject\MeetingType;
use App\Core\Booking\Domain\ValueObject\Period;
use Symfony\Component\Clock\DatePoint;

class CreateBookingHandler
{
    public function __construct(private readonly BookingRepository $bookingRepository, private readonly BookedProvider $bookedProvider, private readonly OwnerProvider $ownerProvider, private readonly CalendarInvitationSender $calendarInvitationSender)
    {
    }

    /**
     * @throws AlreadyBookedAtPeriod
     * @throws OwnerNotFound
     * @throws BookedNotFound
     */
    public function __invoke(CreateBookingCommandInterface $command): BookingDTO
    {
        $booked = $this->getBooked($command);
        $this->ensurePeriodIsValid($command->startDate(), $command->endDate());
        $this->ensureBookableIsAvailableForPeriod($command, $booked);
        $owner = $this->getOwner($command->ownerId());

        $booking = $command->toBooking($owner, $booked, $command->type() ? MeetingType::from($command->type()) : null);
        $this->bookingRepository->add($booking);

        if ($command->sendCalendarInvitation()) {
            $this->calendarInvitationSender->inviteOwnerForBooking($booking);
        }

        return BookingDTO::fromEntity($booking);
    }

    /**
     * @throws AlreadyBookedAtPeriod
     */
    private function ensureBookableIsAvailableForPeriod(CreateBookingCommandInterface $command, $booked): void
    {
        if ($this->bookingRepository->findForBookedAtPeriod($command->bookableId(), $command->startDate(), $command->endDate())) {
            $command->throwAlreadyBookedAtPeriodException($booked->name);
        }
    }

    /**
     * @throws \InvalidArgumentException
     */
    private function ensurePeriodIsValid(\DateTimeImmutable $startDate, \DateTimeImmutable $endDate): void
    {
        $period = new Period($startDate, $endDate);
        $now = new DatePoint();
        $minutes = (int) $now->format('i');
        $hour = (int) $now->format('H');
        $minStartDate = $minutes < 30 ? $now->modify($hour.':00:00') : $now->modify($hour.':30:00');
        if ($period->startDate < $minStartDate) {
            throw new \InvalidArgumentException('La date de début doit être dans le futur ou dans la demi-heure en cours');
        }
        // TODO : supprimer le 2 janvier 2025
        if ($period->startDate < new DatePoint('2025-01-02')) {
            throw new \InvalidArgumentException("Pour rappel, les réservations sur cet outil ne sont ouvertes qu'à partir du 2 Janvier 2025");
        }
    }

    /**
     * @throws BookedNotFound
     */
    private function getBooked(CreateBookingCommandInterface $command): Booked
    {
        $booked = $this->bookedProvider->get($command->bookableId(), $command->bookedType());
        if (null === $booked) {
            $command->throwBookedNotFoundException($command->bookableId());
        }

        return $booked;
    }

    /**
     * @throws OwnerNotFound
     */
    private function getOwner(string $ownerId): Owner
    {
        $owner = $this->ownerProvider->get($ownerId);
        if (null === $owner) {
            throw new OwnerNotFound($ownerId);
        }

        return $owner;
    }
}
