<?php

namespace App\Core\Booking\Domain\ValueObject\BookingFilters;

class RoomBookingFilters extends BookingFilters
{
    public function __construct(
        int $weekNumber,
        int $weekYear,
        ?string $minStartTime = null,
        ?string $maxEndTime = null,
        ?int $minCapacity = null,
        ?array $ownerIdentifiers = null,
        private readonly ?array $roomIdentifiers = null,
    ) {
        parent::__construct($weekNumber, $weekYear, $minStartTime, $maxEndTime, $minCapacity, $ownerIdentifiers);
    }

    public function roomIdentifiers(): ?array
    {
        return $this->roomIdentifiers;
    }
}
