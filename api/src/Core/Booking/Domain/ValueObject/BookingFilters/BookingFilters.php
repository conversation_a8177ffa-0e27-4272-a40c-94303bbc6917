<?php

namespace App\Core\Booking\Domain\ValueObject\BookingFilters;

abstract class BookingFilters
{
    public function __construct(
        private readonly int $weekNumber,
        private readonly int $weekYear,
        private readonly ?string $minStartTime = null,
        private readonly ?string $maxEndTime = null,
        private readonly ?int $minCapacity = null,
        private readonly ?array $ownerIdentifiers = null,
    ) {
    }

    public function minStartDate(): ?\DateTimeImmutable
    {
        return (new \DateTimeImmutable())->setISODate($this->weekYear, $this->weekNumber)->setTime(0, 0, 0);
    }

    public function maxEndDate(): ?\DateTimeImmutable
    {
        return $this->minStartDate()->modify('next Sunday')->setTime(23, 59, 59);
    }

    public function minStartTime(): ?string
    {
        return $this->minStartTime;
    }

    public function maxEndTime(): ?string
    {
        return $this->maxEndTime;
    }

    public function minCapacity(): ?int
    {
        return $this->minCapacity;
    }

    public function ownerIdentifiers(): ?array
    {
        return $this->ownerIdentifiers;
    }
}
