<?php

namespace App\Core\Booking\Domain\ValueObject;

use App\Core\Common\Domain\ValueObject\Equatable;

enum MeetingType: string implements Equatable
{
    case internal = 'internal';
    case external = 'external';

    public function isEqualTo(mixed $other): bool
    {
        if (!$other instanceof self) {
            return false;
        }

        return $this->value === $other->value;
    }
}
