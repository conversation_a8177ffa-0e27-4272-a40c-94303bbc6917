<?php

namespace App\Core\User\Domain;

use App\Core\User\Domain\Entity\ProfilePicture;
use App\Core\User\Domain\Entity\Registration;
use App\Core\User\Domain\ValueObject\Department;
use App\Core\User\Domain\ValueObject\Email;
use App\Core\User\Domain\ValueObject\Firstname;
use App\Core\User\Domain\ValueObject\HashedPassword;
use App\Core\User\Domain\ValueObject\Identity;
use App\Core\User\Domain\ValueObject\Lastname;
use App\Core\User\Domain\ValueObject\Preferences;
use App\Core\User\Domain\ValueObject\Roles;
use Symfony\Component\Uid\Ulid;

class User
{
    private string $id;
    private ?\SplFileInfo $profilePictureFile = null;
    private ?ProfilePicture $profilePicture = null;
    public ?string $profilePictureUrl = null;
    private ?\DateTimeImmutable $profilePictureUpdatedAt = null;

    public function __construct(
        private Firstname $firstname,
        private Lastname $lastname,
        private Email $email,
        private HashedPassword $hashedPassword,
        private readonly Roles $roles,
        private Department $department,
        private ?Preferences $preferences = null,
    ) {
        $this->id = Ulid::generate();
        if (null === $this->preferences) {
            $this->preferences = new Preferences();
        }
    }

    public function id(): string
    {
        return $this->id;
    }

    public function firstname(): Firstname
    {
        return $this->firstname;
    }

    public function lastname(): Lastname
    {
        return $this->lastname;
    }

    public function email(): Email
    {
        return $this->email;
    }

    public function changeEmail(Email $email): void
    {
        $this->email = $email;
    }

    public function hashedPassword(): HashedPassword
    {
        return $this->hashedPassword;
    }

    public function roles(): Roles
    {
        return $this->roles;
    }

    public function department(): Department
    {
        return $this->department;
    }

    public function changeDepartment(Department $department): void
    {
        $this->department = $department;
    }

    public function identity(): Identity
    {
        return new Identity($this->firstname, $this->lastname);
    }

    public function changeIdentity(Identity $identity): void
    {
        $this->firstname = $identity->firstname();
        $this->lastname = $identity->lastname();
    }

    public function changePassword(HashedPassword $newHashedPassword): void
    {
        $this->hashedPassword = $newHashedPassword;
    }

    public function profilePicture(): ?ProfilePicture
    {
        return $this->profilePicture;
    }

    public function changeProfilePicture(?\SplFileInfo $profilePictureFile = null): void
    {
        $this->setProfilePictureFile($profilePictureFile);
    }

    public function preferences(): Preferences
    {
        return $this->preferences;
    }

    public function changePreferences(Preferences $newPreferences): void
    {
        $this->preferences = $newPreferences;
    }

    /**
     * /!\ Nécessaire au bon fonctionnement de l'upload de fichier.
     */
    public function setProfilePictureFile(?\SplFileInfo $profilePictureFile = null): void
    {
        $this->profilePictureFile = $profilePictureFile;

        if (null !== $profilePictureFile) {
            // It is required that at least one field changes if you are using doctrine
            // otherwise the event listeners won't be called and the file is lost
            $this->profilePictureUpdatedAt = new \DateTimeImmutable();
        }
    }

    /**
     * /!\ Nécessaire au bon fonctionnement de l'upload de fichier.
     */
    public function getProfilePictureFile(): ?\SplFileInfo
    {
        return $this->profilePictureFile;
    }

    public static function fromRegistration(Registration $registration): static
    {
        return new self(
            firstname: $registration->firstname(),
            lastname: $registration->lastname(),
            email: $registration->email(),
            hashedPassword: $registration->hashedPassword(),
            roles: Roles::fromArray([]),
            department: $registration->department()
        );
    }
}
