<?php

namespace App\Tests\Fixtures\Repository;

use App\Core\Bookable\Domain\Bookable;
use App\Core\Bookable\Domain\Repository\BookableRepository;
use App\Core\Booking\Domain\Entity\Booked as RoomForBooking;

class InMemoryBookableRepository implements BookableRepository
{
    private array $rooms = [];

    public function add(Bookable $bookable): void
    {
        $this->rooms[(string) $bookable->id()] = $bookable;
    }

    public function getForBooking(string $roomId): RoomForBooking
    {
        $searchResults = array_filter($this->rooms, function ($room) use ($roomId) {
            return (string) $room->id() === $roomId;
        });
        $room = array_shift($searchResults);
        if (null === $room) {
            throw new \Exception('Bookable not found');
        }

        return new RoomForBooking(
            $room->id(),
            $room->name(),
            $room->color(),
            $room->capacity(),
        );
    }

    public function count(): int
    {
        return count($this->rooms);
    }

    public function countByType(string $type): int
    {
        return count(array_filter($this->rooms, fn (Bookable $bookable) => $type === $bookable->type()->value));
    }

    public function get(string $id): Bookable
    {
        return $this->rooms[$id];
    }

    /**
     * @return array<Bookable>
     */
    public function all(): array
    {
        return $this->rooms;
    }

    public function findByType($type)
    {
        return array_filter($this->rooms, fn (Bookable $bookable) => $type === $bookable->type()->value);
    }

    public function update(Bookable $bookable): void
    {
        $this->rooms[$bookable->id()] = $bookable;
    }

    public function delete(Bookable $bookable): void
    {
        unset($this->rooms[$bookable->id()]);
    }

    /** @return Bookable[] */
    public function search(int $page, int $itemsPerPage, string $type): array
    {
        $rooms = array_values($this->rooms);

        // Trie des bookable par nom
        usort($rooms, fn ($a, $b) => strcmp((string) $a->name()->value(), (string) $b->name()->value()));

        return array_slice($rooms, ($page - 1) * $itemsPerPage, $itemsPerPage);
    }
}
