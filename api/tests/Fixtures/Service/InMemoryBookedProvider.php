<?php

namespace App\Tests\Fixtures\Service;

use App\Core\Booking\Domain\Entity\Booked;
use App\Core\Booking\Domain\Service\BookedProvider;
use App\Tests\Fixtures\Repository\InMemoryBookableRepository;

class InMemoryBookedProvider implements BookedProvider
{
    public function __construct(
        private readonly InMemoryBookableRepository $bookableRepository,
    ) {
    }

    public function get(string $bookableId, string $bookedType): ?Booked
    {
        $bookable = $this->bookableRepository->all()[$bookableId] ?? null;
        if (null === $bookable || $bookable->type()->value !== $bookedType) {
            return null;
        }

        return new Booked(
            $bookable->id(),
            (string) $bookable->name(),
            (string) $bookable->color(),
            $bookable->capacity()->value()
        );
    }
}
