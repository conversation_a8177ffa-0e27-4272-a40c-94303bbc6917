<?php

namespace App\Tests\Fixtures\Service;

use App\Core\Booking\Domain\Entity\Owner;
use App\Core\Booking\Domain\Service\OwnerProvider;
use App\Tests\Fixtures\Repository\InMemoryUserRepository;

class InMemoryOwnerProvider implements OwnerProvider
{
    public function __construct(private readonly InMemoryUserRepository $userRepository)
    {
    }

    public function get(string $userId): ?Owner
    {
        $user = $this->userRepository->getById($userId);
        if (null === $user) {
            return null;
        }

        return new Owner($user->id(), $user->firstname(), $user->lastname(), $user->email());
    }
}
