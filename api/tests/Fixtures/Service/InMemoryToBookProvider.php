<?php

namespace App\Tests\Fixtures\Service;

use App\Core\Bookable\Domain\Bookable;
use App\Core\TimeSlot\Domain\Entity\ToBook;
use App\Core\TimeSlot\Domain\Service\ToBookProvider;
use App\Tests\Fixtures\Repository\InMemoryBookableRepository;

readonly class InMemoryToBookProvider implements ToBookProvider
{
    public function __construct(
        private InMemoryBookableRepository $bookableRepository,
    ) {
    }

    /**
     * @return ToBook[]
     */
    public function findByType(string $toBookType): array
    {
        $toBooks = array_filter($this->bookableRepository->all(), fn (Bookable $bookable) => $toBookType === $bookable->type()->value);

        return array_map(function (Bookable $bookable) {
            return new ToBook($bookable->id(), (string) $bookable->name(), (string) $bookable->color(), $bookable->capacity()->value(), $bookable->picture()?->getName() ?? null);
        }, $toBooks);
    }
}
