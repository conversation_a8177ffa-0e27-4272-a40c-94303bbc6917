<?php

namespace App\Tests\Integration\Api\Bookable;

use App\Infrastructure\Database\Repository\DatabaseBookableRepository;
use App\Infrastructure\Security\AuthUser;
use App\Tests\Factory\UserFactory;
use App\Tests\Integration\Api\AbstractApiTestCase;
use PHPUnit\Framework\Attributes\DataProvider;

class CreateBookableTest extends AbstractApiTestCase
{
    private DatabaseBookableRepository $bookableRepository;
    private AuthUser $authUser;

    protected function setUp(): void
    {
        parent::setUp();
        $this->bookableRepository = self::getContainer()->get(DatabaseBookableRepository::class);
        $this->authUser = UserFactory::anAdmin()->createAsAuthUser();
    }

    public function test_can_create_room(): void
    {
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookables/rooms', ['json' => [
                'name' => 'Bordeaux',
                'color' => '#FF4141',
                'capacity' => 10,
            ]]
            )
            ->assertStatus(201)
        ;
        $count = $this->bookableRepository->count(['name.value' => 'Bordeaux']);
        $this->assertEquals(1, $count);
    }

    public function test_can_create_vehicle(): void
    {
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookables/vehicles', ['json' => [
                'name' => 'Jumpy',
                'color' => '#FF4141',
                'capacity' => 2,
            ]]
            )
            ->assertStatus(201)
        ;
        $count = $this->bookableRepository->count(['name.value' => 'Jumpy']);
        $this->assertEquals(1, $count);
    }

    public static function invalidBookableCapacityProvider()
    {
        yield 'room capacity is null' => ['rooms', null, 400];
        yield 'room capacity is empty' => ['rooms', '', 400];
        yield 'room capacity is not a number' => ['rooms', 'foo', 400];
        yield 'room capacity is negative' => ['rooms', -1, 422];

        yield 'vehicle capacity is null' => ['vehicles', null, 400];
        yield 'vehicle capacity is empty' => ['vehicles', '', 400];
        yield 'vehicle capacity is not a number' => ['vehicles', 'foo', 400];
        yield 'vehicle capacity is negative' => ['vehicles', -1, 422];
    }

    #[DataProvider('invalidBookableCapacityProvider')]
    public function test_cannot_create_bookable_with_invalid_capacity($endpoint, $capacity, $expectedStatus): void
    {
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookables/'.$endpoint, ['json' => [
                'name' => 'Nom du réservable',
                'color' => '#FF4141',
                'capacity' => $capacity,
            ]]
            )
            ->assertStatus($expectedStatus)
        ;
    }

    public function test_non_admin_cannot_create_room(): void
    {
        $user = UserFactory::aUser()->createAsAuthUser();
        $this->browser()
            ->actingAs($user)
            ->post('/api/bookables/rooms', ['json' => [
                'name' => 'Bordeaux',
                'color' => '#FF4141',
                'capacity' => 10,
            ]]
            )
            ->assertStatus(403)
            ->assertJsonContains([
                'detail' => "Vous n'avez pas la permission d'accéder à cette ressource",
            ]);

        $count = $this->bookableRepository->count();
        $this->assertEquals(0, $count);
    }
}
