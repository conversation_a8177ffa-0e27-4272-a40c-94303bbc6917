<?php

namespace App\Tests\Integration\Api\User;

use App\Tests\Factory\Booking\RoomBookingFactory;
use App\Tests\Factory\UserFactory;
use App\Tests\Integration\Api\AbstractApiTestCase;

class UpdateUserTest extends AbstractApiTestCase
{
    public function test_can_update_user(): void
    {
        $authUser = UserFactory::anAdmin()->createAsAuthUser();
        $user = UserFactory::aUser()->named('John', 'Doe')->withDepartment('Pole marketing et datas')->create();
        $booking = RoomBookingFactory::new()->ownBy($user)->create();

        $this->browser()
            ->actingAs($authUser)
            ->patch('/api/users/'.$user->id(), [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'firstname' => 'Dave',
                    'lastname' => 'Alienor',
                    'email' => '<EMAIL>',
                    'department' => 'Fidélité',
                ],
            ])
            ->assertSuccessful()
            ->assertJsonContains([
                '@id' => '/api/users/'.$user->id(),
                'id' => $user->id(),
                'firstname' => 'Dave',
                'lastname' => 'Alienor',
                'email' => '<EMAIL>',
                'department' => 'Fidélité',
            ])
        ;

        $booking = RoomBookingFactory::find($booking->id());
        $this->assertEquals('Dave', $booking->owner()->firstname);
        $this->assertEquals('Alienor', $booking->owner()->lastname);
    }

    public function test_non_admin_cannot_update_another_user(): void
    {
        $authUser = UserFactory::aUser()->createAsAuthUser();
        $anotherUser = UserFactory::aUser()->create();

        $this->browser()
            ->actingAs($authUser)
            ->patch('/api/users/'.$anotherUser->id(), [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'firstname' => 'Dave',
                ],
            ])
            ->assertStatus(403)
            ->assertJsonContains([
                'detail' => "Vous n'avez pas la permission d'accéder à cette ressource",
            ]);
    }
}
