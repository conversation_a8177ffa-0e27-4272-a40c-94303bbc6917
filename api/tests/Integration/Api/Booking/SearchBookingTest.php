<?php

namespace App\Tests\Integration\Api\Booking;

use App\Core\Booking\Domain\RoomBooking;
use App\Core\Booking\Domain\VehicleBooking;
use App\Infrastructure\Security\AuthUser;
use App\Tests\Factory\BookableFactory;
use App\Tests\Factory\Booking\RoomBookingFactory;
use App\Tests\Factory\Booking\VehicleBookingFactory;
use App\Tests\Factory\UserFactory;
use App\Tests\Integration\Api\AbstractApiTestCase;
use Zenst<PERSON>ck\Browser\Json;

class SearchBookingTest extends AbstractApiTestCase
{
    private AuthUser $authUser;

    protected function setUp(): void
    {
        parent::setUp();
        $this->authUser = UserFactory::aUser()->createAsAuthUser();
    }

    public function test_can_search_room_bookings(): void
    {
        /** @var RoomBooking[] $roomBookings */
        $roomBookings = RoomBookingFactory::createManyOrderedByStartDateAndBookedName(2);
        VehicleBookingFactory::createMany(2);
        $now = new \DateTimeImmutable();

        $json = $this->browser()
            ->actingAs($this->authUser)
            ->get('/api/bookings/rooms', [
                'query' => ['weekNumber' => (int) $now->format('W'), 'weekYear' => $now->format('Y')],
            ])
            ->assertSuccessful()
            ->assertJsonMatches('length("hydra:member")', 2)
            ->json()
        ;

        $i = 0;
        $json->assertThatEach('"hydra:member"', function (Json $value) use (&$i, $roomBookings): void {
            $roomBooking = $roomBookings[$i];
            $value->hasSubset([
                '@id' => '/api/bookings/rooms/'.$roomBooking->id(),
                '@type' => 'Booking',
                'meetingType' => $roomBooking->type()->value,
                'attendees' => $roomBooking->attendees(),
                'startDate' => $roomBooking->startDate()->format('Y-m-d\TH:i:sP'),
                'endDate' => $roomBooking->endDate()->format('Y-m-d\TH:i:sP'),
                'reason' => $roomBooking->reason(),
                'booked' => [
                    '@type' => 'BookedResource',
                    'id' => $roomBooking->booked()->id,
                    'name' => $roomBooking->booked()->name,
                    'color' => $roomBooking->booked()->color,
                ],
                'owner' => [
                    '@type' => 'OwnerResource',
                    'id' => $roomBooking->owner()->id,
                    'firstname' => $roomBooking->owner()->firstname,
                    'lastname' => $roomBooking->owner()->lastname,
                ],
                'id' => $roomBooking->id(),
            ]);
            ++$i;
        });
        unset($i);
    }

    public function test_can_search_vehicle_bookings(): void
    {
        /** @var VehicleBooking[] $vehicleBookings */
        $vehicleBookings = VehicleBookingFactory::createManyOrderedByStartDateAndBookedName(2);
        RoomBookingFactory::createMany(2);

        $now = new \DateTimeImmutable();
        $json = $this->browser()
            ->actingAs($this->authUser)
            ->get('/api/bookings/vehicles', [
                'query' => ['weekNumber' => (int) $now->format('W'), 'weekYear' => $now->format('Y')],
            ])
            ->assertSuccessful()
            ->assertJsonMatches('length("hydra:member")', 2)
            ->json()
        ;

        $i = 0;
        $json->assertThatEach('"hydra:member"', function (Json $value) use (&$i, $vehicleBookings): void {
            $vehicleBooking = $vehicleBookings[$i];
            $value->hasSubset([
                '@id' => '/api/bookings/vehicles/'.$vehicleBooking->id(),
                '@type' => 'Booking',
                'startDate' => $vehicleBooking->startDate()->format('Y-m-d\TH:i:sP'),
                'endDate' => $vehicleBooking->endDate()->format('Y-m-d\TH:i:sP'),
                'reason' => $vehicleBooking->reason(),
                'booked' => [
                    'id' => $vehicleBooking->booked()->id,
                    'name' => $vehicleBooking->booked()->name,
                    'color' => $vehicleBooking->booked()->color,
                ],
                'owner' => [
                    'id' => $vehicleBooking->owner()->id,
                    'firstname' => $vehicleBooking->owner()->firstname,
                    'lastname' => $vehicleBooking->owner()->lastname,
                ],
                'id' => $vehicleBooking->id(),
            ]);
            ++$i;
        });
        unset($i);
    }

    public function test_can_search_bookings_by_week(): void
    {
        $startDate = new \DateTimeImmutable('2025-07-15 08:00:00');
        $endDate = new \DateTimeImmutable('2025-07-15 08:59:59');
        $room = BookableFactory::aRoom()->withName('Piquette')->create();

        $booking1 = RoomBookingFactory::new()
            ->withBookable($room)
            ->withPeriod($startDate->modify('-1 week'), $endDate->modify('-1 week'))
            ->create();
        $booking2 = RoomBookingFactory::new()
            ->withBookable($room)
            ->withPeriod($startDate, $endDate)
            ->create();
        $booking3 = RoomBookingFactory::new()
            ->withBookable($room)
            ->withPeriod($startDate->modify('+1 week'), $endDate->modify('+1 week'))
            ->create();

        $json = $this->browser()
            ->actingAs($this->authUser)
            ->get('/api/bookings/rooms', [
                'query' => [
                    'weekNumber' => $startDate->format('W'),
                    'weekYear' => $startDate->format('Y'),
                ],
            ])
            ->assertSuccessful()
            ->assertJsonMatches('length("hydra:member")', 1)
            ->json();

        $i = 0;
        $roomBookings = [$booking2];
        $json->assertThatEach('"hydra:member"', function (Json $value) use (&$i, $roomBookings): void {
            $roomBookings = $roomBookings[$i];
            $value->hasSubset([
                'id' => $roomBookings->id(),
            ]);
            ++$i;
        });
        unset($i);
    }

    public function test_can_search_bookings_by_time(): void
    {
        $now = new \DateTimeImmutable();
        $room = BookableFactory::aRoom()->withName('Piquette')->create();

        $booking1 = RoomBookingFactory::new()
            ->withBookable($room)
            ->withPeriod($now->modify('08:00:00'), $now->modify('08:59:59'))
            ->create();
        $booking2 = RoomBookingFactory::new()
            ->withBookable($room)
            ->withPeriod($now->modify('11:00:00'), $now->modify('11:59:59'))
            ->create();
        $booking3 = RoomBookingFactory::new()
            ->withBookable($room)
            ->withPeriod($now->modify('14:00:00'), $now->modify('14:59:59'))
            ->create();

        $json = $this->browser()
            ->actingAs($this->authUser)
            ->get('/api/bookings/rooms', [
                'query' => [
                    'weekNumber' => (int) $now->format('W'),
                    'weekYear' => $now->format('Y'),
                    'minStartTime' => '11:00:00',
                    'maxEndTime' => '11:59:59',
                ],
            ])
            ->assertSuccessful()
            ->assertJsonMatches('length("hydra:member")', 1)
            ->json();

        $i = 0;
        $roomBookings = [$booking2];
        $json->assertThatEach('"hydra:member"', function (Json $value) use (&$i, $roomBookings): void {
            $roomBookings = $roomBookings[$i];
            $value->hasSubset([
                'id' => $roomBookings->id(),
            ]);
            ++$i;
        });
        unset($i);
    }

    public function test_can_search_bookings_by_bookable_identifier(): void
    {
        $now = new \DateTimeImmutable();
        $room1 = BookableFactory::aRoom()->withName('Piquette')->create();
        $room2 = BookableFactory::aRoom()->withName('Beaujolais')->create();
        $room3 = BookableFactory::aRoom()->withName('Pomerol')->create();

        $booking1 = RoomBookingFactory::new()
            ->withBookable($room1)
            ->withPeriod($now->modify('08:00:00'), $now->modify('08:59:59'))
            ->create();
        $booking2 = RoomBookingFactory::new()
            ->withBookable($room2)
            ->withPeriod($now->modify('08:00:00'), $now->modify('08:59:59'))
            ->create();
        $booking3 = RoomBookingFactory::new()
            ->withBookable($room3)
            ->withPeriod($now->modify('08:00:00'), $now->modify('08:59:59'))
            ->create();

        $json = $this->browser()
            ->actingAs($this->authUser)
            ->get('/api/bookings/rooms', [
                'query' => [
                    'weekNumber' => (int) $now->format('W'),
                    'weekYear' => $now->format('Y'),
                    'roomIdentifiers' => implode(',', [$room1->id(), $room3->id()]),
                ],
            ])
            ->assertSuccessful()
            ->assertJsonMatches('length("hydra:member")', 2)
            ->json()
        ;

        $i = 0;
        $roomBookings = [$booking1, $booking3];
        $json->assertThatEach('"hydra:member"', function (Json $value) use (&$i, $roomBookings): void {
            $roomBookings = $roomBookings[$i];
            $value->hasSubset([
                'id' => $roomBookings->id(),
            ]);
            ++$i;
        });
        unset($i);
    }

    public function test_can_search_room_bookings_by_bookable_capacity(): void
    {
        $now = new \DateTimeImmutable();
        $room1 = BookableFactory::aRoom()->withCapacity(2)->create();
        $room2 = BookableFactory::aRoom()->withCapacity(4)->create();
        $room3 = BookableFactory::aRoom()->withCapacity(5)->create();

        RoomBookingFactory::new()
            ->withBookable($room1)
            ->withPeriod($now->modify('08:00:00'), $now->modify('08:59:59'))
            ->create();
        RoomBookingFactory::new()
            ->withBookable($room2)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->create();
        RoomBookingFactory::new()
            ->withBookable($room3)
            ->withPeriod($now->modify('10:00:00'), $now->modify('10:59:59'))
            ->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->get('/api/bookings/rooms', [
                'query' => [
                    'weekNumber' => (int) $now->format('W'),
                    'weekYear' => $now->format('Y'),
                    'minRoomCapacity' => 4,
                ],
            ])
            ->assertSuccessful()
            ->assertJsonMatches('length("hydra:member")', 2);
    }

    public function test_can_search_vehicle_bookings_by_bookable_capacity(): void
    {
        $now = new \DateTimeImmutable();
        $vehicle1 = BookableFactory::aVehicle()->withCapacity(2)->create();
        $vehicle2 = BookableFactory::aVehicle()->withCapacity(4)->create();
        $vehicle3 = BookableFactory::aVehicle()->withCapacity(5)->create();

        VehicleBookingFactory::new()
            ->withBookable($vehicle1)
            ->withPeriod($now->modify('08:00:00'), $now->modify('08:59:59'))
            ->create();
        VehicleBookingFactory::new()
            ->withBookable($vehicle2)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->create();
        VehicleBookingFactory::new()
            ->withBookable($vehicle3)
            ->withPeriod($now->modify('10:00:00'), $now->modify('10:59:59'))
            ->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->get('/api/bookings/vehicles', [
                'query' => [
                    'weekNumber' => (int) $now->format('W'),
                    'weekYear' => $now->format('Y'),
                    'minVehicleCapacity' => 4,
                ],
            ])
            ->assertSuccessful()
            ->assertJsonMatches('length("hydra:member")', 2);
    }

    public function test_can_search_room_bookings_by_owner(): void
    {
        $now = new \DateTimeImmutable();

        $user1 = UserFactory::aUser()->create();
        $user2 = UserFactory::aUser()->create();
        $user3 = UserFactory::aUser()->create();
        RoomBookingFactory::new()->ownBy($user1)->create();
        RoomBookingFactory::new()->ownBy($user2)->create();
        RoomBookingFactory::new()->ownBy($user3)->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->get('/api/bookings/rooms', [
                'query' => [
                    'weekNumber' => (int) $now->format('W'),
                    'weekYear' => $now->format('Y'),
                    'ownerIdentifiers' => implode(',', [$user1->id(), $user2->id()]),
                ],
            ])
            ->assertSuccessful()
            ->assertJsonMatches('length("hydra:member")', 2)
        ;
    }

    public function test_can_search_vehicle_bookings_by_owner(): void
    {
        $now = new \DateTimeImmutable();
        $anotherUser = UserFactory::aUser()->createAsAuthUser();

        $user1 = UserFactory::aUser()->create();
        $user2 = UserFactory::aUser()->create();
        $user3 = UserFactory::aUser()->create();
        VehicleBookingFactory::new()->ownBy($user1)->create();
        VehicleBookingFactory::new()->ownBy($user2)->create();
        VehicleBookingFactory::new()->ownBy($user3)->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->get('/api/bookings/vehicles', [
                'query' => [
                    'weekNumber' => (int) $now->format('W'),
                    'weekYear' => $now->format('Y'),
                    'ownerIdentifiers' => implode(',', [$user1->id(), $user2->id()]),
                ],
            ])
            ->assertSuccessful()
            ->assertJsonMatches('length("hydra:member")', 2)
        ;
    }
}
