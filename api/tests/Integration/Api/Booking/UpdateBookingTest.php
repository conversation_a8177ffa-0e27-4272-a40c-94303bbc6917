<?php

namespace App\Tests\Integration\Api\Booking;

use App\Core\Booking\Domain\BookingRepository;
use App\Infrastructure\Security\AuthUser;
use App\Tests\Factory\BookableFactory;
use App\Tests\Factory\Booking\RoomBookingFactory;
use App\Tests\Factory\Booking\VehicleBookingFactory;
use App\Tests\Factory\UserFactory;
use App\Tests\Integration\Api\AbstractApiTestCase;
use Symfony\Component\Clock\Test\ClockSensitiveTrait;
use Symfony\Component\Uid\Ulid;
use Zenstruck\Browser\HttpOptions;
use Zenst<PERSON>ck\Browser\KernelBrowser;

class UpdateBookingTest extends AbstractApiTestCase
{
    use ClockSensitiveTrait;

    private AuthUser $authUser;
    private AuthUser $anotherUser;
    private BookingRepository $bookingRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->authUser = UserFactory::aUser()->createAsAuthUser();
        $this->anotherUser = UserFactory::aUser()->createAsAuthUser();
        $this->bookingRepository = $this->getContainer()->get(BookingRepository::class);
    }

    protected function browser(array $options = [], array $server = []): KernelBrowser
    {
        return $this->baseKernelBrowser()
            ->setDefaultHttpOptions(
                HttpOptions::create()
                    ->withHeader('Accept', 'application/ld+json')
                    ->withHeader('Content-Type', 'application/merge-patch+json')
            );
    }

    public function test_can_update_his_room_booking(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $bookable = BookableFactory::aRoom()->create();
        $booking = RoomBookingFactory::new()
            ->ownBy($this->authUser)
            ->withBookable($bookable)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->withAttendees(2)
            ->withReason('Réunion en tête à tête')
            ->create();

        $anotherBookable = BookableFactory::aRoom()->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/rooms/'.$booking->id(), ['json' => [
                'bookableId' => $anotherBookable->id(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'attendees' => 4,
                'reason' => 'J\'ai invité des copains finalement',
                'meetingType' => 'external',
            ]])
            ->assertSuccessful()
            ->assertJsonContains([
                '@id' => '/api/bookings/rooms/'.$booking->id(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d\TH:i:sP'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d\TH:i:sP'),
                'attendees' => 4,
                'reason' => 'J\'ai invité des copains finalement',
                'meetingType' => 'external',
                'owner' => [
                    'id' => $this->authUser->userId,
                    'firstname' => $this->authUser->firstname,
                    'lastname' => $this->authUser->lastname,
                ],
                'booked' => [
                    'id' => $anotherBookable->id(),
                    'name' => (string) $anotherBookable->name(),
                    'color' => (string) $anotherBookable->color(),
                ],
                'id' => $booking->id(),
            ])
        ;
    }

    public function test_admin_can_update_another_user_room_booking(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $authUser = UserFactory::anAdmin()->createAsAuthUser();
        $anotherUser = UserFactory::aUser()->create();

        $bookable = BookableFactory::aRoom()->create();
        $booking = RoomBookingFactory::new()
            ->ownBy($anotherUser)
            ->withBookable($bookable)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->withAttendees(2)
            ->withReason('Réunion en tête à tête')
            ->create();

        $this->browser()
            ->actingAs($authUser)
            ->patch('/api/bookings/rooms/'.$booking->id(), ['json' => [
                'bookableId' => $bookable->id(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'attendees' => 4,
                'reason' => 'Réunion en tête à tête - déplacée par un admin',
                'meetingType' => 'external',
            ]])
            ->assertSuccessful()
            ->assertJsonContains([
                '@id' => '/api/bookings/rooms/'.$booking->id(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d\TH:i:sP'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d\TH:i:sP'),
                'attendees' => 4,
                'reason' => 'Réunion en tête à tête - déplacée par un admin',
                'meetingType' => 'external',
                'owner' => [
                    'id' => $booking->owner()->id,
                    'firstname' => $booking->owner()->firstname,
                    'lastname' => $booking->owner()->lastname,
                ],
                'booked' => [
                    'id' => $bookable->id(),
                    'name' => (string) $bookable->name(),
                    'color' => (string) $bookable->color(),
                ],
                'id' => $booking->id(),
            ])
        ;
        $this->assertEmailCount(1);
    }

    public function test_non_admin_user_cannot_update_room_booking_of_another_user(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $booking = RoomBookingFactory::new()
            ->ownBy($this->anotherUser)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/rooms/'.$booking->id(), ['json' => [
                'bookableId' => $booking->booked()->id,
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Haha je vais déplacer ta réunion pour avoir de la place, et tok',
                'attendees' => 4,
                'meetingType' => 'external',
            ]])
            ->assertStatus(403);
    }

    public function test_cannot_update_removed_room_booking(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $booking = RoomBookingFactory::new()->create();
        $this->bookingRepository->delete($booking->id());

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/rooms/'.$booking->id(), ['json' => [
                'bookableId' => $booking->booked()->id,
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Réunion développeurs',
                'attendees' => 4,
                'meetingType' => 'external',
            ]])
            ->assertStatus(404);
    }

    public function test_cannot_update_room_booking_with_an_invalid_bookable_id(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $booking = RoomBookingFactory::new()->ownBy($this->authUser)->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/rooms/'.$booking->id(), ['json' => [
                'bookableId' => Ulid::generate(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Réunion développeurs',
                'attendees' => 4,
                'meetingType' => 'external',
            ]])
            ->assertStatus(400);
    }

    public function test_cannot_update_room_booking_with_an_already_booked_period(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $booking = RoomBookingFactory::new()
            ->ownBy($this->authUser)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/rooms/'.$booking->id(), ['json' => [
                'bookableId' => Ulid::generate(),
                'startDate' => $now->modify('09:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('09:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Réunion développeurs',
                'attendees' => 4,
                'meetingType' => 'external',
            ]])
            ->assertStatus(400);
    }

    public function test_can_update_room_booking_without_changing_period(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:30:00');
        static::mockTime($now);

        $booking = RoomBookingFactory::new()
            ->ownBy($this->authUser)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->withReason('J\'ai besoin d\'une salle pour une raison')
            ->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/rooms/'.$booking->id(), ['json' => [
                'bookableId' => $booking->booked()->id,
                'startDate' => $booking->startDate()->format('Y-m-d H:i:s'),
                'endDate' => $booking->endDate()->format('Y-m-d H:i:s'),
                'reason' => 'J\'ai besoin d\'une salle pour une autre raison mais je ne veux pas changer les horaires',
                'attendees' => $booking->attendees(),
                'meetingType' => $booking->type()->value,
            ]])
            ->assertSuccessful()
            ->assertJsonContains([
                'startDate' => $booking->startDate()->format('Y-m-d\TH:i:sP'),
                'endDate' => $booking->endDate()->format('Y-m-d\TH:i:sP'),
                'reason' => 'J\'ai besoin d\'une salle pour une autre raison mais je ne veux pas changer les horaires',
            ])
        ;
    }

    public function test_can_update_his_vehicle_booking(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $bookable = BookableFactory::aVehicle()->create();
        $booking = VehicleBookingFactory::new()
            ->ownBy($this->authUser)
            ->withBookable($bookable)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->withReason('Voyage en tête à tête')
            ->create();

        $anotherBookable = BookableFactory::aVehicle()->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/vehicles/'.$booking->id(), ['json' => [
                'bookableId' => $anotherBookable->id(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'J\'ai invité des copains finalement',
            ]])
            ->assertSuccessful()
            ->assertJsonContains([
                '@id' => '/api/bookings/vehicles/'.$booking->id(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d\TH:i:sP'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d\TH:i:sP'),
                'reason' => 'J\'ai invité des copains finalement',
                'owner' => [
                    'id' => $this->authUser->userId,
                    'firstname' => $this->authUser->firstname,
                    'lastname' => $this->authUser->lastname,
                ],
                'booked' => [
                    'id' => $anotherBookable->id(),
                    'name' => (string) $anotherBookable->name(),
                    'color' => (string) $anotherBookable->color(),
                ],
                'id' => $booking->id(),
            ])
        ;
    }

    public function test_admin_can_update_another_user_vehicle_booking(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $authUser = UserFactory::anAdmin()->createAsAuthUser();
        $anotherUser = UserFactory::aUser()->create();

        $bookable = BookableFactory::aVehicle()->create();
        $booking = VehicleBookingFactory::new()
            ->ownBy($anotherUser)
            ->withBookable($bookable)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->withReason('Voyage en tête à tête')
            ->create();

        $this->browser()
            ->actingAs($authUser)
            ->patch('/api/bookings/vehicles/'.$booking->id(), ['json' => [
                'bookableId' => $bookable->id(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Réunion en tête à tête - déplacée par un admin',
            ]])
            ->assertSuccessful()
            ->assertJsonContains([
                '@id' => '/api/bookings/vehicles/'.$booking->id(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d\TH:i:sP'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d\TH:i:sP'),
                'reason' => 'Réunion en tête à tête - déplacée par un admin',
                'owner' => [
                    'id' => $booking->owner()->id,
                    'firstname' => $booking->owner()->firstname,
                    'lastname' => $booking->owner()->lastname,
                ],
                'booked' => [
                    'id' => $bookable->id(),
                    'name' => (string) $bookable->name(),
                    'color' => (string) $bookable->color(),
                ],
                'id' => $booking->id(),
            ])
        ;
    }

    public function test_non_admin_user_cannot_update_vehicle_booking_of_another_user(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $booking = VehicleBookingFactory::new()
            ->ownBy($this->anotherUser)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/vehicles/'.$booking->id(), ['json' => [
                // TODO retirer les paramètres facultatifs
                'bookableId' => $booking->booked()->id,
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Haha je vais déplacer ta réservation pour avoir de la place, et tok',
            ]])
            ->assertStatus(403);
    }

    public function test_cannot_update_removed_vehicle_booking(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $booking = VehicleBookingFactory::new()->create();
        $this->bookingRepository->delete($booking->id());

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/vehicles/'.$booking->id(), ['json' => [
                'bookableId' => $booking->booked()->id,
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Voyage entre développeurs',
            ]])
            ->assertStatus(404);
    }

    public function test_cannot_update_vehicle_booking_with_an_invalid_bookable_id(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $booking = VehicleBookingFactory::new()->ownBy($this->authUser)->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/vehicles/'.$booking->id(), ['json' => [
                'bookableId' => Ulid::generate(),
                'startDate' => $now->modify('10:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('10:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Voyage entre développeurs',
            ]])
            ->assertStatus(400);
    }

    public function test_cannot_update_vehicle_booking_with_an_already_booked_period(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $booking = VehicleBookingFactory::new()
            ->ownBy($this->authUser)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/vehicles/'.$booking->id(), ['json' => [
                'bookableId' => Ulid::generate(),
                'startDate' => $now->modify('09:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('09:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Voyage entre développeurs',
            ]])
            ->assertStatus(400);
    }

    public function test_can_update_vehicle_booking_without_changing_period(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:30:00');
        static::mockTime($now);

        $booking = VehicleBookingFactory::new()
            ->ownBy($this->authUser)
            ->withPeriod($now->modify('09:00:00'), $now->modify('09:59:59'))
            ->withReason('J\'ai besoin d\'un véhicule pour une raison')
            ->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->patch('/api/bookings/vehicles/'.$booking->id(), ['json' => [
                'bookableId' => $booking->booked()->id,
                'startDate' => $booking->startDate()->format('Y-m-d H:i:s'),
                'endDate' => $booking->endDate()->format('Y-m-d H:i:s'),
                'reason' => 'J\'ai besoin d\'un véhicule pour une autre raison mais je ne veux pas changer les horaires',
            ]])
            ->assertSuccessful()
            ->assertJsonContains([
                'startDate' => $booking->startDate()->format('Y-m-d\TH:i:sP'),
                'endDate' => $booking->endDate()->format('Y-m-d\TH:i:sP'),
                'reason' => 'J\'ai besoin d\'un véhicule pour une autre raison mais je ne veux pas changer les horaires',
            ])
        ;
    }
}
