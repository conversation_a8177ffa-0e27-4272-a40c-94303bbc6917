<?php

namespace App\Tests\Integration\Api\Booking;

use App\Infrastructure\Database\Repository\DatabaseBookingRepository;
use App\Infrastructure\Security\AuthUser;
use App\Tests\Factory\BookableFactory;
use App\Tests\Factory\Booking\RoomBookingFactory;
use App\Tests\Factory\UserFactory;
use App\Tests\Integration\Api\AbstractApiTestCase;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\Clock\Test\ClockSensitiveTrait;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class CreateBookingTest extends AbstractApiTestCase
{
    use ClockSensitiveTrait;

    private DatabaseBookingRepository $bookingRepository;
    private AuthUser $authUser;

    protected function setUp(): void
    {
        parent::setUp();
        $this->bookingRepository = self::getContainer()->get(DatabaseBookingRepository::class);
        $this->authUser = UserFactory::aUser()->createAsAuthUser();
    }

    public function test_can_book_a_room(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $room = BookableFactory::aRoom()->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookings/rooms', ['json' => [
                'bookableId' => $room->id(),
                'startDate' => $now->modify('11:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('11:59:59')->format('Y-m-d H:i:s'),
                'attendees' => 4,
                'reason' => 'Réunion développeurs',
                'meetingType' => 'internal',
            ]]
            )
            ->assertStatus(201)
        ;
        $count = $this->bookingRepository->count([]);
        $this->assertEquals(1, $count);
    }

    public function test_cannot_book_room_with_start_date_after_end_date(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $room = BookableFactory::aRoom()->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookings/rooms', ['json' => [
                'bookableId' => $room->id(),
                'startDate' => $now->modify('14:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('11:59:59')->format('Y-m-d H:i:s'),
                'attendees' => 4,
                'reason' => 'Réunion développeurs',
                'meetingType' => 'internal',
            ]]
            )
            ->assertStatus(422)
        ;
    }

    public function test_can_book_a_vehicle(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $vehicle = BookableFactory::aVehicle()->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookings/vehicles', ['json' => [
                'bookableId' => $vehicle->id(),
                'startDate' => $now->modify('09:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('11:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Vroom',
            ]]
            )
            ->assertStatus(201)
        ;
    }

    public function test_cannot_book_a_room_using_a_vehicle_id(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $vehicle = BookableFactory::aVehicle()->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookings/rooms', ['json' => [
                'bookableId' => $vehicle->id(),
                'startDate' => $now->modify('09:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('11:59:59')->format('Y-m-d H:i:s'),
                'attendees' => 4,
                'reason' => 'Réunion développeurs',
                'meetingType' => 'internal',
            ]]
            )
            ->assertStatus(400);
    }

    public function test_cannot_book_a_vehicle_using_a_room_id(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $room = BookableFactory::aRoom()->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookings/vehicles', ['json' => [
                'bookableId' => $room->id(),
                'startDate' => $now->modify('09:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('11:59:59')->format('Y-m-d H:i:s'),
                'reason' => 'Déplacement',
            ]]
            )
            ->assertStatus(400);
    }

    public static function bookableThatIsAlreadyBookedProvider(): iterable
    {
        yield 'on same startDate and endDate' => [
            'bookedStartDate' => '09:00:00',
            'bookedEndDate' => '10:59:59',
            'startDate' => '09:00:00',
            'endDate' => '10:59:59',
        ];
        yield 'startDate overlap' => [
            'bookedStartDate' => '09:00:00',
            'bookedEndDate' => '10:59:59',
            'startDate' => '10:00:00',
            'endDate' => '11:59:59',
        ];
        yield 'endDate overlap' => [
            'bookedStartDate' => '09:00:00',
            'bookedEndDate' => '10:59:59',
            'startDate' => '08:00:00',
            'endDate' => '09:59:59',
        ];
        yield 'booked interval between startDate and endDate' => [
            'bookedStartDate' => '09:00:00',
            'bookedEndDate' => '10:59:59',
            'startDate' => '08:00:00',
            'endDate' => '11:59:59',
        ];
    }

    #[DataProvider('bookableThatIsAlreadyBookedProvider')]
    public function test_cannot_book_a_bookable_that_is_already_booked(string $bookedStartDate, string $bookedEndDate, string $startDate, string $endDate): void
    {
        $now = new \DateTimeImmutable('2025-10-22 08:00:00');
        static::mockTime($now);

        $bookedStartDate = $now->modify($bookedStartDate);
        $bookedEndDate = $now->modify($bookedEndDate);
        $startDate = $now->modify($startDate);
        $endDate = $now->modify($endDate);

        $room = BookableFactory::aRoom()->withName('Piquette')->create();
        RoomBookingFactory::new()
            ->ownBy($this->authUser)
            ->withBookable($room)
            ->withPeriod($bookedStartDate, $bookedEndDate)
            ->create();

        $this->browser()
            ->actingAs($this->authUser)
            ->expectException(BadRequestHttpException::class, 'La salle "Piquette" est déjà réservée sur la période demandée')
            ->post('/api/bookings/rooms', ['json' => [
                'bookableId' => $room->id(),
                'startDate' => $startDate->format('Y-m-d H:i:s'),
                'endDate' => $endDate->format('Y-m-d H:i:s'),
                'attendees' => 4,
                'reason' => 'Réunion développeurs',
                'meetingType' => 'internal',
            ]]
            )
        ;
    }

    public function test_can_book_a_bookable_and_receive_an_email_with_an_ics_invitation_in_attachment(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $bookable = BookableFactory::aRoom()->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookings/rooms', ['json' => [
                'bookableId' => $bookable->id(),
                'startDate' => $now->modify('11:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('11:59:59')->format('Y-m-d H:i:s'),
                'attendees' => 4,
                'reason' => 'Réunion développeurs',
                'meetingType' => 'internal',
                'sendCalendarInvitation' => true,
            ]]
            )
            ->assertStatus(201);

        $this->assertEmailCount(1);
        $email = $this->getMailerMessage();
        $this->assertEmailAttachmentCount($email, 1);
    }

    public function test_can_book_a_bookable_without_receiving_an_email(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 09:00:00');
        static::mockTime($now);

        $bookable = BookableFactory::aRoom()->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookings/rooms', ['json' => [
                'bookableId' => $bookable->id(),
                'startDate' => $now->modify('11:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('11:59:59')->format('Y-m-d H:i:s'),
                'attendees' => 4,
                'reason' => 'Réunion développeurs',
                'meetingType' => 'internal',
            ]]
            )
            ->assertStatus(201);

        $this->assertEmailCount(0);
    }

    public function test_can_book_a_room_on_the_current_half_hour(): void
    {
        $now = new \DateTimeImmutable('2025-10-22 11:03:00');
        static::mockTime($now);

        $room = BookableFactory::aRoom()->create();
        $this->browser()
            ->actingAs($this->authUser)
            ->post('/api/bookings/rooms', ['json' => [
                'bookableId' => $room->id(),
                'startDate' => $now->modify('11:00:00')->format('Y-m-d H:i:s'),
                'endDate' => $now->modify('11:59:59')->format('Y-m-d H:i:s'),
                'attendees' => 4,
                'reason' => 'Réunion développeurs',
                'meetingType' => 'internal',
            ]]
            )
            ->assertStatus(201);
        $count = $this->bookingRepository->count([]);
        $this->assertEquals(1, $count);
    }
}
