<?php

namespace App\Tests\Unit\Core\User\Application;

use App\Core\User\Application\DTO\UserDTO;
use App\Core\User\Application\UseCase\SearchUser\SearchUserCommand;
use App\Core\User\Application\UseCase\SearchUser\SearchUserHandler;
use App\Core\User\Domain\Service\ProfilePictureUrlGenerator;
use App\Core\User\Domain\User;
use App\Infrastructure\Security\AuthorizationChecker\UserAuthorizationChecker;
use App\Tests\Factory\UserFactory;
use App\Tests\Fixtures\Repository\InMemoryUserRepository;
use App\Tests\Unit\AbstractTestCase;
use Symfony\Bundle\SecurityBundle\Security;

class SearchUserTest extends AbstractTestCase
{
    protected InMemoryUserRepository $userRepository;
    private SearchUserHandler $searchUserHandler;

    public function setUp(): void
    {
        parent::setUp();

        $this->userRepository = new InMemoryUserRepository();
        $security = $this->createMock(Security::class);
        $security->method('isGranted')->willReturn(true);
        $authorizationChecker = new UserAuthorizationChecker($security);
        $this->searchUserHandler = new SearchUserHandler($this->userRepository, $this->createMock(ProfilePictureUrlGenerator::class), $authorizationChecker);
    }

    public function test_search_users_with_pagination(): void
    {
        $users = UserFactory::sortUsersByFirstnameAndLastname(UserFactory::createMany(14));
        foreach ($users as $user) {
            $this->userRepository->add($user);
        }
        $expectedUserDTOs = array_map(
            fn (User $user) => UserDTO::fromEntity($user),
            array_slice($users, 10, 4)
        );

        $searchUserOutputDTO = $this->searchUserHandler->__invoke(new SearchUserCommand(
            page: 2,
            itemsPerPage: 10,
        ));

        $this->assertEquals(14, $searchUserOutputDTO->numberOfUsers);
        $this->assertCount(4, $searchUserOutputDTO->userDTOs);
        $this->assertEquals($expectedUserDTOs, $searchUserOutputDTO->userDTOs);
    }
}
