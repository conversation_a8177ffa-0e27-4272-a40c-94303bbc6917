vich_uploader:
    db_driver: orm
    metadata:
        type: attribute
    mappings:
        profile_picture:
            uri_prefix: /profile_picture
            upload_destination: '%kernel.project_dir%/uploads/profile_picture'
            # Will rename uploaded files using a uniqueid as a prefix.
            namer: Vich\UploaderBundle\Naming\SmartUniqueNamer
        bookable_picture:
            uri_prefix: /bookable_picture
            upload_destination: '%kernel.project_dir%/uploads/bookable_picture'
            # Will rename uploaded files using a uniqueid as a prefix.
            namer: Vich\UploaderBundle\Naming\SmartUniqueNamer
