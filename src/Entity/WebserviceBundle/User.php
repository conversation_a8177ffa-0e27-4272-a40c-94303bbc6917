<?php

namespace App\Entity\WebserviceBundle;

use Symfony\Component\Security\Core\User\EquatableInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\String\Inflector\EnglishInflector;

/**
 * @method checkDroitsStandard()
 * @method checkDroitsPartageCiblesGroupes()
 * @method checkDroitsEmailPersoAvancee()
 * @method checkDroitsOpNatCreer()
 * @method checkDroitsEmailContactsTest()
 * @method checkDroitsDashbord()
 * @method checkDroitsEmailSmsPrevisuAvancee()
 * @method checkDroitsSerieCreer()
 * @method checkDroitsNoDelaiRoutage()
 * @method checkDroitsOpNatUploadFile()
 * @method checkDroitsNoValidationDiffFtp()
 * @method checkDroitsOpNatEmailParticipation()
 * @method checkDroitsTarifsReserves()
 * @method checkDroitsCanalCourrier()
 * @method checkDroitsCanalEmail()
 * @method checkDroitsCanalSms()
 * @method checkDroitsCanalFtp()
 * @method checkDroitsCanalAudio()
 * @method checkDroitsSelectionImportIds()
 * @method checkDroitsParamEchantillonsCtrl()
 * @method checkDroitsGroupeImport()
 * @method checkDroitsTarifsLibreService()
 * @method checkDroitsDataviz()
 * @method checkDroitsGestionModeles()
 * @method checkDroitsSimulationUtilisateur()
 * @method checkDroitsSelectionAvancee()
 * @method checkDroitsChangelog()
 * @method checkDroitsSmsPersoAvancee()
 * @method checkDroitsCodesEan()
 * @method checkDroitsEmailExpediteur()
 * @method checkDroitsSmsModifierExpediteur()
 * @method checkDroitsSmsAucunExpediteur()
 * @method checkDroitsAdminAquitem()
 * @method checkDroitsSiege()
 * @method checkDroitsModuleClient()
 * @method checkDroitsConsultationConsolidation()
 * @method checkDroitsOperations()
 * @method checkDroitsSelections()
 * @method checkDroitsCanalWallet()
 * @method checkDroitsDocumentsLecture()
 * @method checkDroitsDocumentsEcriture()
 * @method checkDroitsCreationClient()
 * @method checkDroitsAjoutVente()
 * @method checkDroitsAjoutBonus()
 * @method checkDroitsOffreDemat()
 * @method checkDroitsPromotions()
 * @method checkDroitsExclusions()
 * @method checkDroitsFusionClients()
 * @method checkDroitsRetourProduit()
 * @method checkDroitsUrlCourtePdf()
 * @method checkDroitsAnonymisation()
 * @method checkDroitsEditModulesStripo()
 * @method checkDroitsBri()
 * @method checkDroitsCartesCadeaux()
 * @method checkDroitsActivationCarteCadeau()
 * @method checkDroitsAnnulationCarteCadeau()
 * @method checkDroitsProduitsOperation()
 * @method checkDroitsSouscriptionJobs()
 * @method checkDroitsConsultationSouscriptionJobs()
 * @method checkDroitsOperationsWallet()
 * @method checkDroitsPointsDeVente()
 * @method checkDroitsPointsDeVenteCreation()
 * @method checkDroitsPointsDeVenteModification()
 * @method checkDroitsPointsDeVenteFermetureOuModificationFiness()
 */
class User implements UserInterface, EquatableInterface
{

    private $id;
    private $username;
    private $password;
    private $description;
    private $enseigne;
    private $organisation;
    private $requeteur;
    private $civilite;
    private $nom;
    private $prenom;
    private $fonction;
    private $tel;
    private $mobile;
    private $email;
    private $independantNonFranchise;
    private $droitsAcces;
    private $droitsAccesByte;
    private $idRequeteur;
    private $localeRequeteur;
    private $urlDataviz;
    private $lienCourtSms;
    private $apiKeyMiniSiteSms;
    private $compteSms;
    private $compteMiniSiteSms;
    private $smsLongs;
    private $nbTarifsWallet;
    private $requeteurAcceptantLesImportsHorsFid;
    private $requeteurContenantDesEnseignesHorsFid;
    private $synchronisationExportsDataViz;
    private $accesPersonnalisationWalletsOK;
	private Droits $droits;

	public function __call($methodName, $arguments)
    {
        if (substr($methodName, 0, 11) === 'checkDroits' && strlen($methodName) > 12) {
			if($this->droitsAccesByte === null) {
				return false;
			}
            $word = substr($methodName, 11);
			return $this->droits->{'has'.$word}();
		}

	}

    /**
     * @return mixed
     */
    public function getId(): mixed
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     * @return User
     */
    public function setId($id): User
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getUsername(): mixed
    {
        return $this->username;
    }

    /**
     * @param mixed $username
     * @return User
     */
    public function setUsername($username): User
    {
        $this->username = $username;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getPassword(): mixed
    {
        return $this->password;
    }

    /**
     * @param mixed $password
     * @return User
     */
    public function setPassword($password): User
    {
        $this->password = $password;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDescription(): mixed
    {
        return $this->description;
    }

    /**
     * @param mixed $description
     * @return User
     */
    public function setDescription($description): User
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEnseigne(): mixed
    {
        return $this->enseigne;
    }

    /**
     * @param mixed $enseigne
     * @return User
     */
    public function setEnseigne($enseigne): User
    {
        $this->enseigne = $enseigne;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getOrganisation(): mixed
    {
        return $this->organisation;
    }

    /**
     * @param mixed $organisation
     * @return User
     */
    public function setOrganisation($organisation): User
    {
        $this->organisation = $organisation;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getRequeteur(): mixed
    {
        return $this->requeteur;
    }

    /**
     * @param mixed $requeteur
     * @return User
     */
    public function setRequeteur($requeteur): User
    {
        $this->requeteur = $requeteur;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCivilite(): mixed
    {
        return $this->civilite;
    }

    /**
     * @param mixed $civilite
     * @return User
     */
    public function setCivilite($civilite): User
    {
        $this->civilite = $civilite;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getNom(): mixed
    {
        return $this->nom;
    }

    /**
     * @param mixed $nom
     * @return User
     */
    public function setNom($nom): User
    {
        $this->nom = $nom;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getPrenom(): mixed
    {
        return $this->prenom;
    }

    /**
     * @param mixed $prenom
     * @return User
     */
    public function setPrenom($prenom): User
    {
        $this->prenom = $prenom;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFonction(): mixed
    {
        return $this->fonction;
    }

    /**
     * @param mixed $fonction
     * @return User
     */
    public function setFonction($fonction): User
    {
        $this->fonction = $fonction;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getTel(): mixed
    {
        return $this->tel;
    }

    /**
     * @param mixed $tel
     * @return User
     */
    public function setTel($tel): User
    {
        $this->tel = $tel;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getMobile(): mixed
    {
        return $this->mobile;
    }

    /**
     * @param mixed $mobile
     * @return User
     */
    public function setMobile($mobile): User
    {
        $this->mobile = $mobile;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEmail(): mixed
    {
        return $this->email;
    }

    /**
     * @param mixed $email
     * @return User
     */
    public function setEmail($email): User
    {
        $this->email = $email;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getIndependantNonFranchise(): mixed
    {
        return $this->independantNonFranchise;
    }

    /**
     * @param mixed $independantNonFranchise
     * @return User
     */
    public function setIndependantNonFranchise($independantNonFranchise): User
    {
        $this->independantNonFranchise = $independantNonFranchise;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDroitsAcces(): mixed
    {
        return $this->droitsAcces;
    }

    /**
     * @param mixed $droitsAcces
     * @return User
     */
    public function setDroitsAcces($droitsAcces): User
    {
        $this->droitsAcces = $droitsAcces;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDroitsAccesByte(): mixed
    {
        return $this->droitsAccesByte;
    }

    /**
     * @param mixed $droitsAccesByte
     */
    public function setDroitsAccesByte($droitsAccesByte)
    {
        $this->droitsAccesByte = $droitsAccesByte;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getIdRequeteur(): mixed
    {
        return $this->idRequeteur;
    }

    /**
     * @param mixed $idRequeteur
     * @return User
     */
    public function setIdRequeteur($idRequeteur): User
    {
        $this->idRequeteur = $idRequeteur;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLocaleRequeteur(): mixed
    {
        return $this->localeRequeteur;
    }

    /**
     * @param mixed $localeRequeteur
     * @return User
     */
    public function setLocaleRequeteur($localeRequeteur): User
    {
        $this->localeRequeteur = $localeRequeteur;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getUrlDataviz(): mixed
    {
        return $this->urlDataviz;
    }

    /**
     * @param mixed $urlDataviz
     * @return User
     */
    public function setUrlDataviz($urlDataviz): User
    {
        $this->urlDataviz = $urlDataviz;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getLienCourtSms(): mixed
    {
        return $this->lienCourtSms;
    }

    /**
     * @param mixed $lienCourtSms
     * @return User
     */
    public function setLienCourtSms($lienCourtSms): User
    {
        $this->lienCourtSms = $lienCourtSms;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getApiKeyMiniSiteSms(): mixed
    {
        return $this->apiKeyMiniSiteSms;
    }

    /**
     * @param mixed $apiKeyMiniSiteSms
     * @return User
     */
    public function setApiKeyMiniSiteSms($apiKeyMiniSiteSms): User
    {
        $this->apiKeyMiniSiteSms = $apiKeyMiniSiteSms;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getCompteMiniSiteSms(): mixed
    {
        return $this->compteMiniSiteSms;
    }

    /**
     * @param mixed $compteMiniSiteSms
     * @return User
     */
    public function setCompteMiniSiteSms($compteMiniSiteSms): User
    {
        $this->compteMiniSiteSms = $compteMiniSiteSms;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getSynchronisationExportsDataViz(): mixed
    {
        return $this->synchronisationExportsDataViz;
    }

    /**
     * @param mixed $synchronisationExportsDataViz
     * @return User
     */
    public function setSynchronisationExportsDataViz($synchronisationExportsDataViz): User
    {
        $this->synchronisationExportsDataViz = $synchronisationExportsDataViz;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getRequeteurAcceptantLesImportsHorsFid(): mixed
    {
        return $this->requeteurAcceptantLesImportsHorsFid;
    }

    /**
     * @param mixed $requeteurAcceptantLesImportsHorsFid
     * @return User
     */
    public function setRequeteurAcceptantLesImportsHorsFid($requeteurAcceptantLesImportsHorsFid): User
    {
        $this->requeteurAcceptantLesImportsHorsFid = $requeteurAcceptantLesImportsHorsFid;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getRequeteurContenantDesEnseignesHorsFid(): mixed
    {
        return $this->requeteurContenantDesEnseignesHorsFid;
    }

    /**
     * @param mixed $requeteurContenantDesEnseignesHorsFid
     * @return User
     */
    public function setRequeteurContenantDesEnseignesHorsFid($requeteurContenantDesEnseignesHorsFid): User
    {
        $this->requeteurContenantDesEnseignesHorsFid = $requeteurContenantDesEnseignesHorsFid;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getCompteSms(): mixed
    {
        return $this->compteSms;
    }

    /**
     * @param mixed $compteSms
     * @return User
     */
    public function setCompteSms($compteSms): User
    {
        $this->compteSms = $compteSms;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getSmsLongs(): mixed
    {
        return $this->smsLongs;
    }

    /**
     * @param mixed $smsLongs
     * @return User
     */
    public function setSmsLongs($smsLongs): User
    {
        $this->smsLongs = $smsLongs;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getNbTarifsWallet(): mixed
    {
        return $this->nbTarifsWallet;
    }

    /**
     * @param mixed $nbTarifsWallet
     * @return User
     */
    public function setNbTarifsWallet($nbTarifsWallet): User
    {
        $this->nbTarifsWallet = $nbTarifsWallet;
        return $this;
    }

    /**
     * @return mixed
     */
    public function canAccesPersonnalisationWallets(): mixed
    {
        return $this->accesPersonnalisationWalletsOK;
    }

    /**
     * @param mixed $requeteurContenantDesEnseignesHorsFid
     * @return User
     */
    public function setAccesPersonnalisationWalletsOK($accesPersonnalisationWalletsOK): User
    {
        $this->accesPersonnalisationWalletsOK = $accesPersonnalisationWalletsOK;
        return $this;
    }

    public function getRoles(): array
    {
        return ["ROLE_USER"];
    }

    public function getSalt()
    {
        return "";
    }

    public function eraseCredentials(): void
    {
    }

    public function unserialize($serialized)
    {
        $data = json_decode($serialized, true);
        return self::fromArray($data);
    }

    static public function fromArray($data)
    {
        return (new self)
            ->setId($data['id'])
            ->setUsername($data['login'])
            ->setPassword($data['password'])
            ->setDescription($data['description'])
            ->setOrganisation($data['organisation'])
            ->setRequeteur($data['requeteur'])
            ->setEnseigne($data['enseigne'])
            ->setCivilite($data['civilite'])
            ->setNom($data['nom'])
            ->setPrenom($data['prenom'])
            ->setFonction($data['fonction'])
            ->setTel($data['tel'])
            ->setMobile($data['mobile'])
            ->setEmail($data['email'])
            ->setIndependantNonFranchise($data['independantNonFranchise'])
            ->setDroitsAcces($data['droitsAcces'])
            ->withDroitsAcces($data['droitsAccesByte'], $data['independantNonFranchise'] !== "false", $data['smsLongs'] !== "false", $data['requeteurAcceptantLesImportsHorsFid'] !== "false", $data['requeteurContenantDesEnseignesHorsFid'] !== "false")
            ->setIdRequeteur($data['idRequeteur'])
            ->setLocaleRequeteur($data['localeRequeteur'])
            ->setUrlDataviz($data['urlDataviz'])
            ->setLienCourtSms($data['lienCourtSms'])
            ->setApiKeyMiniSiteSms($data['apiKeyMiniSiteSms'])
            ->setCompteSms($data['compteSms'])
            ->setCompteMiniSiteSms($data['compteMiniSiteSms'])
            ->setSmsLongs(filter_var($data['smsLongs'], FILTER_VALIDATE_BOOLEAN))
            ->setSynchronisationExportsDataViz(filter_var($data['synchronisationExportsDataViz'], FILTER_VALIDATE_BOOLEAN))
            ->setRequeteurAcceptantLesImportsHorsFid(filter_var($data['requeteurAcceptantLesImportsHorsFid'], FILTER_VALIDATE_BOOLEAN))
            ->setRequeteurContenantDesEnseignesHorsFid(filter_var($data['requeteurContenantDesEnseignesHorsFid'], FILTER_VALIDATE_BOOLEAN))
            ->setAccesPersonnalisationWalletsOK(filter_var($data['accesPersonnalisationWalletsOK'], FILTER_VALIDATE_BOOLEAN))
            ->setAccesPersonnalisationWalletsOK(filter_var($data['accesPersonnalisationWalletsOK'], FILTER_VALIDATE_BOOLEAN))
            ->setNbTarifsWallet($data['nbTarifsNotificationsAutonomes']);
    }

    public function serialize()
    {
        return json_encode(array(
            'id' => $this->getId(),
            'login' => $this->getUsername(),
            'password' => $this->getPassword(),
            'description' => $this->getDescription(),
            'organisation' => $this->getOrganisation(),
            'enseigne' => $this->getEnseigne(),
            'requeteur' => $this->getRequeteur(),
            'civilite' => $this->getCivilite(),
            'nom' => $this->getNom(),
            'prenom' => $this->getPrenom(),
            'fonction' => $this->getFonction(),
            'tel' => $this->getTel(),
            'mobile' => $this->getMobile(),
            'email' => $this->getEmail(),
            'independantNonFranchise' => $this->getIndependantNonFranchise(),
            'droitsAcces' => $this->getDroitsacces(),
            'droitsAccesByte' => $this->getDroitsaccesByte(),
            'idRequeteur' => $this->getIdrequeteur(),
            'localeRequeteur' => $this->getLocalerequeteur(),
            'urlDataviz' => $this->getUrlDataviz(),
            'lienCourtSms' => $this->getLienCourtSms(),
            'apiKeyMiniSiteSms' => $this->getApiKeyMiniSiteSms(),
            'compteSms' => $this->getCompteSms(),
            'compteMiniSiteSms' => $this->getCompteMiniSiteSms(),
            'smsLongs' => $this->getSmsLongs(),
            'synchronisationExportsDataViz' => $this->getSynchronisationExportsDataViz(),
            'requeteurAcceptantLesImportsHorsFid' => $this->getRequeteurAcceptantLesImportsHorsFid(),
            'requeteurContenantDesEnseignesHorsFid' => $this->getRequeteurContenantDesEnseignesHorsFid(),
            'accesPersonnalisationWalletsOK' => $this->canAccesPersonnalisationWallets(),
            'nbTarifsNotificationsAutonomes' => $this->getNbTarifsWallet(),
        ));
    }

    public function isEqualTo(UserInterface $user): bool
    {
        return true;
    }

    public function canAccessDataviz()
    {
        return $this->checkDroitsDataviz() && $this->getUrlDataviz() !== "";
    }

    public function canAccessWalletOperationCreation()
    {
        return $this->checkDroitsOperationsWallet() && $this->getNbTarifsWallet() > 0;
    }
    public function getUserIdentifier(): string
    {
		return $this->username;
    }

	public function withDroitsAcces($droitsAccesByte, $independantNonFranchise = false, $smsLongs = false, $requeteurAcceptantLesImportsHorsFid = false, $requeteurContenantDesEnseignesHorsFid = false)
	{
		$this->setDroitsAccesByte($droitsAccesByte);
		$this->setIndependantNonFranchise($independantNonFranchise);
		$this->setSmsLongs($smsLongs);
		$this->setRequeteurAcceptantLesImportsHorsFid($requeteurAcceptantLesImportsHorsFid);
		$this->setRequeteurContenantDesEnseignesHorsFid($requeteurContenantDesEnseignesHorsFid);
		$this->droits = new Droits($droitsAccesByte);
		return $this;
	}
}
