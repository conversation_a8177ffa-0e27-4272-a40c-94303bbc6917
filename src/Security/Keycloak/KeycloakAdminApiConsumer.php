<?php

namespace App\Security\Keycloak;

use App\Security\Keycloak\DTO\GetAdminTokenResponse;
use App\Security\Keycloak\DTO\GetUserResponse;
use App\Security\Keycloak\DTO\RegisterUserResponse;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class KeycloakAdminApiConsumer
{
    const KEYCLOAK_CLIENT_ID = 'symfony-test';
    const KEYCLOAK_BASE_URL = 'https://accounts.alienor.net';
    const REALMS_URI = '/realms/dev-pp';

    const ADMIN_API_ENDPOINT = self::KEYCLOAK_BASE_URL . '/admin' . self::REALMS_URI;
    const OIDC_ENDPOINT = self::KEYCLOAK_BASE_URL . self::REALMS_URI . '/protocol/openid-connect';

    const ACCESS_TOKEN_ENDPOINT = self::OIDC_ENDPOINT . '/token';
    const REGISTER_USER_ENDPOINT = self::ADMIN_API_ENDPOINT . '/users';
    const VERIFY_EMAIL_AVAILABILITY_ENDPOINT = self::ADMIN_API_ENDPOINT . '/users?email=:email';
    const LOGOUT_USER_ENDPOINT = self::ADMIN_API_ENDPOINT . '/users/:id/logout';

    public function __construct(
        private readonly HttpClientInterface $httpClient,
    ) {
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getAdminToken(): GetAdminTokenResponse
    {
        $response = $this->httpClient->request('POST', self::ACCESS_TOKEN_ENDPOINT, [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'body' => [
                'grant_type' => 'password',
                'client_id' => self::KEYCLOAK_CLIENT_ID,
                // TODO mettre en variable d'environnement les accès d'un utilisateur admin dédié à symfony-test
                'username' => '<EMAIL>',
                'password' => 'dqJ7xXAZo9DoyTS4nYS29WtEsu2DVSg79',
            ],
        ]);
        return GetAdminTokenResponse::fromArray($response->toArray());
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getUserByEmail(string $accessToken, string $email): ?GetUserResponse
    {
        $url = str_replace(':email', $email, self::VERIFY_EMAIL_AVAILABILITY_ENDPOINT);
        $response = $this->httpClient->request('GET', $url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
            ],
        ]);
        $data = $response->toArray();
        return [] === $data ? null : GetUserResponse::fromArray($data);
    }

//    public function registerUser(string $accessToken, RegistrationDTO $registrationDTO): RegisterUserResponse
//    {
//        $response = $this->httpClient->request('POST', self::REGISTER_USER_ENDPOINT, [
//            'headers' => [
//                'Authorization' => 'Bearer ' . $accessToken,
//            ],
//            'json' => [
//                'username' => $registrationDTO->email,
//                'email' => $registrationDTO->email,
//                'enabled' => true,
//                'firstName' => $registrationDTO->firstname,
//                'lastName' => $registrationDTO->lastname,
//                'requiredActions' => ['VERIFY_EMAIL'],
//            ],
//        ]);
//
//        // il est possible de retouver l'id de l'utilisateur dans l'en-tête Location directement
//        $headers = $response->getHeaders();
//        if (null === $headers['location'] || empty($headers['location'])) {
//            // ce n'est pas implémenté ici mais il est également possible de trouver cet id
//            // en appelant la route d'API suivante : https://<host>/admin/realms/<realm>/users?username=nouvel_utilisateur
//            throw new \LogicException('Aucune réponse Location');
//        }
//        $userUri = $headers['location'][0];
//
//        return new RegisterUserResponse(userUri: $userUri);
//    }

    public function setUserPassword(string $accessToken, string $userUri, string $password): void
    {
        $response = $this->httpClient->request('PUT', $userUri . '/reset-password', [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
            ],
            'json' => [
                'type' => 'password',
                'value' => $password,
                'temporary' => false,
            ],
        ]);
    }

    /**
     * @throws TransportExceptionInterface
     */
    public function logoutUserById(string $accessToken, string $sub): void
    {
        $url = str_replace(':id', $sub, self::LOGOUT_USER_ENDPOINT);
        $this->httpClient->request('POST', $url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
            ]
        ]);
    }
}
