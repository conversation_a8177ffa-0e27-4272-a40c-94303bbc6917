<?php

namespace App\Security\Keycloak\DTO;

use App\Security\Keycloak\KeycloakUserApiConsumer;
use Symfony\Component\Security\Core\User\OidcUser;

class GetUserInfoResponse
{
    private function __construct(
        public string $sub,
        public array $roles,
        public bool $emailVerified,
        public string $name,
        public string $preferredUsername,
        public string $givenName,
        public string $familyName,
        public string $email,
    ) {
    }

    public static function fromArray(array $array): self
    {
        return new self(
            sub: $array['sub'],
            roles: $array['resource_access'][KeycloakUserApiConsumer::KEYCLOAK_CLIENT_ID]['roles'],
            emailVerified: $array['email_verified'],
            name: $array['name'],
            preferredUsername: $array['preferred_username'],
            givenName: $array['given_name'],
            familyName: $array['family_name'],
            email: $array['email'],
        );
    }

    public function toOidcUser(): OidcUser
    {
        return new OidcUser(
            roles: $this->roles,
            sub: $this->sub,
            name: $this->name,
            givenName: $this->givenName,
            familyName: $this->familyName,
            preferredUsername: $this->preferredUsername,
            email: $this->email,
            emailVerified: $this->emailVerified,
        );
    }
}
