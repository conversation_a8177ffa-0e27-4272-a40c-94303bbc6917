<?php

namespace App\Security\WebserviceBundle;

use App\Entity\WebserviceBundle\User;
use Symfony\Bundle\FrameworkBundle\Routing\Router;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Symfony\Component\HttpFoundation\Cookie;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Http\Authenticator\AbstractLoginFormAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;

class WebserviceAuthenticator extends AbstractLoginFormAuthenticator
{
    /**
     * @var Router
     */
    private $router;

    /**
     * @var RequestStack
     */
    private $requestStack;

    private $securityLogger;

    /**
     * @var UserProviderInterface
     */
    protected $userProvider;

    /**
     * WebserviceGuardAuthenticator constructor.
     * @param Router $router
     * @param Session $requestStack
     */
    public function __construct(RouterInterface $router, RequestStack $requestStack, LoggerInterface $securityLogger, UserProviderInterface $userProvider) {
        $this->router = $router;
        $this->requestStack = $requestStack;
        $this->securityLogger = $securityLogger;
        $this->userProvider = $userProvider;
    }

    public function getCredentials(Request $request)
    {
        return array(
            'username' => $request->request->get('_username'),
            'password' => $request->request->get('_password'),
            'ip' => $request->getClientIp()
        );
    }

	public function supports(Request $request): bool
	{
		return 'aquitem_webservice_login_check' === $request->attributes->get('_route') && $request->isMethod('POST');
	}

    public function authenticate(Request $request): Passport
    {
        $credentials = $this->getCredentials($request);

        $userBadge = new UserBadge($credentials['username'], function () use ($credentials) {
            return $this->userProvider->loadUserByUsernameAndPassword($credentials['username'], $credentials['password'], $credentials['ip']);
        });

        // Les utilisateurs webservice ne passe pas par la validation de mot de passe de Symfony
        // car il est déjà validé par l'appel au webservice
        return new SelfValidatingPassport($userBadge);
    }

    public function getUser($credentials)
    {
        return $this->userProvider->loadUserByUsernameAndPassword($credentials['username'], $credentials['password'], $credentials['ip']);
    }


    public function checkCredentials($credentials, UserInterface $user)
    {
        return true;
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, $firewallName): ?Response
    {
        $this->securityLogger->info("Connexion réussie : " . $request->request->get('_username'));
        $route = $this->router->generate('aquitem_requeteur_index');
        if ($request->isXmlHttpRequest()) {
            $session = $this->requestStack->getSession();
            if($session->has('referer')) {
                if(!$token->getUser()->checkDroitsSimulationUtilisateur() && !$token->getUser()->getIndependantNonFranchise()) {
                    $route = $session->get('referer');
                    $parseReferer = parse_url($session->get('referer'));
                    if (isset($this->router->match($parseReferer['path'])['_route']) && $this->router->match($parseReferer['path'])['_route'] == 'external_login') {
                        $route = $this->router->generate('aquitem_requeteur_index');
                    }
                } else {
                    if ($token->getUser()->getIndependantNonFranchise()) {
                        $route = $this->router->generate('aquitem_requeteur_participation_independant');
                    }
                    if ($token->getUser()->checkDroitsSimulationUtilisateur()) {
                        $route = $this->router->generate('aquitem_requeteur_simulation_utilisateur');
                    }
                }
            } elseif (!$token->getUser()->checkDroitsSimulationUtilisateur() && !$token->getUser()->getIndependantNonFranchise()) {
                $route = $this->router->generate('aquitem_requeteur_index');
            } else {
                if ($token->getUser()->getIndependantNonFranchise()) {
                    $route = $this->router->generate('aquitem_requeteur_participation_independant');
                }
                if ($token->getUser()->checkDroitsSimulationUtilisateur()) {
                    $route = $this->router->generate('aquitem_requeteur_simulation_utilisateur');
                }
            }
            $result = array(
                'success' => true,
                'redirect' => $route
            );
            $response = new JsonResponse($result);
            $response->headers->setCookie(new Cookie('cookieaide', '1'));
            return $response;
        }
        return new RedirectResponse($this->router->generate('aquitem_requeteur_index'));
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): Response
    {
        $this->securityLogger->info("Connexion échouée : " . $request->request->get('_username'));
        if ($request->isXmlHttpRequest()) {
            $array = array(
                'success' => false,
                'message' => $exception->getMessage()
            );
            return new JsonResponse($array, 401);
        } else {
            $session = $this->requestStack->getSession();
            $session->getFlashBag()->set('danger', 'login.error');
            return new RedirectResponse($this->router->generate('aquitem_requeteur_login', array('auth' => 'failure')));
        }
    }

    public function start(Request $request, AuthenticationException $authException = null): Response
    {
        if ($request->isXmlHttpRequest()) {
            $array = array(
                'success' => false,
                'message' => $authException->getMessage()
            );
            return new JsonResponse($array, 401);
        }
        if ($request->get('_route') !== "bazinga_jstranslation_js") {
            $session = $this->requestStack->getSession();
            $session->set('referer', $request->getUri());
        }
        $lang = $request->server->get('HTTP_ACCEPT_LANGUAGE');
        $routeGeneration = $this->router->generate('aquitem_requeteur_login', ['_locale' => substr($lang, 0, 2)]);
        return new RedirectResponse($routeGeneration);
    }

    public function supportsRememberMe()
    {
        return true;
    }

    protected function getLoginUrl(Request $request): string
    {
        return $this->router->generate('aquitem_requeteur_login');
    }
}
