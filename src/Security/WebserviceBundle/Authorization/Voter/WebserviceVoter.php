<?php

namespace App\Security\WebserviceBundle\Authorization\Voter;

use App\Entity\WebserviceBundle\User;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class WebserviceVoter extends Voter implements VoterInterface
{
    const DROITS_CONTROLLER = 'droits_controller'; // droits sur toutes les actions du controller
    const DROITS_ACTION = 'droits_action'; // droits sur une action de controller

    private $reqStack;

    public function __construct(RequestStack $reqStack)
    {
        $this->reqStack = $reqStack;
        // and other services you want to add
    }

    private $user;
    /**
     * Determines if the attribute and subject are supported by this voter.
     *
     * @param string $attribute An attribute
     * @param mixed  $subject   The subject to secure, e.g. an object the user wants to access or any other PHP type
     *
     * @return bool True if the attribute and subject are supported, false otherwise
     */
    protected function supports($attribute, $subject): bool
    {
        // if the attribute isn't one we support, return false
        return in_array($attribute, array(self::DROITS_CONTROLLER, self::DROITS_ACTION));
    }

    /**
     * Perform a single access check operation on a given attribute, subject and token.
     * It is safe to assume that $attribute and $subject already passed the "supports()" method check.
     *
     * @param string         $attribute
     * @param mixed          $subject
     * @param TokenInterface $token
     *
     * @return bool
     */
    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        /** @var User $this->user */
        $this->user = $token->getUser();

        if (!$this->user instanceof User) {
            // the user must be logged in; if not, deny access
            return false;
        }
        switch ($attribute) {
            case self::DROITS_CONTROLLER:
                switch (get_class($subject)) {
                    case 'App\Controller\RequeteurBundle\ModeleController':
                        return $this->user->checkDroitsGestionModeles();
                        break;
                    case 'App\Controller\RequeteurBundle\SelectionController':
                        return !$this->user->getIndependantNonFranchise() && ($this->user->checkDroitsSelections() || $this->user->canAccessDataviz());
                        break;
                    case 'App\Controller\RequeteurBundle\ParticipationOperationNationaleIndependantController':
                        return $this->user->getIndependantNonFranchise();
                        break;
                    case 'App\Controller\RequeteurBundle\ParticipationOperationNationaleController':
                        return !$this->user->getIndependantNonFranchise();
                        break;
                    case 'App\Controller\RequeteurBundle\PortailEnseigneController':
                        return !$this->user->getIndependantNonFranchise() && $this->user->checkDroitsModuleClient();
                        break;
                    case 'App\Controller\RequeteurBundle\OperationController':
                        return !$this->user->getIndependantNonFranchise() && $this->user->checkDroitsOperations();
                        break;
					case 'App\Controller\RequeteurBundle\StatistiquesEtDocumentsController':
						return $this->user->checkDroitsDocumentsLecture();
						break;
					case 'App\Controller\RequeteurBundle\RegleController':
						$typePromotion = $this->reqStack->getMainRequest()->get('type');
						if($typePromotion == "bri") {
							return $this->user->checkDroitsBri();
						}
						else if($typePromotion == "exclusion") {
							return $this->user->checkDroitsExclusions();
						}
						else {
							return $this->user->checkDroitsPromotions();
						}
						break;
                    case 'App\Controller\RequeteurBundle\CartesCadeauxController':
                        return $this->user->checkDroitsCartesCadeaux();
                        break;
                    case 'App\Controller\RequeteurBundle\CarteWalletController':
                        return $this->user->canAccesPersonnalisationWallets();
                        break;
                    case 'App\Controller\RequeteurBundle\JobsController':
                        return $this->user->checkDroitsConsultationSouscriptionJobs() || $this->user->checkDroitsSouscriptionJobs();
                        break;
                    case 'App\Controller\RequeteurBundle\OperationWalletController':
                        return !$this->user->getIndependantNonFranchise() && $this->user->checkDroitsOperationsWallet();
                        break;
					case 'App\Controller\RequeteurBundle\PointsDeVenteController':
						return $this->user->checkDroitsPointsDeVente();
						break;
                }
                break;
            case self::DROITS_ACTION:
                switch (get_class($subject)) {
                    case 'App\Controller\RequeteurBundle\DefaultController':
                        break;
                    case 'App\Controller\RequeteurBundle\OperationController':
                        switch ($subject->getRequest()->get('_route')) {
                            case 'aquitem_requeteur_operation_mode_de_diffusion':
                            case 'aquitem_requeteur_operation_liste_canaux':
                                return $this->canAccessCanal($subject->getRequest()->get('canal'));
                                break;
                            case 'aquitem_requeteur_operation_charger_modele':
                            case 'aquitem_requeteur_operation_selectionner_modele':
                            case 'aquitem_requeteur_operation_charger_modele_valide':
                                return $this->canAccessCanal($subject->getRequest()->get('type'));
                                break;
                            case 'aquitem_requeteur_operation_ouvrir_modele_sms':
                                return $this->canAccessCanal('sms');
                                break;
//                            case 'aquitem_requeteur_operation_personnaliser_modele_email':
//                            case 'aquitem_requeteur_operation_personnaliser_modele_email_mosaico':
//                                return $this->user->checkDroitsEmailPersoAvancee();
							case 'aquitem_requeteur_operation_offre':
								return $this->user->checkDroitsOffreDemat();
								break;
							case 'aquitem_requeteur_operation_notification_wallet':
								return $this->user->checkDroitsCanalWallet();
								break;
							case 'aquitem_requeteur_operation_open_dataviz':
								return $this->user->canAccessDataviz();
								break;
                        }
                        break;
                    case 'App\Controller\RequeteurBundle\CartesCadeauxController':
                        switch ($subject->getRequest()->get('_route')) {
                            case 'aquitem_requeteur_cartes_cadeaux_activer':
                            case 'aquitem_requeteur_cartes_cadeaux_prolonger':
                                return $this->user->checkDroitsActivationCarteCadeau();
                                break;
                        }
                        break;
                    case 'App\Controller\RequeteurBundle\NotificationController':
                        switch ($this->reqStack->getCurrentRequest()->get('_route')) {
                            case 'aquitem_requeteur_notification_index':
                            case 'aquitem_requeteur_notification_liste':
                            case 'aquitem_requeteur_notification_new':
                            case 'aquitem_requeteur_notification_edit':
                            case 'aquitem_requeteur_notification_image':
                            case 'aquitem_requeteur_notification_delete':
                            case 'aquitem_requeteur_notification_browse_img':
                                return $this->user->checkDroitsSimulationUtilisateur() || $this->user->checkDroitsSiege();
                                break;
                        }
                        break;
                    case 'App\Controller\RequeteurBundle\JobsController':
                        switch ($this->reqStack->getCurrentRequest()->get('_route')) {
                            case 'aquitem_requeteur_jobs_souscriptions':
                            case 'aquitem_requeteur_jobs_liste_souscriptions':
                            case 'aquitem_requeteur_jobs_details_souscriptions':
                            case 'aquitem_requeteur_jobs_liste_details_souscriptions':
                            case 'aquitem_requeteur_jobs_reseau':
                            case 'aquitem_requeteur_jobs_liste_reseau':
                                return $this->user->checkDroitsConsultationSouscriptionJobs();
                                break;
                            case 'aquitem_requeteur_jobs_operations':
                            case 'aquitem_requeteur_jobs_selection_contacts':
                            case 'aquitem_requeteur_jobs_liste_selection_contacts':
                            case 'aquitem_requeteur_jobs_session_operations':
                                return $this->user->checkDroitsSouscriptionJobs();
                                break;
                        }
                        break;
					case 'App\Controller\RequeteurBundle\PointsDeVenteController':
						switch ($this->reqStack->getCurrentRequest()->get('_route')) {
							case 'aquitem_requeteur_pdv_recherche':
							case 'aquitem_requeteur_pdv_liste':
							case 'aquitem_requeteur_pdv_fiche':
								return $this->user->checkDroitsPointsDeVente();
								break;
							case 'aquitem_requeteur_pdv_creation':
								return $this->user->checkDroitsPointsDeVenteCreation();
								break;
						}
						break;
                }
                break;
        }

        return true;
    }

    private function canAccessCanal($canal)
    {
        switch (strtolower($canal)) {
            case 'courrier':
                return $this->user->checkDroitsCanalCourrier();
            case 'email':
                return $this->user->checkDroitsCanalEmail();
            case 'sms':
                return $this->user->checkDroitsCanalSms();
            case 'ftp':
                return $this->user->checkDroitsCanalFtp();
            case 'audio':
                return $this->user->checkDroitsCanalAudio();
        }
        return false;
    }
}
