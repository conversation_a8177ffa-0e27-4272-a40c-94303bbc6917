<?php

namespace App\Security;

use App\Security\Keycloak\KeycloakUserAuthenticator;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Authenticator\Passport\SelfValidatingPassport;

class OidcAuthenticator extends AbstractAuthenticator
{
    public function __construct(
        private readonly UrlGeneratorInterface $urlGenerator,
        private readonly UserProvider $userProvider,
        private readonly KeycloakUserAuthenticator $keycloakAuthenticator,
    ) {
    }

    public function supports(Request $request): ?bool
    {
        $requiredKeys = ['session_state', 'iss', 'code'];

        return '/login/callback' === $request->getPathInfo()
            && count(array_intersect(array_keys($request->query->all()), $requiredKeys)) === count($requiredKeys);
    }

    public function authenticate(Request $request): Passport
    {
        // récupération des paramètres de query
        $sessionSate = $request->query->get('session_state');
        $iss = $request->query->get('iss');
        $code = $request->query->get('code');

        $this->keycloakAuthenticator->authenticate($code);

        return new SelfValidatingPassport(
            new UserBadge($code, function (string $code): ?UserInterface {
                return $this->userProvider->loadUserByIdentifier($code);
            }),
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        /** @var AuthenticatedUser $user */
        $user = $token->getUser();

        // TODO implémenter la création de compte
        //        if (!$user->isRegistrationCompleted()) {
        //            return new RedirectResponse($this->urlGenerator->generate('app_secured_area_first_login'));
        //        }
        return new RedirectResponse($this->urlGenerator->generate('app_homepage'));
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        return null;
    }
}
