<?php

namespace App\Form\DTO;

use App\Entity\LocalUser;
use Symfony\Component\Validator\Constraints as Assert;

class FirstLoginDTO
{
    public function __construct(
        #[Assert\NotBlank]
        public ?string $pseudonym = null,
    ) {
    }

    public function applyToEntity(LocalUser $entity): LocalUser
    {
        $entity->setPseudonym($this->pseudonym);

        return $entity;
    }
}