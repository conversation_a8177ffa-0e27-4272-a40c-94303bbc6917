<?php

namespace App\Form\DTO;

use App\Entity\WebHost\WebHost;
use App\Entity\WebHost\WebHostEnvironnement;
use App\Entity\WebHost\WebHostUrl;
use App\Model\WebsiteVisibility;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class EditWebHostDTO
{
    public function __construct(
        public ?string $name,
        #[Assert\NotBlank]
        public array $urls,
        #[Assert\NotBlank]
        public ?WebsiteVisibility $expectedVisibility,
        #[Assert\NotBlank]
        public ?WebHostEnvironnement $environnement,
    ) {
    }

    #[Assert\Callback]
    public function validateUrls(ExecutionContextInterface $context, mixed $payload): void
    {
        if (count($this->urls) < 1 || empty($this->urls[0])) {
            $context->buildViolation('Vous devez spécifier au moins une URL')
                ->atPath('urls')
                ->addViolation();

            return;
        }

        foreach ($this->urls as $url) {
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                $context->buildViolation('Toutes les URL doivent être valides')
                    ->atPath('urls')
                    ->addViolation();
            }
        }
    }

    public static function fromEntity(WebHost $webHost): self
    {
        return new self(
            name: $webHost->getName(),
            urls: $webHost->getUrls()->map(fn (WebHostUrl $webHostUrl) => $webHostUrl->getUrl())->toArray(),
            expectedVisibility: $webHost->getExpectedVisibility(),
            environnement: $webHost->getEnvironnement(),
        );
    }

    public function toEntity(WebHost $webHost): WebHost
    {
        $submittedDomains = array_map(fn (string $url) => parse_url($url, PHP_URL_HOST), $this->urls);
        $existingUrls = $webHost->getUrls();
        $existingDomains = $existingUrls->map(fn (WebHostUrl $webHostUrl) => $webHostUrl->getDomain())->toArray();

        // création des nouvelles URLs
        foreach ($this->urls as $url) {
            $domain = parse_url((string) $url, PHP_URL_HOST);
            if (!in_array($domain, $existingDomains, true)) {
                $webHostUrl = new WebHostUrl();
                $webHostUrl->setUrl($url);
                $webHost->addUrl($webHostUrl);
                $existingDomains[] = $domain;
            }
        }

        // nettoyage des URLs existantes qui ont été supprimées de la liste
        foreach ($existingUrls as $webHostUrl) {
            if (!in_array($webHostUrl->getDomain(), $submittedDomains, true)) {
                $webHost->removeUrl($webHostUrl);
            }
        }

        // mise à jour des autres champs
        $webHost->setName($this->name);
        $webHost->setExpectedVisibility($this->expectedVisibility);
        $webHost->setEnvironnement($this->environnement);

        return $webHost;
    }
}
