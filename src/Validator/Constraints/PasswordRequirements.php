<?php

namespace App\Validator\Constraints;

use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Constraints\Compound;

#[\Attribute]
class PasswordRequirements extends Compound
{
    public const string PASSWORD_RULE = 'Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule, un chiffre et un caractère spécial.';

    protected function getConstraints(array $options): array
    {
        return [
            new Assert\Length(['min' => 8, 'max' => 255]),
            new Assert\Regex([
                'pattern' => "/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,255}/",
                'message' => self::PASSWORD_RULE,
                'match' => true,
            ]),
        ];
    }
}

