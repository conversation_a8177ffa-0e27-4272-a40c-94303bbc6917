{% extends 'ZeFidPortailClientBundle:ClientActivate:layout.html.twig' %}

{% block body %}
    {{ parent() }}

	<div class="container-fluid">
		<div class="row">
			<div class="column large-12 small-12">
				{% include 'ZeFidPortailClientBundle:Common:messages.html.twig' %}
				{{ form_errors(form) }}
				<div class="column large-12 small-12 activation">
					<div class="activation-puces">
						<div class="float_center">
							<div class="float_center_child">
								<div class="activation-puce activation-puce-active">
									01
								</div>
								<div class="activation-puce-separator">
								</div>
								<div class="activation-puce">
									02
								</div>
								<div class="activation-puce-separator">
								</div>
								<div class="activation-puce">
									03
								</div>
							</div>
						</div>
					</div>
					<div class="divider35">
					</div>
					<h1 class="text-center has-green">{{ 'activation.title' | trans | raw}}</h1>
					<div class="activation-line"></div>
					<div class="activation-etape has-green">
						{{ 'activation.activation1' | trans }}
					</div>
					<div class="row">
						<div class="column large-5 large-centered small-12 small-centered activation-etape1-text">	
							{{ 'activation.enregistrer' | trans }}
						</div>
					</div>
					<div class="row">
						<div class="large-5 small-12 activationForm1">
							{{ form_start(form, {method:'post'}) }}
							<div class="row">
								<div class="column medium-5 small-12">
									<span class=""><label for="clientActivate_codeCarte" class="required">{{'activation.codeCarte' | trans | raw }}</label></span>
								</div>
								<div class="column medium-7 small-12">
									{{ form_widget(form.codeCarte) }}
								</div>
							</div>
							<div class="row">
								<div class="column medium-5 small-12">
									<span class=""><label for="clientActivate_codeAcces" class="required">{{'activation.codeAcces' | trans | raw }}</label></span>
								</div>
								<div class="column medium-7 small-12">
									{{ form_widget(form.codeAcces) }}
									<div class="h-captcha" data-sitekey="b406ce48-eba6-4f70-be64-c368b71ef81c"></div>
								</div>
							</div>
							<div class="formatDate">
								{{ 'formatDate'|trans }}
							</div>
							<div class="mtl small-mbl text-center">
								{{ form_end(form) }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script src='https://www.hCaptcha.com/1/api.js' async defer></script>
{% endblock %}