{% extends 'ZeFidPortailClientBundle:ClientActivate:layout.html.twig' %}
{% form_theme form 'ZeFidPortailClientBundle:Form:fields.html.twig' %}

{% block body %}
    {{ parent() }}

	<div class="container-fluid">
		<div class="row">
			<div class="column large-12 small-12">
				{% include 'ZeFidPortailClientBundle:Common:messages.html.twig' %}
				{{ form_errors(form) }}
				<div class="column large-12 small-12 activation">
					<div class="activation-puces">
						<div class="float_center">
							<div class="float_center_child">
								<div class="activation-puce">
									01
								</div>
								<div class="activation-puce-separator">
								</div>
								<div class="activation-puce activation-puce-active">
									02
								</div>
								<div class="activation-puce-separator">
								</div>
								<div class="activation-puce">
									03
								</div>
							</div>
						</div>
					</div>
					<div class="divider35">
					</div>
					<h1 class="text-center has-green">{{ 'activation.title' | trans | raw}}</h1>
					<div class="activation-line"></div>
					<div class="activation-etape has-green">
						{{ 'activation.activation2' | trans }}
					</div>
					<div class="row">
						<div class="column large-6 large-centered small-12 small-centered activation-etape1-text">
							{{ 'activation.compte' | trans }}
						</div>
					</div>
					{{ form_start(form, {method:'post'}) }}
					<div class="fieldsetWith large-8 large-centered activation-compte">
						<div class="row">
							<div class="{{getColBootstrap(responsive,menuLateral,{colXs:12,colSm:6})}} first">
								<div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg color mrs" aria-hidden="true"></i>
									<span class="ralewayBold has-grey">{{ form_label(form.civilite) }}* :</span>
									<div class="has-error">{{ form_errors(form.civilite)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.civilite) }}</div>
								</div>
								<div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg color mrs" aria-hidden="true"></i>
									<span class="ralewayBold has-grey">{{ form_label(form.nom) }}* :</span>
									<div class="has-error">{{ form_errors(form.nom)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.nom) }}</div>
								</div>
								<div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg color mrs" aria-hidden="true"></i>
									<span class="ralewayBold has-grey">{{ form_label(form.prenom) }}* :</span>
									<div class="has-error">{{ form_errors(form.prenom)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.prenom) }}</div>
								</div>
								<div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg color mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ 'compte.DateDeNaissance'|trans }}* :</span>
									<div class="has-error">{{ form_errors(form.dateNaissance)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.dateNaissance) }}</div>
								</div>
								<div class="checked mobile">
									<i class="fa fa-arrow-circle-right  fa-lg color mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ 'compte.Mobile'|trans }}
										<div class="has-error">{{ form_errors(form.telephoneMobile)}}</div></span>
									<div class="form-group form-group--margin">{{ form_widget(form.telephoneMobile)}}</div>
								</div>
								<div class="checked fixe">
									<i class="fa fa-arrow-circle-right  fa-lg color mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ 'compte.telFixe'|trans }}
										<div class="has-error">{{ form_errors(form.telephoneFixe)}}</div></span>
									<div class="form-group form-group--margin">{{ form_widget(form.telephoneFixe)}}</div>
								</div>
								<div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg color mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ 'compte.Email'|trans }}* :
										<div class="has-error">{{ form_errors(form.email)}}</div>
										<div class="form-group form-group--margin">{{ form_widget(form.email)}}</div>
									</span>
								</div>
								<div class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg color mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ 'client.magasinRattachement'|trans }}*</span>
									<div class="has-error">{{ form_errors(form.magasinId)}}</div>
									<div class="form-group form-group--margin">{{ form_widget(form.magasinId)}}</div>
								</div>
							</div>
							<div class="{{getColBootstrap(responsive,menuLateral,{colXs:12,colSm:6})}}">
								<div id="adresse" class="checked">
									<i class="fa fa-arrow-circle-right  fa-lg color mrs" aria-hidden="true"></i>
									<span class="ralewayBold">{{ 'compte.Adresse'|trans }}</span>
									<div class="form-group form-group--margin">
										{{ form_row(form.numero)}}
										{{ form_row(form.voie)}}
										{{ form_row(form.escalier)}}
										{{ form_row(form.batiment)}}
										{{ form_row(form.lieuDit)}}
										<div>
											<label for="client_codepostal" class="required">{{ 'client.codepostal'|trans }} :</label>
											<div class="has-error">{{ form_errors(form.codepostal)}}</div>
											{{ form_widget(form.codepostal) }}
										</div>
										<div>
											<label for="client_ville" class="required">{{ 'client.ville'|trans }} :</label>
											<div class="has-error">{{ form_errors(form.ville)}}</div>
											{{ form_widget(form.ville) }}
										</div>
										{{ form_row(form.codePaysClient)}}
									</div>
								</div>
							</div>
							<div class="column small-12 consentement">
								<p>{{ 'activation.consentement' | trans }}</p>
							</div>
							<div class="column small-12 is-flex flex-centered is-margin-bottom-4">
								<button type="submit" class="btn btn-default btn-valider-modif btn-fr  ralewayBold"><i class="fa fa-check-circle fa-lg mrs" aria-hidden="true"></i>{{'activation.save'|trans}}</button>
							</div>
						</div>
						<p>{{'client.mentionsObligatoire'|trans}}</p>
						<p class="cnilMessageInfoRecueillies has-size-7">
							{{ "cnil.messageCNIL"|trans}}
						</p>
					</div>
					{{ form_widget(form._token) }}
					{{ form_end(form, {render_rest:false}) }}
				</div>
			</div>
		</div>
	</div>

{% endblock %}

