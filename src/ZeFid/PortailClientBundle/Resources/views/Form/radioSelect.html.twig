<div class="radioSelect">
	<span class="arrow glyphicon glyphicon-collapse-down">&nbsp;</span>
	<div class="radio">
		{% for id, choice in form.vars.choices %}
			<label for="{{ form.vars.id }}{{choice.value}}"><input{% if form.vars.value[id] %} checked="checked"{% endif %} type="radio" id="{{ form.vars.id }}{{choice.value}}" value="{{choice.value}}" name="{{ form.vars.full_name }}"/><span class="flag flag{{choice.value}}"></span>{% if showText == 1 %} {{choice.label}}{% endif %}</label>
			{% endfor %}
			{% do form.setRendered %}
	</div>
</div>