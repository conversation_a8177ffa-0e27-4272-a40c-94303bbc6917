@charset "UTF-8";
body {
  background-color: #fff; }

/**
 * Identifiez-vous
 */
.vertical h1 {
  background: #009fe3; }

.vertical input[type="text"] {
  border-color: #cfcfcf;
  background: rgba(255, 255, 255, 0.4);
  color: #000; }

.banner-optin button {
  background-color: #009fe3;
  font-weight: 700;
  padding: 1rem 2rem; }

.vertical {
  background-color: #fff; }

#divBienvenue p {
  color: black; }

#divBienvenue hr {
  border-color: #dbdbdf; }

/**
*Menu Page connecté
*/
.menu {
  background: #009fe3; }

/**** FOOTER ****/
.footer {
  background: #fff;
  color: #009fe3; }

.footer-top {
  border-bottom: 1px solid #009fe3; }

.footer-icon {
  background: #009fe3;
  color: #fff; }

a.footer-bottom-link, a.footer-bottom-link:focus, a.footer-bottom-link:hover {
  color: #009534; }

#bienvenue {
  color: #000;
  border-bottom-color: rgba(0, 0, 0, 0.1); }

input[type="text"] {
  color: black;
  border-color: #CFCFCF; }

input[type="text"]::-webkit-input-placeholder {
  color: black; }

input[type="text"]:-moz-placeholder {
  color: black; }

input[type="text"]::-moz-placeholder {
  color: black; }

input[type="text"]:-ms-input-placeholder {
  color: black; }

.contentClient input, .contentClient .radio, .contentClient select {
  color: black;
  border-color: #cfcfcf; }

.contentClient input::-webkit-input-placeholder {
  color: black; }

.contentClient input:-moz-placeholder {
  color: black; }

.contentClient input::-moz-placeholder {
  color: black; }

.contentClient input:-ms-input-placeholder {
  color: black; }

.contentClient input::placeholder {
  color: black; }

.contentClient .grisOnWhiteBloc {
  color: black; }

.contentClient {
  background-color: #fff;
  color: #7b7b7b; }

.contentClient .gris {
  background-color: #EFEFEF; }

.btn-deconnexion {
  background-color: #009fe3; }

.menu ul li a {
  color: #fff; }

.pageClient .open {
  background-color: #009fe3; }

.btn {
  background-color: #009fe3;
  color: #fff; }

.btn:hover, .btn:focus, .vertical button:hover {
  background-color: #1B74CA; }

.emptyInput input[name=client\[telephoneMobile\]], .emptyInput input[name=client\[email\]] {
  border-color: #E20C0C; }

#mesInformations .ralewayBold label, .offreUtilisee, #ptsatteind {
  color: #009fe3; }

.code_barre {
  background-color: #fff;
  color: #000;
  display: block;
  padding: 0 1em; }

.color {
  color: #009fe3; }

.gris.bubble::after {
  border-color: #EFEFEF transparent; }

.menu .highlight {
  background-color: #54bfec;
  color: #fff; }

.menu ul li {
  border-bottom-color: #54bfec; }

@media only screen and (min-width: 40em) {
  .border-right {
    border-right: 1px solid #009fe3; } }
@media only screen and (min-width: 64em) {
  .code_barre {
    display: inline-block; } }
.activation-puce-active {
  background-color: #009fe3;
  color: white; }

/************************************************
*                                               *
*             Utility classes                   *
*                                               *
*************************************************/
.has-error {
  color: red !important; }

.has-green {
  color: #009534 !important; }

.has-blue {
  color: #009fe3 !important; }

.has-grey, .has-grey label {
  color: #7a7a7a !important; }

.has-size-7 {
  font-size: 0.7rem; }

.has-size-15 {
  font-size: 1.5rem; }

.has-size-12 {
  font-size: 1.2rem; }

.has-size-10 {
  font-size: 1rem; }

.has-weight8 {
  font-weight: 800; }

.is-flex {
  display: flex; }

.flex-centered {
  justify-content: center; }

.is-align-centered {
  align-items: baseline; }
  .is-align-centered [type=checkbox], .is-align-centered [type=file], .is-align-centered [type=radio] {
    margin: 0; }

.is-margin-bottom-4 {
  margin-bottom: 4rem; }

/************************************************
*                                               *
*             Alphega style                     *
*                                               *
*************************************************/
footer {
  font-weight: 800; }
  footer > div {
    padding: 0 1rem; }
  footer .footer-top {
    border-bottom: 1px solid #009534; }
    footer .footer-top div {
      padding: .8rem 0; }
    footer .footer-top a {
      text-decoration: none; }
  footer .footer-bottom div {
    padding: .8rem 0; }

header {
  text-align: center;
  z-index: 3;
  height: 7.0625rem;
  background: #009fe3 url("../images/HEAD_activation.png") 50%;
  position: relative; }

.vertical h1 {
  font-size: 1.7rem; }

img.centerElement {
  margin: 1rem auto; }

.button-like {
  background: #009fe3;
  padding: 1.7rem;
  text-align: center;
  text-transform: none;
  margin-bottom: 1rem; }
  .button-like p {
    color: white;
    margin: 0; }
    .button-like p.has-margin-top-onehalf {
      margin-top: 0.5rem; }

#activation-carte .carteButton--container {
  display: flex;
  justify-content: center; }
  #activation-carte .carteButton--container div.carteButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18.125rem; }
    #activation-carte .carteButton--container div.carteButton a {
      flex-shrink: 0;
      position: relative;
      left: -1.1rem;
      font-weight: bold;
      padding: 1rem 1.5rem; }
    #activation-carte .carteButton--container div.carteButton img {
      z-index: 10; }

.activation-puce {
  float: left;
  width: 3.5em;
  height: 3.5em;
  border-radius: 50%;
  background-color: #e4e4e4;
  color: black;
  text-align: center;
  line-height: 3.5em;
  font-size: 1.2em;
  font-weight: bold; }

.activation-puce-active {
  background-color: #009fe3;
  color: white; }

.activation-puce-separator {
  width: 15px;
  height: 2.2em;
  float: left;
  border-bottom: 2px solid #e4e4e4; }

.activation-line {
  width: 15em;
  margin: auto;
  border-bottom: 2px solid #009534; }

.activation-etape {
  text-align: center;
  padding-top: 20px;
  padding-bottom: 50px;
  color: #009fe3; }

.activation input {
  color: #333;
  border: 1px solid #cacaca;
  outline: none;
  width: 100%;
  padding: 14px 20px;
  display: block;
  transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  border-radius: 0;
  height: auto;
  background: #FFF; }

.activation label {
  color: #7a7a7a; }

.activationForm1 label {
  text-align: right;
  color: black;
  padding-top: .5rem; }
  .activationForm1 label[for='clientActivate_captcha'] {
    margin-bottom: .2rem; }
.activationForm1 .label-captcha label {
  padding: unset;
  line-height: 1; }

.activation-etape1-text {
  line-height: 1.2em;
  padding: 0 0 45px 0; }

.activation-etape1-text, .activation-etape3-text1, .activation-etape3-text2 {
  color: #000; }

.activation-compte {
  border: 1px solid #efefef; }

.activation-compte input {
  padding: 0.5rem; }

.activation-compte input[type="checkbox"] {
  display: inline;
  width: inherit; }

.activation-confirm-checkboxes {
  margin: 25px 0 0 15px; }

.activation-button-confirmation {
  padding: 13px 25px; }

.activation-compte-confirmation-label {
  text-indent: -27px;
  padding-left: 27px;
  line-height: 0.4em;
  cursor: pointer; }

.activation-compte-cgv-label {
  text-indent: -27px;
  padding-left: 27px;
  line-height: 0.4em;
  cursor: pointer; }

.activation-etape3-text1 {
  padding: 0 0 15px 0;
  font-weight: bold;
  font-size: 1.2em; }

.activation-etape3-text2 {
  padding: 0 0 20px 0; }

#menuWrap {
  z-index: 2; }
  #menuWrap .open {
    z-index: 3; }

.pageClient .open {
  z-index: 3; }

.menu .fixed {
  position: relative;
  top: 8rem; }
.menu .close {
  top: -0.75rem; }
.menu .menu-list {
  margin-top: 1rem; }

.separator {
  border-bottom: solid #009534 2px;
  width: 11rem;
  margin: auto; }
  .separator.has-blue {
    border-bottom: solid #009fe3 2px;
    width: 11rem;
    margin: auto; }

.titre h2 {
  margin-bottom: .6rem;
  padding-bottom: unset; }
.titre p {
  margin-top: 1rem; }

[id^='client_enfants_'] {
  display: flex;
  flex-wrap: wrap; }
  [id^='client_enfants_'] div {
    margin-right: 0.5rem; }
    [id^='client_enfants_'] div .btn_add {
      padding: unset;
      background-color: #009534; }
    [id^='client_enfants_'] div .delete span, [id^='client_enfants_'] div .btn_add span {
      display: inline-block;
      height: 2.4375rem;
      width: 2.4375rem;
      line-height: 2.4375rem;
      text-align: center; }
    [id^='client_enfants_'] div.delete-btn, [id^='client_enfants_'] div.add-btn {
      display: flex;
      align-items: center;
      padding-top: 0.6rem; }

#prototypeEnfants div {
  margin-right: 0.5rem; }
  #prototypeEnfants div .btn_add {
    padding: unset;
    background-color: #009534; }
  #prototypeEnfants div .delete span, #prototypeEnfants div .btn_add span {
    display: inline-block;
    height: 2.4375rem;
    width: 2.4375rem;
    line-height: 2.4375rem;
    text-align: center; }
  #prototypeEnfants div.delete-btn, #prototypeEnfants div.add-btn {
    display: flex;
    align-items: center;
    padding-top: 0.6rem; }

.form-sub-cat {
  padding-left: 1.8rem; }

.consentement {
  margin-top: 4rem; }

span.circle {
  display: inline-block;
  height: 1.3rem;
  width: 1.3rem;
  border-radius: .65rem;
  color: white;
  padding-top: 0.14rem;
  padding-left: 0.3rem;
  line-height: 1.2; }
  span.circle.green {
    background-color: #006c26; }

#avantages-items {
  flex-wrap: wrap;
  justify-content: space-evenly;
  align-content: space-between;
  align-items: stretch;
  margin-bottom: 4rem; }
  #avantages-items .bubble-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    align-content: space-between;
    width: 35%;
    margin-top: 2rem; }
    #avantages-items .bubble-container .bubble {
      flex: 2;
      text-align: center; }
    #avantages-items .bubble-container button img {
      margin: auto; }
  #avantages-items .titreBubble {
    position: unset;
    left: unset;
    right: unset;
    top: unset;
    margin: unset;
    width: unset; }
  #avantages-items .bubble {
    width: 100%;
    margin: unset; }

#ptsatteind {
  font-size: .9rem; }

.form-group--margin {
  margin-left: 1.8rem; }

a.site {
  text-decoration: underline; }

.site-arrow {
  background-color: white;
  height: 1.2rem;
  width: 1.2rem;
  line-height: 1.2rem;
  -webkit-border-radius: 0.8rem;
  -moz-border-radius: 0.8rem;
  border-radius: 0.8rem;
  margin-right: .5rem; }

@media screen and (max-width: 48rem) {
  #menuWrap {
    z-index: 6; }

  .menu .fixed {
    top: 2rem; }
  .menu .close {
    top: -2.75rem; } }
@media screen and (max-width: 27rem) {
  #avantages-items .bubble-container {
    width: 100%; }

  .listeMenu .btn-deconnexion {
    width: 10rem;
    padding: 1rem 0; }

  .header {
    height: 143px;
    background-image: url("../images/carte.png");
    background: #009534;
    background: url("../images/carte.png"), -moz-linear-gradient(top, #009534 0%, #00fb58 100%);
    background: url("../images/carte.png"), -webkit-linear-gradient(top, #009534 0%, #00fb58 100%);
    background: url("../images/carte.png"), linear-gradient(to bottom, #009534 0%, #00fb58 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=$green, endColorstr=lighten($green, 20), GradientType=0);
    background-repeat: no-repeat;
    background-position: 50%; } }

/*# sourceMappingURL=app.css.map */
