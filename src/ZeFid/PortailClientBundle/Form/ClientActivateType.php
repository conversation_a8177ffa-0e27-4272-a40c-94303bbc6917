<?php

namespace ZeFid\PortailClientBundle\Form;

use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\CallbackValidator;
use Symfony\Component\Form\FormError;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Alienor\AquitemWebServiceParserBundle\Entity\GlobalDefinitions;
use Alienor\AquitemWebServiceParserBundle\Form\BaseType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;

class ClientActivateType extends BaseType {

    public function buildForm(FormBuilderInterface $builder, array $options) {
        $builder
        ->add(
            'codeCarte', TextType::class, array(
                'required' => true,
                'label' => 'activation.codeCarte',
            )
        )
        ->add(
            'codeAcces', TextType::class, array(
                'required' => true,
                'label' => 'activation.codeAcces',
            )
        )
        ->add(
            'submit', SubmitType::class, array(
                'label' => 'login.submit',
                'attr' => array(
                    'class'=>'btn btn-default btn-valider-modif ralewayBold mlxl',
                ),
            )
        );
    }
    
    public function configureOptions(OptionsResolver $resolver) {
        parent::configureOptions($resolver);
        $resolver->setDefaults(
            array()
        );
    }
    
    public function getBlockPrefix() {
        return 'clientActivate';
    }
}