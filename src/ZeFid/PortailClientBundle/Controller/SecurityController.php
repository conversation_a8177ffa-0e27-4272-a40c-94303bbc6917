<?php

namespace ZeFid\PortailClientBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Route;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Template;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityController extends Controller
{

    /**
     * @Route("/" , name="home_login")
     * @Template()
     */
    public function loginAction(Request $request)
    {
        $session = $this->get('session');

        $form = $this->get('form.factory')->create('ZeFid\PortailClientBundle\Form\LoginType');

        if ($this->get('security.authorization_checker')->isGranted('IS_AUTHENTICATED_FULLY')) {
            return $this->redirect($this->generateUrl('home_vue'));
        }
		$numbers = $this->get('captcha')->generateCaptchaNumber($session);
		$label_captcha = $numbers;

        return array(
            'label_captcha' => $label_captcha,
            'form' => $form->createView()
        );
    }

    /**
     * @Route("/statut/{statut}", name="home_login_statut", defaults={"statut"= 200})
     */
    public function loginStatutAction(Request $request, $statut = 200)
    {
        return new Response(
            $this->renderView('ZeFidPortailClientBundle:Security:loginStatut.html.twig', array()),
            $statut // return code
        );
    }

    /**
     * @Route("/login_check")
     */
    public function login_checkAction()
    {
        return array();
    }

    protected function generateCaptchaNumber($session) {
        $first = rand(1,9);
        $second = rand(1,9);
        $session->set('captcha_first', $first);
        $session->set('captcha_second', $second);
        return array($first,$second);
    }
}