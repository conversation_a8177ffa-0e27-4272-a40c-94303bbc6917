<?php

namespace ZeFid\PortailClientBundle\Services;

use Symfony\Component\DependencyInjection\ContainerInterface;

use Aquitem\FidBundle\Entity\Operation;
use Aquitem\FidBundle\Entity\AdressePerso;
use Aquitem\FidBundle\Entity\Ville;
use Aquitem\FidBundle\Entity\Client;
use Aquitem\FidBundle\Entity\Cheque;
use BG\BarcodeBundle\Util\Base1DBarcode as barCode;

/**
 * Gestion de la génération des chèques
 *
 * <AUTHOR> <<EMAIL>>
 */
class CartesGenerateur
{
    /**
     * Dossier des cartes générés
     */
    public  $cartes_generes_dir = 'cartes';

    /**
     * Container d'injection de dependences
     */
    protected $container;

    /**
     * Traducteur
     */
    protected $translator;

    /**
     * Paramètrage des zones
     */
    protected static $zones = array(
        // En tête
    	'background-image' => array(
    		'x' => 5, 'y' => 5,
    		'w' => 200, 'h' => 0
    	),
    	'num_carte' => array(
    		'x' => 35, 'y' => 34,
    		'w' => 20, 'h' => 5
    	),
    	'code_barre' => array(
    		'x' => 34, 'y' => 42,
    		'w' => 0, 'h' => 20
    	),
    	'texte_code_barre' => array(
    		'x' => 48, 'y' => 62,
    		'w' => 0, 'h' => 5
    	),
    );

    /**
     * Dossier de stockage
     */
    protected $dir;

    public function __construct(ContainerInterface $container)
    {
        $this->dir = $container->getParameter('kernel.root_dir') . '/../web/';

        $this->container = $container;
        $this->translator = $container->get('translator');
    }

    /**
     * Traduit une chaîne
     */
    protected function trans($label, array $parameters = array())
    {
        return $this->translator->trans($label, $parameters);
    }


    /**
     * Génère le PDF contenant les chèques d'une opération
     *
     * @param Operation l'opération
     */
    public function genererPdf($client)
    {
        $pdf = $this->createPdf();
        $pdf->AddPage();
        $this->addBackgroundImage('bundles/zefidportailclient/images/FOND_carte.png',$pdf);
        $this->addNumCarte($client->getId(),$pdf);
        $this->addCodeBarre($client->getCodeCarte(),$pdf);
        $pdf->SetFont('Arial','B',16);
		
        $pdf->output($this->dir.'/'.$this->cartes_generes_dir.'/'.$client->getCodeCarte().'.pdf');
    }

    /**
     * Créer un PDF
     */
    protected function createPdf()
    {
        $pdf = $this->container->get('pdf.handling')->createPdf();
        $pdf->addFont('EAN13');
        $pdf->addFont('Arial');
        $pdf->addFont('Arial-bold');
        $pdf->setAutoPageBreak(false);
        $pdf->setTextColor(0, 0, 0);
        return $pdf;
    }

    /**
     *
     * @param $imageBackground imageBackground
     * @param $pdf
     */
    protected function addBackgroundImage($imageBackground,$pdf){
    	$pdf->Image($imageBackground,self::$zones['background-image']['x'],self::$zones['background-image']['y'],
    			self::$zones['background-image']['w'],self::$zones['background-image']['h']);
    }

    /**
     *
     * Num Carte
     * @param unknown $pdf
     */
    protected function addNumCarte($numCarte,$pdf){
    	$numCarte = utf8_decode($this->trans('pdf.carte_num').' : '.$numCarte);
    	    
    	$pdf->setFont('arial', 'b', 10);
    	$pdf->setXY(self::$zones['num_carte']['x'], self::$zones['num_carte']['y']);
    
    	$pdf->cell(self::$zones['num_carte']['y'], 5, $numCarte, 0, 2);
    }

    /**
     *
     * @param $code code
     * @param $pdf
     */
    protected function addCodeBarre($code,$pdf){
    	$myBarcode = new barCode();
    	$myBarcode->savePath = 'code_barre/';
    	$bcPathAbs = $myBarcode->getBarcodePNGPath($code, 'EAN13', 3, 120);
    	$pdf->Image($bcPathAbs,self::$zones['code_barre']['x'],self::$zones['code_barre']['y'],
    			self::$zones['code_barre']['w'],self::$zones['code_barre']['h']);
    	$pdf->setFont('arial', null, 7);
    	$pdf->setXY(self::$zones['texte_code_barre']['x'], self::$zones['texte_code_barre']['y']);
    	$pdf->cell(self::$zones['texte_code_barre']['y'], 5, $code, 0, 2);
    }
}