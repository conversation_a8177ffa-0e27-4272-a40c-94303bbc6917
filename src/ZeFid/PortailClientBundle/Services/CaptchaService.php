<?php

namespace ZeFid\PortailClientBundle\Services;
use Symfony\Component\HttpFoundation\Session\Session;use Symfony\Component\VarDumper\VarDumper;

class CaptchaService {

    private $session;

    public function __construct(Session $session) {
        $this->session = $session;
    }
    
	/**
	 * @param $session
	 *
	 * @return string
	 */
	public function generateCaptchaNumber() {
		$first = rand(1, 9);
		$second = rand(1, 9);
		$this->session->set('captcha_first', $first);
		$this->session->set('captcha_second', $second);
		return $first . ' + ' . $second . ' =';
	}

	/**
	 * Check if provided result is egal to expected one
	 *
	 * @param $session
	 * @param $captcha
	 *
	 * @return bool
	 */
	public function checkCaptcha($captcha) {
        $expected = $this->session->get('captcha_first') + $this->session->get('captcha_second');
        return md5($expected) === md5($captcha);
	}
}