<?php

namespace App\Controller;

use App\Config\ConfigLoader;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class HomeController extends AbstractController
{
    #[Route('/', name: 'app_homepage')]
    public function homepage(ConfigLoader $configLoader): Response
    {
        $assistants = $configLoader->getAvailableAssistants();

        return $this->render('homepage/index.html.twig', [
            'assistants' => $assistants,
        ]);
    }
}
