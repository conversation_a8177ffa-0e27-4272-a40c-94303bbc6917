<?php

namespace App\Controller\RequeteurBundle;

use App\Factory\CRUDFactory;
use App\Services\RequeteurBundle\CRUDManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Annotation\Route;
use App\Services\WebserviceBundle\WebservicePointsDeVente;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

#[Route(path: '/points-de-vente')]
class PointsDeVenteController extends AbstractRequeteurController
{
	protected $webservicePointsDeVente;

	protected $crudManager;

	public function __construct(WebservicePointsDeVente $webservicePointsDeVente, CRUDFactory $CRUDFactory)
	{
		$this->webservicePointsDeVente = $webservicePointsDeVente;
		$this->crudManager = $CRUDFactory->create($webservicePointsDeVente);
	}

	#[Route(path: '/recherche/{idSession}', name: 'aquitem_requeteur_pdv_recherche')]
	public function recherchePDVAction(Request $request, $idSession = 0): Response
	{
		$parameters = $this->crudManager->recherche($idSession);
		return $this->render('@Requeteur/PointsDeVente/recherche.html.twig', $parameters);
	}

	#[Route(path: '/clients-session', name: 'aquitem_requeteur_pvd_session_recherche', options: ['expose' => true])]
	public function sessionRecherchePDVAction(Request $request): JsonResponse
	{
		$datas = $this->crudManager->sessionRecherche();
		return new JsonResponse($datas);
	}

	#[Route(path: '/liste/{idSession}', name: 'aquitem_requeteur_pdv_liste', options: ['expose' => true])]
	public function listePDVAction(Request $request, $idSession = 0): Response
	{
		$template = '@Requeteur/PointsDeVente/resultats.html.twig';
		// Si requête ajax
		if ($request->isXmlHttpRequest()) {
			$template = '@Requeteur/PointsDeVente/Includes/searchResultDatas.html.twig';
		}
		$parameters = $this->crudManager->listeResultats($idSession);
		if(empty($parameters)) {
			return $this->redirectToRoute('aquitem_requeteur_pdv_recherche');
		}
		return $this->render($template, $parameters);
	}


	#[Route(path: '/fiche/{idPdv}/{idSession}', name: 'aquitem_requeteur_pdv_fiche', defaults: ['idSession' => '0'], options: ['expose' => true])]
	public function fichePDVAction(Request $request, $idPdv, $idSession): Response
	{
		$session = $request->getSession();
		if($idSession and $session->get($this->webservicePointsDeVente::NOM_SESSION) and !array_key_exists($idSession, $session->get($this->webservicePointsDeVente::NOM_SESSION))) {
			$idSession = 0;
		}
		$donneesFiche = $this->webservicePointsDeVente->afficherDonneesPointDeVente(['idPointDeVente' => $idPdv]);
		$template = '@Requeteur/PointsDeVente/fiche.html.twig';
		$markers['codePdv'] = $idPdv;
		if(!$donneesFiche->hasError()) {
			$donneesCompl = $donneesFiche->getValeurs()[3]['valeurs'];
			$donneesCoord = $donneesFiche->getValeurs()[2]['valeurs'];
			$valeursPredefinies = $donneesFiche->getValeurs()[1]['valeurs'];
			$markers = $this->crudManager->donneesFiche($donneesCoord, $donneesCompl, $valeursPredefinies);
			$markers['pdvModifiable'] = filter_var($donneesFiche->getValeurs()[0]['valeur'], FILTER_VALIDATE_BOOLEAN);
			$markers['codeMagasin'] = $markers['donneesCoord']['CMAG']['valeur'];
			$markers['codeSite'] = $markers['donneesCoord']['CSIT']['valeur'];
			$markers['nomPdv'] = $markers['donneesCoord']['LMAG']['valeur'];
			$markers['dateOuverture'] = $markers['donneesCoord']['OUVT']['valeur'];
			$markers['dateFermeture'] = $markers['donneesCoord']['FERM']['valeur'];
			// Si requête ajax
			if ($request->isXmlHttpRequest()) {
				$template = '@Requeteur/PointsDeVente/Includes/donnees_pdv.html.twig';
			}
		}
		$markers['idPdv'] = $idPdv;
		$markers['idSession'] = $idSession;
		return $this->render($template, $markers);
	}

	#[Route(path: '/creation', name: 'aquitem_requeteur_pdv_creation')]
	public function creationPDVAction(): Response
	{
		$donneesFiche = $this->webservicePointsDeVente->saisieNouveauPointDeVente();
		$donneesCompl = $donneesFiche->getValeurs()[2]['valeurs'];
		$donneesCoord = $donneesFiche->getValeurs()[1]['valeurs'];
		$valeursPredefinies = $donneesFiche->getValeurs()[0]['valeurs'];
		$markers = $this->crudManager->donneesFiche($donneesCoord, $donneesCompl, $valeursPredefinies);
		$template = '@Requeteur/PointsDeVente/fiche.html.twig';
		return $this->render($template, $markers);
	}
}
