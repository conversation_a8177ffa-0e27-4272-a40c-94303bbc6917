<?php

namespace App\Controller\RequeteurBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;
use App\Services\WebserviceBundle\WebserviceIdentification;
use App\Services\WebserviceBundle\WebserviceSimulationUtilisateur;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Security\WebserviceBundle\User\WebserviceUserProvider;
use App\Entity\WebserviceBundle\User;
use App\Services\WebserviceBundle\DashboardBridge;
use App\Services\WebserviceBundle\WebserviceCarteWallet;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;
use App\Services\WebserviceBundle\WebserviceExclusion;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use App\Services\WebserviceBundle\WebserviceContactsEnseigne;
use App\Repository\RequeteurBundle\NotificationRepository;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

#[Route("/simulation")]
class SimulationUtilisateurController extends AbstractRequeteurController
{
    protected $webservice;
	protected $webserviceExclusion;
	protected $webserviceContactsEnseigne;
	protected $webserviceCarteWallet;
	protected $webserviceIdentification;
    protected $notificationRepository;

    public function __construct(
		WebserviceSimulationUtilisateur $webservice,
		WebserviceExclusion $webserviceExclusion,
		WebserviceContactsEnseigne $webserviceContactsEnseigne,
		WebserviceCarteWallet $webserviceCarteWallet,
		WebserviceIdentification $webserviceIdentification,
		NotificationRepository $notificationRepository
	)
    {
        $this->webservice = $webservice;
        $this->webserviceExclusion = $webserviceExclusion;
        $this->webserviceContactsEnseigne = $webserviceContactsEnseigne;
        $this->webserviceCarteWallet = $webserviceCarteWallet;
		$this->webserviceIdentification = $webserviceIdentification;
        $this->notificationRepository = $notificationRepository;
    }

	#[Route('/liste-utilisateurs', name: 'aquitem_requeteur_simulation_utilisateur', options: ['expose' => true])]
    public function indexAction(Request $request, EventDispatcherInterface $eventDispatcher, TokenStorageInterface $tokenStorage): Response|RedirectResponse
    {
        /* déconnexion */
        $session = $request->getSession();
		$lang = $request->server->get('HTTP_ACCEPT_LANGUAGE');
        $user = $session->get('real_user');
        if ($user) {
			$this->webserviceIdentification->logout();
            $session->set(WebserviceUserProvider::USER_KEY, $user);
            $session->set(WebserviceUserProvider::USER_MENU_KEY, false);
            $session->remove('real_user');
            $session->remove(WebserviceUserProvider::USER_PROFIL_KEY);
            $user = json_decode($user, true);
            $user = User::fromArray($user);

            $token = new UsernamePasswordToken($user, 'main', $user->getRoles());
            $tokenStorage->setToken($token);
            $session->set('_security_main', serialize($token));

            $event = new InteractiveLoginEvent($request, $token);
            $eventDispatcher->dispatch($event, "security.interactive_login");
        }
		if (!$this->getUser()) {
			return $this->redirectToRoute('aquitem_requeteur_login', ['_locale' => substr($lang, 0, 2)]);
		}
        if (!$this->getUser()->checkDroitsSimulationUtilisateur()) {
            throw new AccessDeniedHttpException();
        }
        $listeEnseignes = null;
        /** @var WebserviceResponse $listeEnseignes */
        try {
            $listeEnseignes = $this->webservice->listerEnseignes()->getSingleValeurs();
            if (!$listeEnseignes) {
				$this->webserviceIdentification->logout();
                $tokenStorage->setToken(null);
                $this->container->get('session')->invalidate();
                return $this->redirectToRoute('aquitem_requeteur_login', ['_locale' => substr($lang, 0, 2)]);
            }
            $template = '@Requeteur/Simulation/index.html.twig';

            $response = $this->render($template, array(
                'liste' => $listeEnseignes
            ));
            $response->headers->addCacheControlDirective('no-store');
        } catch (\Throwable $th) {
			return $this->redirectToRoute('aquitem_requeteur_login', ['_locale' => substr($lang, 0, 2)]);
        }
        return $response;
    }

	#[Route('/liste-utilisateurs/{idEnseigne}', name: 'aquitem_requeteur_simulation_utilisateur_liste', options: ['expose' => true])]
    public function listeUtilisateursAction(Request $request, $idEnseigne): Response
    {
        $searchKeyWords = $request->request->get('searchKeyWords');
        /** @var WebserviceResponse $listeDossiers */
        $listeUtilisateurs = $this->webservice->listerUtilisateurs(array('idEnseigne' => $idEnseigne, 'texteRecherche' => $searchKeyWords));
        return $this->render('@Requeteur/Simulation/utilisateurs_liste.html.twig', array(
            'idEnseigne' => $idEnseigne,
            'listeUtilisateurs' => $listeUtilisateurs->getSingleValeurs(),
            'searchKeyWords' => $searchKeyWords,
        ));
    }

	#[Route('/connexion/{id}', name: 'aquitem_requeteur_simulation_utilisateur_connexion', options: ['expose' => true])]
    public function simulationAction(Request $request, DashboardBridge $dashboardBridge, $id): RedirectResponse
    {
        $session = $request->getSession();

        /** @var WebserviceResponse $simulerConnexion */
        $simulerConnexion = $this->webservice->simulerConnexion(array('idUtilisateur' => $id))->getValeurs()->toArray();

        if (empty($simulerConnexion)) {
            if (!empty(json_decode($session->get('real_user'))) && $this->getUser() instanceof \Symfony\Component\Security\Core\User\UserInterface) {
                return $this->redirectToRoute('aquitem_requeteur_index');
            } else {
				return $this->redirectToRoute('aquitem_requeteur_login', ['_locale' => substr($request->server->get('HTTP_ACCEPT_LANGUAGE'), 0, 2)]);
			}
        } else {
            $userAdmin = $this->getUser();
            $serializedUser = $simulerConnexion[0]['valeur'];
            foreach ($simulerConnexion as $key => $data) {
                if ($key > 0 && (isset($data['nom']) && (isset($data['valeur']) || isset($data['valeurs'])))) {
                    $serializedUser[$data['nom']] = isset($data['valeur']) ? $data['valeur'] : $data['valeurs'];
                }
            }
            $user = User::fromArray($serializedUser);
            $profil = $simulerConnexion[6]['valeurs'];
            $infosRQ = null;
			$user->setPassword($dashboardBridge->decryptPassword($user->getPassword()));
            if (is_array($profil) && $profil !== []) {
                $idParams = $keysInfosRQ = $valuesInfosRQ = [];
                foreach ($profil as $values) {
                    foreach ($values as $key => $value) {
                        if ($key === 'id') {
                            $idParams[] = $value;
                        }
                        if ($key === 'libelle') {
                            $keysInfosRQ[] = $value;
                        }
                        if ($key === 'valeur') {
                            $valuesInfosRQ[] = $value;
                        }
                    }
                }
                $infosRQ = array_combine($keysInfosRQ, $valuesInfosRQ);
                /* on récupère l'idParametre pour l'affichage du logo*/
                $infosRQWithIdParameters = array_combine($keysInfosRQ, $idParams);
                if (array_key_exists('logo', $infosRQWithIdParameters)) {
                    $infosRQ['logo'] = $infosRQWithIdParameters['logo'];
                }
                /* on récupère l'idParametre pour le fichier des fonts stripo*/
                if (array_key_exists('stripo_fonts', $infosRQWithIdParameters)) {
                    $infosRQ['stripo_fonts'] = $infosRQWithIdParameters['stripo_fonts'];
                }
                $session->set(WebserviceUserProvider::USER_PROFIL_KEY, $infosRQ);
            }
            foreach ($simulerConnexion as $config) {
                switch ($config['nom']) {
                    case 'afficherMenuSouscriptionOperationNationale':
                        $session->set(WebserviceUserProvider::USER_MENU_KEY, $config['valeur']);
                        break;
                    case 'typeDePointsBonus':
                        $session->set(WebserviceUserProvider::TYPE_BONUS, $config['valeur']);
                        break;
                    case 'niveauUser':
                        $session->set(WebserviceUserProvider::NIVEAU_USER, $config['valeur']);
                        break;
                    case 'routeurSmsPrincipal':
                        $session->set(WebserviceUserProvider::ROUTEUR_SMS, $config['valeur']);
                        break;
                    case 'dateCourante':
                        $session->set(WebserviceUserProvider::DATE_COURANTE, $config['valeur']);
                        $localTime = strtotime($config['valeur']);
                        $serverTime = strtotime(date("Y-m-d H:i:s"));
                        $diff = $localTime - $serverTime;
                        $timeZone = $diff / (60 * 60);
                        $session->set(WebserviceUserProvider::TIME_ZONE, $timeZone);
                        break;
                    case 'tailleMaxDocument':
                        $session->set(WebserviceUserProvider::TAILLE_MAX_DOC, $config['valeur']);
                        break;
                    case 'enseigneTracking':
                        $session->set(WebserviceUserProvider::ENSEIGNE_TRACKING, $config['valeur']);
                        break;
                    case 'typeCampagneObligatoire':
                        $session->set(WebserviceUserProvider::TYPE_CAMPAGNE_OBLIGATOIRE, $config['valeur']);
                        break;
                    case 'accesModificationTypeMailing':
                        $session->set(WebserviceUserProvider::ACCESS_MODIFICATION_TYPE_MAILING, $config['valeur']);
                        break;
                    case 'widgetsAccueil':
                        $session->set(WebserviceUserProvider::RACCOURCIS, $config['valeurs']);
                        break;
                    case 'lesContactsEnseigne':
                        $session->set(WebserviceUserProvider::CONTACTS, $config['valeurs']);
                        break;
                    case 'accesPersonnalisationWalletsOK':
                        $session->set(WebserviceUserProvider::ACCES_PERSO_WALLET, $config['valeur']);
                        break;
                    case 'idFormatRoutageNotificationAutonome':
                        $session->set(WebserviceUserProvider::ID_FORMAT_ROUTAGE_NOTIFICATION_AUTONOME, $config['valeur']);
                        break;
					case 'typeDePointDeVentes':
						$session->set(WebserviceUserProvider::PDV_TYPE_POINT_DE_VENTE, $config['valeur']);
						break;
					case 'typeDeNumeroSirenFiness':
						$session->set(WebserviceUserProvider::PDV_TYPE_NUM_SIREN_FINESS, $config['valeur']);
						break;
					case 'valeurNouveauSiteMagasinAutomatique':
						$session->set(WebserviceUserProvider::PDV_VALEUR_SITE_MAGASIN_AUTO, $config['valeur']);
						break;
					case 'saisieCodeMagasinManuelle':
						$session->set(WebserviceUserProvider::PDV_SAISIE_CODE_MAG_MANUELLE, $config['valeur']);
						break;
					case 'ajoutSiteAutorise':
						$session->set(WebserviceUserProvider::PDV_AUTORISE_AJOUT_SITE, $config['valeur']);
						break;
					case 'idPointDeVente':
						$session->set(WebserviceUserProvider::PDV_ID_PDV, $config['valeur']);
						break;
                }
            }

            if ($user->checkDroitsExclusions()) {
                /** @var WebserviceResponse $pointsDeVente */
                $pointsDeVente = $this->webserviceExclusion->listerPointsDeVenteExclusion()->getValeurs()[0]['valeurs'];
                if (count($pointsDeVente) == 1) {
                    $session->set(WebserviceUserProvider::CAN_ACCESS_EXCLUSIONS, true);
                } else {
                    $session->set(WebserviceUserProvider::CAN_ACCESS_EXCLUSIONS, false);
                }
            }

            //contacts enseigne
            $contacts = $this->webserviceContactsEnseigne->listerContacts(['niveauUser' => $session->get('niveauUser')])->getValeurs()[0]['valeurs'];
            $session->set(WebserviceUserProvider::CONTACTS, $contacts);

            if ($session->get(WebserviceUserProvider::ACCES_PERSO_WALLET)) {
                $session->set(WebserviceUserProvider::HAS_CARTE_WALLET_EN_COURS, false);
                /** @var WebserviceResponse $carteWalletEnCours */
                try {
                    $carteWalletEnCours = $this->webserviceCarteWallet->afficherPersonnalisation()->getSingleValeur();
                    if ($carteWalletEnCours['dateDebut'] != "") {
                        $session->set(WebserviceUserProvider::HAS_CARTE_WALLET_EN_COURS, true);
                    }
                } catch (\Throwable $th) {
                    //throw $th;
                }
            }

            // notifications
            $session->set('nbNotifications', 0);
            $notifications = $this->notificationRepository->findByDate(isset($infosRQ['langue']) ? strtolower($infosRQ['langue']): 'fr', $user->getIdRequeteur());
            if(count($notifications)) {
                $session->set('nbNotifications', count($notifications));
            }
            else {
                $session->set('nbNotifications', 0);
            }

            $session->set(WebserviceUserProvider::USER_KEY, $user->serialize());
            $session->set('real_user', $userAdmin->serialize());
            $session->save();
        }

        return $this->redirectToRoute('aquitem_requeteur_index');
    }
}
