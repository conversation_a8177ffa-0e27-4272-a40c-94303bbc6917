<?php

namespace App\Controller\RequeteurBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;
use App\Services\WebserviceBundle\Response\ModuleBRIResponseManager;
use App\Services\WebserviceBundle\Response\ModuleExclusionsResponseManager;
use App\Services\WebserviceBundle\Response\ModulePromotionsResponseManager;
use App\Services\WebserviceBundle\Response\SelectionsResponseManager;
use App\Services\WebserviceBundle\WebservicePromotion;
use App\Services\WebserviceBundle\WebserviceExclusion;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use App\Services\WebserviceBundle\WebserviceBri;
use App\Services\WebserviceBundle\WebserviceSelection;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;

#[Route("/regle")]
class RegleController extends AbstractRequeteurController
{
    const NB_PRODUIT_PAR_PAGE = 100;

    /**
     * @var WebservicePromotion
     */
    protected $webservicePromotion;

    /**
     * @var WebserviceBri
     */
    protected $webserviceBri;

    /**
     * @var WebserviceExclusion
     */
    protected $webserviceExclusion;

    /**
     * @var WebserviceSelection
     */
    protected $webserviceSelection;

    public function __construct(WebservicePromotion $webservicePromotion, WebserviceBri $webserviceBri, WebserviceExclusion $webserviceExclusion, WebserviceSelection $webserviceSelection)
    {
        $this->webservicePromotion = $webservicePromotion;
        $this->webserviceBri = $webserviceBri;
        $this->webserviceExclusion = $webserviceExclusion;
        $this->webserviceSelection = $webserviceSelection;
    }

	#[Route('/choix-regle', name: 'aquitem_requeteur_regle_choix_regle', options: ['expose' => true])]
    public function choixRegleAction(Request $request): Response
    {
        $session = $request->getSession();
        $promoSession = $session->get('regle-avantage-fid');
        $erreurSessionEnCours = false;
        if ($promoSession) {
            end($promoSession);
            $lastKey = key($session->get('regle-avantage-fid')) + 1;
            $idSessionPromo = $lastKey;
            // session déjà en cours ?
            for ($i = 1; $i <= max(array_keys($promoSession)); $i++) {
                if (isset($promoSession[$i]['creation'])) {
                    $erreurSessionEnCours = true;
                }
            }
        } else {
            $idSessionPromo = 1;
        }
        return $this->render('@Requeteur/Regle/choix_regle.html.twig', array(
            'idSessionPromo' => $idSessionPromo,
            'idSessionExclusion' => "",
            'erreurSessionEnCours' => $erreurSessionEnCours,
        ));
    }

	#[Route('/liste/{type}/{idSession}/{errorSession}', name: 'aquitem_requeteur_regle_liste', defaults: ['type' => 'avantage-fid', 'idSession' => '1', 'errorSession' => 0], options: ['expose' => true])]
    public function listeAction(Request $request, $type, $idSession, $errorSession): Response
    {
        $nb = 20;
        $searchCriteres = array(
            'nbMaxResultats' => $nb,
            'premierResultat' => 1,
            'statut' => array('ENCOURS', 'AVENIR'),
            'searching' => "",
        );
        $resultsRegles = array();
        $nbRegles = 0;
        $newPos = $page = 1;
        $colonneTri = $typeTri = "";
        $submittedSearch = false;
        if ($request->isMethod('post')) {
            $submittedSearch = true;
            foreach ($request->request->all() as $key => $data) {
                if ($key == "titre") {
                    $searchCriteres['descriptif'] = $data;
                }
            }
            $searchCriteres = array_merge($searchCriteres, $request->request->all());
            $colonneTri = isset($searchCriteres['colonneDeTri']) ? $searchCriteres['colonneDeTri'] : "";
            $typeTri = isset($searchCriteres['typeDeTri']) ? $searchCriteres['typeDeTri'] : "";
            $page = isset($searchCriteres['page']) ? $searchCriteres['page'] : 1;
            $newPos = ($page * $nb) + 1;
        }
        if ($type == "avantage-fid") {
            /* PROMOTIONS (AVANTAGES FID') */
            $recherche = $this->webservicePromotion->rechercherPromotions($searchCriteres);
        } elseif ($type == "bri") {
            /* EXCLUSIONS */
            $recherche = $this->webserviceBri->rechercherBRI($searchCriteres);
        } elseif ($type == "exclusion") {
            /* EXCLUSIONS */
            $recherche = $this->webserviceExclusion->rechercherExclusions($searchCriteres);
        }

        $erreurSessionEnCours = false;

        if ($recherche->getValeurs()[0] != null) {
            $resultats = $recherche->getValeurs()[0]['collectionPromotion'];
            $nbRegles = $resultats['NBPROMOTIONS'];
            if ($nbRegles > 0) {
                if (array_key_exists('IDOPERATION', $resultats['promotion'])) {
                    $resultsRegles['promotion'][0] = $resultats['promotion'];
                } else {
                    $resultsRegles['promotion'] = $resultats['promotion'];
                }
                foreach ($resultsRegles['promotion'] as $key => $promotion) {
                    $dateDebut = explode(' ', $promotion['collectionPeriode']['periode']['DATEDEBUT']);
                    $resultsRegles['promotion'][$key]['DATEDEBUT'] = $dateDebut[0];
                    $dateFin = explode(' ', $promotion['collectionPeriode']['periode']['DATEFIN']);
                    $resultsRegles['promotion'][$key]['DATEFIN'] = $dateFin[0];
                }
            }
            /* mise en session des critères de recherche et de tri */
            $session = $request->getSession();
            $sessionRegles = $session->get('regle-' . $type);
            if (!$sessionRegles) {
                $idSession = 1;
            } else {
                end($sessionRegles);
                $lastKey = key($sessionRegles) + 1;
                $idSession = $lastKey;
            }
            $sessionRegles[$idSession]['searchDatas'] = $searchCriteres;
            $session->set('regle-' . $type, $sessionRegles);

            if ($type == "avantage-fid") {
                // session déjà en cours
                $regleSession = $session->get('regle-' . $type);
                if ($regleSession) {
                    for ($i = 1; $i <= max(array_keys($regleSession)); $i++) {
                        if (isset($regleSession[$i]['creation'])) {
                            $erreurSessionEnCours = true;
                        }
                    }
                }
            }
        }

        $template = '@Requeteur/Regle/liste.html.twig';
        // Si requête ajax
        if ($request->isXmlHttpRequest()) {
            $template = '@Requeteur/Regle/Includes/searchResultDatas.html.twig';
        }

        return $this->render($template, array(
            'resultsRegles' => $resultsRegles,
            'type' => $type,
            'idSession' => $idSession,
            'nbParPage' => $nb,
            'newPos' => $newPos,
            'page' => $page ? $page : 1,
            'nbTotal' => $nbRegles,
            'colonneTri' => $colonneTri,
            'typeTri' => $typeTri,
            'searchCriteres' => $searchCriteres,
            'submittedSearch' => $submittedSearch,
            'erreurSessionEnCours' => $erreurSessionEnCours,
            'errorSession' => $errorSession,
        ));
    }

	#[Route('/duplication/{type}/{idRegle}', name: 'aquitem_requeteur_regle_duplication', defaults: ['type' => 'avantage-fid'], options: ['expose' => true])]
    public function duplicationAction(Request $request, $type, $idRegle): RedirectResponse
    {
        $session = $request->getSession();
        if ($idRegle === 0) {
            return $this->redirectToRoute('aquitem_requeteur_regle_liste', array('type' => $type));
        } else {
            if ($type == "avantage-fid") {
                $promo = $this->webservicePromotion->chargerPromotion(array('idPromotion' => $idRegle));

                /** @var ModulePromotionsResponseManager $promoResponseManager */
                $promoResponseManager = $promo->responseManager;

                $formatedDatas = $promoResponseManager->setFormatedPromotionDatas();

                // recup sélection de contacts
                $selectionDatas = $this->webservicePromotion->afficherSelection(array('idPromotion' => $idRegle));

                $formatedDatas['typeSelection'] = $selectionDatas->getValeurs()[0]['nom'] == "typeSelection" ? $selectionDatas->getValeurs()[0]['valeur'] : $selectionDatas->getValeurs()[1]['valeur'];

                $selectionValeur = $selectionDatas->getValeurs();

                if ($selectionValeur[0]['nom'] == "typeSelection") {
                    $selectionValeurs = $selectionValeur[1]['valeur'];
                } else {
                    $selectionValeurs = $selectionValeur[0]['valeur'];
                }

                $formatedDatas['nbContacts'] = $selectionValeurs['tailleCalcul'];
                $formatedDatas['idSelection'] = $selectionValeurs['id'];
                $formatedDatas['nomSelection'] = $selectionValeurs['libelle'];
                if ($formatedDatas['typeSelection'] == "1") {
                    $formatedDatas['nbContacts'] = $this->webservicePromotion->compterSelectionComplete(array(
                        'sites' => $formatedDatas['sites'],
                        'magasins' => $formatedDatas['magasins']
                    ))->getSingleValeur()['resultatCalcul'];
                }
                $criteriumList = $this->webserviceSelection->listerCriteres(array(
                    'filtrePourPromo' => 'true'
                ));
                $js_vars = \json_encode(array(
                    "selection" => $selectionValeurs,
                    "edition" => true,
                    "readOnly" => true,
                    "criteriumList" => $criteriumList->responseManager->getCriteriumListFromCriteres(),
                    "operatorsList" => $criteriumList->responseManager->getOperatorsListFromCriteres(),
                    "aggregationsList" => $criteriumList->responseManager->getAggregationsListFromCriteres(),
                ));
                $formatedDatas['tokenSelection'] = "";

                // libelles niveaux
                if (count($formatedDatas['idsNiveaux']) > 0) {
                    // libelles niveaux
                    $niveauxDatas = $this->webservicePromotion->convertirValeursNiveauxPromotion(array(
                        'idsNiveaux' => $formatedDatas['idsNiveaux'],
                        'valeursNiveaux' => $formatedDatas['valeursNiveaux'],
                    ))->getSingleValeurs();
                    foreach ($niveauxDatas as $niveau) {
                        $formatedDatas['typeNiveaux'][] = $niveau['libelle'];
                        $formatedDatas['libNiveaux'][] = implode('|', array_column($niveau['valeurs'], 'libelle'));
                    }
                    $formatedDatas['nbProduits'] = $this->webservicePromotion->compterProduitsSelectionnesPromotion(array(
                        'idsNiveaux' => $formatedDatas['idsNiveaux'],
                        'valeursNiveaux' => $formatedDatas['valeursNiveaux'],
                    ))->getSingleValeur();
                    $formatedDatas['typeChoixProduits'] = 2;
                } elseif ($formatedDatas['codesArticles'] == "") {
                    $formatedDatas['nbProduits'] = $this->webservicePromotion->compterTousLesProduitsPromotion()->getSingleValeur();
                    $formatedDatas['typeChoixProduits'] = 1;
                } else {
                    $formatedDatas['typeChoixProduits'] = 3;
                }

                $formatedDatas['idRegle'] = $formatedDatas['idPromotion'];
                $avantage = $this->webservicePromotion->convertirAvantageXML(array('gain' => $formatedDatas['gain'], 'coef' => $formatedDatas['coef']));
                $formatedDatas['gain'] = $avantage->getValeurs()[0]['valeur'];
                $formatedDatas['coef'] = $avantage->getValeurs()[1]['valeur'];
            } elseif ($type == "bri") {
                $promo = $this->webserviceBri->chargerBRI(array('idBRI' => $idRegle));

                /** @var ModuleBRIResponseManager $promoResponseManager */
                $promoResponseManager = $promo->responseManager;

                $formatedDatas = $promoResponseManager->setFormatedBRIDatas();

                $formatedDatas['idRegle'] = $formatedDatas['idBRI'];
                $formatedDatas['typeChoixProduits'] = 3;
                $avantage = $this->webserviceBri->convertirAvantageXML(array('gain' => $formatedDatas['gain'], 'coef' => $formatedDatas['coef']));
                $formatedDatas['gain'] = $avantage->getValeurs()[0]['valeur'];
                $formatedDatas['coef'] = $avantage->getValeurs()[1]['valeur'];
            } elseif ($type == "exclusion") {
                $exclusion = $this->webserviceExclusion->chargerExclusion(array('idExclusion' => $idRegle));

                /** @var ModuleExclusionsResponseManager $exclusionResponseManager */
                $exclusionResponseManager = $exclusion->responseManager;

                $formatedDatas = $exclusionResponseManager->setFormatedExclusionDatas();
                $formatedDatas['idRegle'] = $formatedDatas['idExclusion'];
                $avantage = $this->webservicePromotion->convertirAvantageXML(array('gain' => $formatedDatas['gain'], 'coef' => $formatedDatas['coef']));
                $formatedDatas['gain'] = $avantage->getValeurs()[0]['valeur'];
                $formatedDatas['coef'] = $avantage->getValeurs()[1]['valeur'];
            }
            // on vide titre, date debut et date fin
            unset($formatedDatas['titre']);
            unset($formatedDatas['dateDebut']);
            unset($formatedDatas['dateFin']);
            unset($formatedDatas['descriptif']);

            //mise en session des données de la regle
            $session = $request->getSession();
            $regleSession = $session->get('regle-' . $type);
            if ($regleSession) {
                end($regleSession);
                $lastKey = key($regleSession) + 1;
                $idSession = $lastKey;
            } else {
                $idSession = 1;
            }
            $regleSession[$idSession] = $formatedDatas;
            $regleSession[$idSession]['idSession'] = $idSession;
            $regleSession[$idSession]['valideSelection'] = true;
            $session->set('regle-' . $type, $regleSession);
        }
        return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession));
    }

	#[Route('/edition/{type}/{idSession}/{idRegle}', name: 'aquitem_requeteur_regle_edition', defaults: ['type' => 'avantage-fid', 'idSession' => ''], options: ['expose' => true])]
    public function editionAction(Request $request, $type, $idRegle, $idSession): Response|RedirectResponse
    {
        $session = $request->getSession();
        $regleSearchSession = [];
        $formatedDatas = [];
        $regleSession = $session->get('regle-' . $type);
        $js_vars = \json_encode(array());

        $erreurSessionEnCours = false;
        // session déjà en cours
        if ($type == "avantage-fid" && $regleSession) {
            for ($i = 1; $i <= max(array_keys($regleSession)); $i++) {
                if (isset($regleSession[$i]['niveauUser'])) {
                    $erreurSessionEnCours = true;
                }
            }
        }

        /** @var WebserviceResponse $pointsDeVente */
        if ($type == "bri") {
            $pointsDeVente = $this->webserviceBri->listerPointsDeVenteBRI()->getValeurs()[0]['valeurs'];
        } else {
            $pointsDeVente = $this->webservicePromotion->listerPointsDeVentePromotion()->getValeurs()[0]['valeurs'];
        }
        if ($idRegle === 0) {
            return $this->redirectToRoute('aquitem_requeteur_regle_liste', array('type' => $type));
        } else {
            if ($type == "avantage-fid") {
                $promo = $this->webservicePromotion->chargerPromotion(array('idPromotion' => $idRegle));

                /** @var ModulePromotionsResponseManager $promoResponseManager */
                $promoResponseManager = $promo->responseManager;

                $formatedDatas = $promoResponseManager->setFormatedPromotionDatas();
                $formatedDatas['edition'] = true;

                // recup sélection de contacts
                $selectionDatas = $this->webservicePromotion->afficherSelection(array('idPromotion' => $idRegle));
                $selectionValeurs = null;

                if ($selectionDatas) {
                    $formatedDatas['typeSelection'] = $selectionDatas->getValeurs()[0]['nom'] == "typeSelection" ? $selectionDatas->getValeurs()[0]['valeur'] : $selectionDatas->getValeurs()[1]['valeur'];
                    $selectionValeurs = $selectionDatas->getValeurs()[0]['nom'] == "typeSelection" ? $selectionDatas->getValeurs()[1]['valeur'] : $selectionDatas->getValeurs()[0]['valeur'];

                    $formatedDatas['nbContacts'] = $selectionValeurs['tailleCalcul'];
                    $formatedDatas['idSelection'] = $selectionValeurs['id'];
                    $formatedDatas['nomSelection'] = $selectionValeurs['libelle'];
                    if ($formatedDatas['typeSelection'] == "1") {
                        $formatedDatas['nbContacts'] = $this->webservicePromotion->compterSelectionComplete(array(
                            'sites' => $formatedDatas['sites'],
                            'magasins' => $formatedDatas['magasins']
                        ))->getSingleValeur()['resultatCalcul'];
                    }
                }

                $criteriumList = $this->webserviceSelection->listerCriteres(array(
                    'filtrePourPromo' => 'true'
                ));
                $js_vars = \json_encode(array(
                    "selection" => $selectionValeurs,
                    "edition" => true,
                    "readOnly" => true,
                    "criteriumList" => $criteriumList->responseManager->getCriteriumListFromCriteres(),
                    "operatorsList" => $criteriumList->responseManager->getOperatorsListFromCriteres(),
                    "aggregationsList" => $criteriumList->responseManager->getAggregationsListFromCriteres(),
                ));
                $formatedDatas['tokenSelection'] = "";

                // recup createurs promos en conflit
                if (array_key_exists('conflits', $formatedDatas) && array_key_exists('promotionsBloquees', $formatedDatas['conflits'])) {
                    foreach ($formatedDatas['conflits']['promotionsBloquees'] as $key => $promoBloquee) {
                        $loginRegle = $this->webservicePromotion->afficherLibelleLoginPromotion(array('login' => $promoBloquee['LOGIN']));
                        $formatedDatas['conflits']['promotionsBloquees'][$key]['createur'] = $loginRegle->getSingleValeur();
                    }
                }

                if (count($formatedDatas['idsNiveaux']) > 0) {
                    // libelles niveaux
                    $niveauxDatas = $this->webservicePromotion->convertirValeursNiveauxPromotion(array(
                        'idsNiveaux' => $formatedDatas['idsNiveaux'],
                        'valeursNiveaux' => $formatedDatas['valeursNiveaux'],
                    ))->getSingleValeurs();
                    foreach ($niveauxDatas as $niveau) {
                        $formatedDatas['typeNiveaux'][] = $niveau['libelle'];
                        $formatedDatas['libNiveaux'][] = implode('|', array_column($niveau['valeurs'], 'libelle'));
                    }
                    $formatedDatas['nbProduits'] = $this->webservicePromotion->compterProduitsSelectionnesPromotion(array(
                        'idsNiveaux' => $formatedDatas['idsNiveaux'],
                        'valeursNiveaux' => $formatedDatas['valeursNiveaux'],
                    ))->getSingleValeur();
                    $formatedDatas['typeChoixProduits'] = 2;
                } elseif ($formatedDatas['codesArticles'] === "") {
					$formatedDatas['nbProduits'] = $formatedDatas['nbProduitsReels'];
                    $formatedDatas['typeChoixProduits'] = 1;
                } else {
                    $formatedDatas['typeChoixProduits'] = 3;
                }
                $formatedDatas['idRegle'] = $formatedDatas['idPromotion'];
                $avantage = $this->webservicePromotion->convertirAvantageXML(array('gain' => $formatedDatas['gain'], 'coef' => $formatedDatas['coef']));
            } elseif ($type == "exclusion") {
                $exclusion = $this->webserviceExclusion->chargerExclusion(array('idExclusion' => $idRegle));

                /** @var ModuleExclusionsResponseManager $exclusionResponseManager */
                $exclusionResponseManager = $exclusion->responseManager;

                $formatedDatas = $exclusionResponseManager->setFormatedExclusionDatas();
                // recup createurs promos en conflit
                if (array_key_exists('conflits', $formatedDatas) && array_key_exists('promotionsBloquees', $formatedDatas['conflits'])) {
                    foreach ($formatedDatas['conflits']['promotionsBloquees'] as $key => $promoBloquee) {
                        $loginRegle = $this->webservicePromotion->afficherLibelleLoginPromotion(array('login' => $promoBloquee['LOGIN']));
                        $formatedDatas['conflits']['promotionsBloquees'][$key]['createur'] = $loginRegle->getSingleValeur();
                    }
                }
                // libelles niveaux
                if (count($formatedDatas['idsNiveaux']) > 0) {
                    $niveauxDatas = $this->webserviceExclusion->convertirValeursNiveauxExclusion(array(
                        'idsNiveaux' => $formatedDatas['idsNiveaux'],
                        'valeursNiveaux' => $formatedDatas['valeursNiveaux'],
                    ))->getSingleValeurs();
                    foreach ($niveauxDatas as $niveau) {
                        $formatedDatas['typeNiveaux'][] = $niveau['libelle'];
                        $formatedDatas['libNiveaux'][] = implode('|', array_column($niveau['valeurs'], 'libelle'));
                    }
                    $formatedDatas['nbProduits'] = $this->webserviceExclusion->compterProduitsSelectionnesExclusion(array(
                        'idsNiveaux' => $formatedDatas['idsNiveaux'],
                        'valeursNiveaux' => $formatedDatas['valeursNiveaux'],
                    ))->getSingleValeur();
                    $formatedDatas['typeChoixProduits'] = 2;
                } elseif ($formatedDatas['codesArticles'] == "") {
                    $formatedDatas['nbProduits'] = $this->webserviceExclusion->compterTousLesProduitsExclusion()->getSingleValeur();
                    $formatedDatas['typeChoixProduits'] = 1;
                } else {
                    $formatedDatas['typeChoixProduits'] = 3;
                }
                $formatedDatas['idRegle'] = $formatedDatas['idExclusion'];
                $avantage = $this->webservicePromotion->convertirAvantageXML(array('gain' => $formatedDatas['gain'], 'coef' => $formatedDatas['coef']));
            } elseif ($type == "bri") {
                $bri = $this->webserviceBri->chargerBRI(array('idBRI' => $idRegle));

                /** @var ModuleBRIResponseManager $briResponseManager */
                $briResponseManager = $bri->responseManager;

                $formatedDatas = $briResponseManager->setFormatedBRIDatas();

                // recup createurs promos en conflit
                if (array_key_exists('conflits', $formatedDatas) && array_key_exists('promotionsBloquees', $formatedDatas['conflits'])) {
                    foreach ($formatedDatas['conflits']['promotionsBloquees'] as $key => $promoBloquee) {
                        $loginRegle = $this->webserviceBri->afficherLibelleLoginBRI(array('login' => $promoBloquee['LOGIN']));
                        $formatedDatas['conflits']['promotionsBloquees'][$key]['createur'] = $loginRegle->getSingleValeur();
                    }
                }
                $formatedDatas['typeChoixProduits'] = 3;
                $formatedDatas['idRegle'] = $formatedDatas['idBRI'];
                $avantage = $this->webserviceBri->convertirAvantageXML(array('gain' => $formatedDatas['gain'], 'coef' => $formatedDatas['coef']));
            }
            $formatedDatas['gain'] = $avantage->getValeurs()[0]['valeur'];
            $formatedDatas['coef'] = $avantage->getValeurs()[1]['valeur'];

            if ($regleSession) {
                if (array_key_exists($idSession, $regleSession) && array_key_exists('searchDatas', $regleSession[$idSession])) {
                    $regleSearchSession = $regleSession[$idSession]['searchDatas'];
                }
                end($regleSession);
                $lastKey = key($regleSession) + 1;
                $idSession = $lastKey;
            } else {
                $idSession = 1;
            }
            $regleSession[$idSession] = $formatedDatas;
            $regleSession[$idSession]['searchDatas'] = $regleSearchSession;
            $regleSession[$idSession]['idSession'] = $idSession;
            $regleSession[$idSession]['valideSelection'] = true;
            $session->set('regle-' . $type, $regleSession);
        }

        return $this->render("@Requeteur/Regle/edition.html.twig", array(
            'idRegle' => $formatedDatas['idRegle'],
            'type' => $type,
            'etapes' => $this->getEtapes($type),
            'regleSession' => $regleSession,
            'idSession' => $idSession,
            'pointsDeVente' => $pointsDeVente,
            'js_vars' => $js_vars,
            'erreurSessionEnCours' => $erreurSessionEnCours,
        ));
    }

	#[Route('/produits/{type}/{page}/{idNiveau}', name: 'aquitem_requeteur_regle_produits', defaults: ['type' => 'avantage-fid', 'page' => '1'], options: ['expose' => true])]
    public function produitsAction($type, $page, $idNiveau): Response
    {
        if ($type == 'avantage-fid') {
            /** @var WebserviceResponse $criteres */
            $criteres = $this->webservicePromotion->listerCriteresRechercheProduitsPromotion()->getSingleValeurs();
        } elseif ($type == 'exclusion') {
            /** @var WebserviceResponse $criteres */
            $criteres = $this->webserviceExclusion->listerCriteresRechercheProduitsExclusion()->getSingleValeurs();
        }
        $nbPerPage = self::NB_PRODUIT_PAR_PAGE;
        $counter = count($criteres);
        for ($i = 0; $i < $counter; $i++) {
            if ($criteres[$i]['id'] == $idNiveau) {
                foreach ($criteres[$i]['valeurs'] as $key => $critere) {
                    $criteres[$i]['valeurs'][$key] = str_replace('/', ' / ', $critere);
                }
                $produits = array(
                    'id' => $idNiveau,
                    'libelle' => $criteres[$i]['libelle'],
                    'valeurs' => array_filter($criteres[$i]['valeurs'], function ($key) use ($page, $nbPerPage) {
                        return $key >= ((($page * $nbPerPage) - $nbPerPage)) && $key < ($page * $nbPerPage);
                    }, ARRAY_FILTER_USE_KEY),
                );
            }
        }
        return $this->render("@Requeteur/Regle/Partials/Promotion/produits-niveaux.html.twig", array(
            'page' => $page,
            'nbPerPage' => $nbPerPage,
            'produits' => $produits,
        ));
    }

	#[Route('/recherche-produit/{type}/{init}', name: 'aquitem_requeteur_regle_recherche_produit', defaults: ['type' => 'avantage-fid', 'init' => '0'], options: ['expose' => true])]
    public function rechercheProduitAction(Request $request, $type, $init): bool|Response
    {
        if ($request->isXmlHttpRequest()) {
            if ($request->isMethod('post')) {
                $idNiveau = $request->request->get('idNiveau');
                $text = $request->request->get('text');
                if ($type == "avantage-fid") {
                    /** @var WebserviceResponse $criteres */
                    $criteres = $this->webservicePromotion->listerCriteresRechercheProduitsPromotion()->getSingleValeurs();
                } elseif ($type == "exclusion") {
                    /** @var WebserviceResponse $criteres */
                    $criteres = $this->webserviceExclusion->listerCriteresRechercheProduitsExclusion()->getSingleValeurs();
                }
                $valeursNiveau = [];
                $counter = count($criteres);
                for ($i = 0; $i < $counter; $i++) {
                    if ($criteres[$i]['id'] == $idNiveau) {
                        $valeursNiveau = $criteres[$i]['valeurs'];
                        foreach ($criteres[$i]['valeurs'] as $key => $critere) {
                            $valeursNiveau[$key] = str_replace('/', ' / ', $critere);
                        }
                    }
                }
                if ($init !== 0) {
                    $foundValues['valeurs'] = array_slice($valeursNiveau, 0, (self::NB_PRODUIT_PAR_PAGE - 1));
                } else {
                    $foundValues['valeurs'] = array_filter($valeursNiveau, function ($element) use ($text) {
                        foreach ($element as $v) {
                            if (stripos($this->skip_accents($v), $text) !== false) {
                                return true;
                            }
                        };
                        return false;
                    });
                }

                return $this->render("@Requeteur/Regle/Partials/Promotion/produits-niveaux.html.twig", array(
                    'produits' => $foundValues,
                ));
            } else {
                throw new AccessDeniedHttpException();
            }
        } else {
            throw new AccessDeniedHttpException();
        }
    }

	#[Route('/reset-session/{type}/{resetAll}/{idSession}/{idRegle}', name: 'aquitem_requeteur_regle_reset_session', defaults: ['type' => 'avantage-fid', 'resetAll' => 0, 'idSession' => 0, 'idRegle' => 0], options: ['expose' => true])]
    public function resetSessionAction(Request $request, $type, $resetAll, $idSession, $idRegle): RedirectResponse
    {
        $session = $request->getSession();
        $regleSession = $session->get('regle-' . $type);
        if ($resetAll !== 0) {
            /* for($i=1;$i<max(array_keys($regleSession));$i++) {
                unset($regleSession[$i]);
            } */
            $lastIdSession = max(array_keys($regleSession));
            $regleSession = [$lastIdSession => []];
            $session->set('regle-' . $type, $regleSession);

            if ($resetAll == 1) {
                return $this->redirectToRoute('aquitem_requeteur_regle_liste', array('type' => $type));
            } else {
                $parameters = array(
                    'type' => $type,
                    'idSession' => $lastIdSession,
                );
                return $this->redirectToRoute('aquitem_requeteur_regle_etapes', $parameters);
            }
        } else {
            if ($idRegle !== 0) {
                if ($regleSession) {
                    for ($i = 1; $i < $idSession; $i++) {
                        unset($regleSession[$i]);
                    }
                    $session->set('regle-' . $type, $regleSession);
                }
                $parameters = array(
                    'idRegle' => $idRegle,
                    'type' => $type,
                    'idSession' => $idSession,
                );
            } elseif ($regleSession) {
                $lastKey = max(array_keys($regleSession)) + 1;
                $regleSession[$lastKey] = [];
                $regleSession[$lastKey]['idSession'] = $lastKey;
                $regleSession[$lastKey]['niveauUser'] = $session->get('niveauUser');
                for ($i = 1; $i < $lastKey; $i++) {
                    unset($regleSession[$i]);
                }
                $session->set('regle-' . $type, $regleSession);
                $parameters = array(
                    'type' => $type,
                    'idSession' => $lastKey
                );
            } else {
                $parameters = array(
                    'type' => $type,
                    'idSession' => 1,
                );
            }

            return $this->redirectToRoute('aquitem_requeteur_regle_etapes', $parameters);
        }
    }

	#[Route('/etapes/{type}/{idSession}/{numEtape}-{etape}/{idRegle}', name: 'aquitem_requeteur_regle_etapes', defaults: ['type' => 'avantage-fid', 'idSession' => '0', 'numEtape' => '1', 'etape' => 'informations', 'idRegle' => ''], options: ['expose' => true])]
    public function etapesAction(Request $request, $type, $idSession, $numEtape, $idRegle): Response|RedirectResponse
    {
        $session = $request->getSession();
        if ($type == "avantage-fid" && !$this->getUser()->checkDroitsSelections()) {
            $template = '@Requeteur/Regle/etapes.html.twig';
            return $this->render($template,  array('type' => $type, 'idRegle' => $idRegle));
        }
        $lastKey = 1;
        $regleDatas = [];
        $markers = [];
        $resultsCreaRegle = array('etat' => '', 'message' => '');
        $typeChoixProduits = 0;
        $regleSession = $session->get('regle-' . $type);
        $erreurSelection = "";
        $currentSelection = null;
        $js_vars = \json_encode(array());

        /* setting session */
        if ($regleSession) {
            if (!isset($regleSession[$idSession])) {
                if ($type == "avantage-fid" && $idSession < max(array_keys($regleSession))) {
                    if ($idSession == 0) {
                        // idSession absent : redirection vers liste avec message "session en cours"
                        return $this->redirectToRoute('aquitem_requeteur_regle_liste', array('type' => $type, 'idSession' => $idSession, 'errorSession' => 2));
                    } else {
                        // idSession n'existe plus : redirection vers liste avec message "session expirée"
                        return $this->redirectToRoute('aquitem_requeteur_regle_liste', array('type' => $type, 'idSession' => $idSession, 'errorSession' => 1));
                    }
                }
                if ($numEtape > 1) {
                    return $this->redirectToRoute('aquitem_requeteur_regle_liste', array('type' => $type, 'idSession' => $idSession));
                } else {
                    end($regleSession);
                    $lastKey = key($regleSession) + 1;
                    $regleSession[$lastKey] = [];
                    $idSession = $lastKey;
                }
            }
        } elseif ($numEtape > 1) {
            return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type));
        } else {
            $idSession = 1;
            $regleSession[$idSession] = [];
            $regleSession[$idSession]['created'] = time();
        }

        $regleSession[$idSession]['creation'] = true;
        if ($idRegle != "") {
            unset($regleSession[$idSession]['edition']);
            if (!isset($regleSession[$idSession]['idPromotion']) && !isset($regleSession[$idSession]['idExclusion']) && !isset($regleSession[$idSession]['idBRI'])) {
                return $this->redirectToRoute('aquitem_requeteur_regle_edition', array('type' => $type, 'idSession' => $idSession, 'idRegle' => $idRegle));
            }
        }

        $regleSession[$idSession]['idSession'] = $idSession;
        $regleSession[$idSession]['niveauUser'] = $session->get('niveauUser');
        if ((($type == "avantage-fid" && $numEtape < 4) || ($type == "exclusion" && $numEtape < 2) || ($type == "bri" && $numEtape < 3))
            && array_key_exists('conflits', $regleSession[$idSession])
        ) {
            $regleSession[$idSession]['conflits'] = [];
        }
        /**/

        /* post étape */
        if ($request->isMethod('post')) {
            $allDatas = $request->request->all();

            $actionCreation = false;
            $actionModification = false;
            if (isset($allDatas['creerRegle'])) {
                $actionCreation = true;
            } elseif (isset($allDatas['modifierRegle'])) {
                $actionModification = true;
            }

            // dernière étape : enregistrement création / modification
            if ((($type == "avantage-fid" && $numEtape == 7) || ($type == "exclusion" && $numEtape == 3) || ($type == "bri" && $numEtape == 5)) && ($actionCreation || $actionModification)) {
                if ($type == "avantage-fid") {
                    if ($actionCreation) {
                        $creaRegle = $this->webservicePromotion->ajouterPromotion($regleSession[$idSession]);
                    } elseif ($actionModification) {
                        $creaRegle = $this->webservicePromotion->modifierPromotion($regleSession[$idSession]);
                    }
                } elseif ($type == "bri") {
                    if ($actionCreation) {
                        $creaRegle = $this->webserviceBri->ajouterBRI($regleSession[$idSession]);
                    } elseif ($actionModification) {
                        $creaRegle = $this->webserviceBri->modifierBRI($regleSession[$idSession]);
                    }
                } elseif ($type === "exclusion") {
                    if ($actionCreation) {
                        $creaRegle = $this->webserviceExclusion->ajouterExclusion($regleSession[$idSession]);
                    } elseif ($actionModification) {
                        $creaRegle = $this->webserviceExclusion->modifierExclusion($regleSession[$idSession]);
                    }
                }
                if ($creaRegle->hasError()) {
                    $resultsCreaRegle['etat'] = 'KO';
                    if (isset($creaRegle->getValidations()[0]['message'])) {
                        if ($creaRegle->getValidations()[0]['message'] != "") {
                            $resultsCreaRegle['message'] = $creaRegle->getValidations()[0]['message'];
                        }
                    } elseif (isset($creaRegle->getErreurs()[0]['message'])) {
                        if ($creaRegle->getErreurs()[0]['message'] != "") {
                            $resultsCreaRegle['message'] = $creaRegle->getErreurs()[0]['message'];
                        }
                    }
                } else {
                    $resultsCreaRegle['etat'] = 'OK';
                }
            } else {
                $pointsDeVente = $this->getPointsDeVente($type);
                foreach ($allDatas as $key => $data) {
                    // tous les ids magasins et tous les ids sites pour niveau user = FRANCHISE
                    if (($key == "magasins" || $key == "sites") && $data == "all") {
                        if ($session->get('niveauUser') == "FRANCHISE") {
                            if ($key == "magasins") {
                                $data = implode(',', array_column($pointsDeVente, 'codeMagasin'));
                            }
                            if ($key == "sites") {
                                $data = implode(',', array_column($pointsDeVente, 'codeSite'));
                            }
                        } else {
                            $data = "";
                        }
                    }
                    if ($key == "idsNiveaux" || $key == "valeursNiveaux" || $key == "typeNiveaux" || $key == "libNiveaux") {
                        if ($data == "all" || $data == "") {
                            $data = [];
                        } elseif ($key == "idsNiveaux" || $key == "typeNiveaux") {
                            $data = explode(',', $data);
                        } elseif ($key == "valeursNiveaux" || $key == "libNiveaux") {
                            $data = (array) json_decode($data);
                        }
                    }
                    $regleDatas[$key] = $data;
                }
                $regleSession[$idSession] = array_merge($regleSession[$idSession], $regleDatas);
                /* si après le forcebloquage il n'y a plus de conflit bloque  */
                if (isset($regleSession[$idSession]['supprimerConflitBloque']) && $regleSession[$idSession]['supprimerConflitBloque'] == "1") {
                    unset($regleSession[$idSession]['conflits']['promotionsBloquees']);
                }
            }
        }

        switch ($numEtape) {
            case 1:
                $regleSession[$idSession]['pointsDeVente'] = $this->getPointsDeVente($type);
                if ($type == "avantage-fid" && isset($regleSession[$idSession]['valideSelection']) && !$regleSession[$idSession]['valideSelection']) {
                    // si choix selection de contacts pas validée, on annule le choix
                    unset($regleSession[$idSession]['tokenSelection']);
                }
                if ($type == "avantage-fid" && isset($regleSession[$idSession]['idRegle']) && $regleSession[$idSession]['typeSelection'] == "2") {
                    // recup sélection de contacts
                    $selectionDatas = $this->webservicePromotion->afficherSelection(array('idPromotion' => $regleSession[$idSession]['idRegle']));
                    $selectionValeurs = $selectionDatas->getValeurs()[0]['nom'] == "typeSelection" ? $selectionDatas->getValeurs()[1]['valeur'] : $selectionDatas->getValeurs()[0]['valeur'];

                    $criteriumList = $this->webserviceSelection->listerCriteres(array(
                        'filtrePourPromo' => 'true'
                    ));
                    $js_vars = \json_encode(array(
                        "selection" => $selectionValeurs,
                        "edition" => true,
                        "readOnly" => true,
						"criteriumList" => $criteriumList->responseManager->getCriteriumListFromCriteres(),
						"operatorsList" => $criteriumList->responseManager->getOperatorsListFromCriteres(),
						"aggregationsList" => $criteriumList->responseManager->getAggregationsListFromCriteres(),
                    ));
                }
                break;
            case 2:
                if (($type == "avantage-fid" || $type == "bri") && count($regleSession[$idSession]['pointsDeVente']) == 1) {
                    throw new AccessDeniedHttpException();
                }
                if ($type == "exclusion") {
                    $seuil = $this->getSeuilExclusion($regleSession[$idSession], $idRegle);
                    $markers['seuil'] = $seuil[1]['valeur'];
                    // produits
                    $markers['criteresProduits'] = $this->getCriteresProduits($type);
                    if (isset($regleSession[$idSession]['typeChoixProduits'])) {
                        $typeChoixProduits = $regleSession[$idSession]['typeChoixProduits'];
                    }
                    $markers['typeChoixProduits'] = $typeChoixProduits;
                } elseif ($type == "avantage-fid" && !isset($regleSession[$idSession]['valideSelection'])) {
                    // si choix selection de contacts pas validée, on annule le choix
                    unset($regleSession[$idSession]['nbContacts']);
                    unset($regleSession[$idSession]['idSelection']);
                    unset($regleSession[$idSession]['typeSelection']);
                    unset($regleSession[$idSession]['tokenSelection']);
                }
                break;
            case 3:
                if ($type == "bri") {
                    if (isset($regleSession[$idSession]['typeChoixProduits'])) {
                        $typeChoixProduits = $regleSession[$idSession]['typeChoixProduits'];
                    }
                    $markers['typeChoixProduits'] = $typeChoixProduits;
                }
                if ($type == "avantage-fid") {
                    /* Sélection contacts */
                    // sélections existantes
                    $searchKeywords = $request->request->get('searchKeywords');
                    /** @var WebserviceResponse $listeDossiers */
                    $listeDossiers = $this->webserviceSelection->listerDossiers(array('texteRecherche' => $searchKeywords, 'filtrePourPromo' => 'true'));
                    /** @var ArrayCollection $valeurs */
                    $valeurs = $listeDossiers->getValeurs();
                    $markers['mesSelections'] = $valeurs[0]['valeurs'];
                    $markers['searchKeywords'] = $searchKeywords;

                    $criteriumList = $this->webserviceSelection->listerCriteres(array(
                        'filtrePourPromo' => 'true'
                    ));

                    $js_vars = \json_encode(array(
                        "edition" => true,
                        "readOnly" => true,
						"criteriumList" => $criteriumList->responseManager->getCriteriumListFromCriteres(),
						"operatorsList" => $criteriumList->responseManager->getOperatorsListFromCriteres(),
						"aggregationsList" => $criteriumList->responseManager->getAggregationsListFromCriteres(),
                        "selection" => null,
                    ));

                    // si sélection déjà validée, on revalide en cas de modification des pdv
                    if (
                        count($regleSession[$idSession]['pointsDeVente']) > 1 && isset($regleSession[$idSession]['valideSelection'])
                        && (isset($regleSession[$idSession]['tokenSelection']))
                    ) {
                        switch ($regleSession[$idSession]['typeSelection']) {
                            case 1:
                                $regleSession[$idSession]['nbContacts'] = $this->webservicePromotion->compterSelectionComplete(array(
                                    'sites' => $regleSession[$idSession]['sites'],
                                    'magasins' => $regleSession[$idSession]['magasins']
                                ))->getSingleValeur()['resultatCalcul'];
                                break;
                            case 2:
                                $datas = (array) json_decode($regleSession[$idSession]['criteres']);
                                array_walk_recursive($datas, function (&$val) {
                                    if (is_bool($val)) {
                                        $val = $val ? "true" : "false";
                                    }
                                    return $val;
                                });
                                $datas['sites'] = $regleSession[$idSession]['sites'];
                                $datas['magasins'] = $regleSession[$idSession]['magasins'];
                                $regleSession[$idSession]['nbContacts'] = $this->webservicePromotion->compterSelectionParCriteres($datas)->getSingleValeur()['resultatCalcul'];

                                break;
                            case 3:
                                $regleSession[$idSession]['nbContacts'] = $this->webservicePromotion->compterSelectionEnregistree(
                                    array(
                                        'idSelection' => $regleSession[$idSession]['idSelection'],
                                        'sites' => $regleSession[$idSession]['sites'],
                                        'magasins' => $regleSession[$idSession]['magasins']
                                    )
                                )->getSingleValeur()['resultatCalcul'];
                                break;
                        }
                    }

                    $markers['idSelection'] = isset($regleSession[$idSession]['idSelection']) ? $regleSession[$idSession]['idSelection'] : "";
                    $markers['nomSelection'] = isset($regleSession[$idSession]['nomSelection']) ? $regleSession[$idSession]['nomSelection'] : "";
                    $markers['typeSelection'] = isset($regleSession[$idSession]['typeSelection']) ? (int) $regleSession[$idSession]['typeSelection'] : 0;
                    $markers['nbContacts'] = isset($regleSession[$idSession]['nbContacts']) ? $regleSession[$idSession]['nbContacts'] : 0;
                    /**/
                }
                /**/
                break;
            case 4:
                if ($type == "avantage-fid") {
                    // produits
                    $markers['criteresProduits'] = $this->getCriteresProduits($type);
                    if (isset($regleSession[$idSession]['typeChoixProduits'])) {
                        $typeChoixProduits = $regleSession[$idSession]['typeChoixProduits'];
                    }
                    $markers['typeChoixProduits'] = $typeChoixProduits;

                    // Validation de la sélection
                    // à ce stade tokenSelection est vide
                    if (isset($regleSession[$idSession]['typeSelection'])) {
                        if ($regleSession[$idSession]['typeSelection'] == 1) {
                            $regleSession[$idSession]['tokenSelection'] = $this->webservicePromotion->validerSelectionComplete(array(
                                'sites' => $regleSession[$idSession]['sites'],
                                'magasins' => $regleSession[$idSession]['magasins'],
                            ))->getSingleValeur();

                            $regleSession[$idSession]['critere'] = "";
                        } elseif ($regleSession[$idSession]['typeSelection'] == 2) {
                            $datas = (array) json_decode($regleSession[$idSession]['criteres']);
                            array_walk_recursive($datas, function (&$val) {
                                if (is_bool($val)) {
                                    $val = $val ? "true" : "false";
                                }
                                return $val;
                            });
                            $datas['sites'] = $regleSession[$idSession]['sites'];
                            $datas['magasins'] = $regleSession[$idSession]['magasins'];

                            $regleSession[$idSession]['tokenSelection'] = $this->webservicePromotion->validerSelectionParCriteres($datas)->getSingleValeur();
                        } elseif ($regleSession[$idSession]['typeSelection'] == 3) {
                            $regleSession[$idSession]['tokenSelection'] = $this->webservicePromotion->validerSelectionEnregistree(array(
                                'sites' => $regleSession[$idSession]['sites'],
                                'magasins' => $regleSession[$idSession]['magasins'],
                                'idSelection' => $regleSession[$idSession]['idSelection'],
                            ))->getSingleValeur();
                            $regleSession[$idSession]['critere'] = "";
                        }
                        //destruction des autres sessions en cours
                        for ($i = 1; $i < $idSession; $i++) {
                            if (isset($regleSession[$i])) {
                                unset($regleSession[$i]);
                            }
                        }
                    }
                }
                $regleSession[$idSession]['valideSelection'] = true;
                break;
            case 7:
                if (isset($regleSession[$idSession]['tokenSelection'])) {
                    $selection = $this->webservicePromotion->afficherSelectionEnCreation(array(
                        'tokenSelection' => $regleSession[$idSession]['tokenSelection']
                    ));
                    if ($selection->hasError()) {
                        $erreurSelection = $selection->getErreurs()[0]['message'];
                    } else {
                        $selectionEnCours = $selection->getValeurs();
                        $typeSelectionEnCours = $selectionEnCours[0]['valeur'];
                        if ($typeSelectionEnCours == "2" || $typeSelectionEnCours == "3") {
                            $criteriumList = $this->webserviceSelection->listerCriteres(array(
                                'filtrePourPromo' => 'true'
                            ));
                            $currentSelection = $selectionEnCours[1]['valeur'];
                            $currentSelection['selectionPromotion'] = true;
                            $js_vars = \json_encode(array(
                                "selection" => $currentSelection,
                                "edition" => true,
                                "readOnly" => true,
								"criteriumList" => $criteriumList->responseManager->getCriteriumListFromCriteres(),
								"operatorsList" => $criteriumList->responseManager->getOperatorsListFromCriteres(),
								"aggregationsList" => $criteriumList->responseManager->getAggregationsListFromCriteres(),
                            ));
                        }
                    }
                }
                break;
        }
        $session->set('regle-' . $type, $regleSession);

        $globalMarkers = array(
            'etapes' => $this->getEtapes($type),
            'type' => $type,
            'numEtape' => $numEtape,
            'idSession' => $idSession,
            'pointsDeVente' => $regleSession[$idSession]['pointsDeVente'],
            'nbPerPage' => self::NB_PRODUIT_PAR_PAGE,
            'idRegle' => $idRegle,
            'resultsCreaRegle' => $resultsCreaRegle,
            'regleSession' => $session->get('regle-' . $type),
            'js_vars' => $js_vars,
            'erreurSelection' => $erreurSelection,
        );

        $template = '@Requeteur/Regle/etapes.html.twig';
        return $this->render($template, array_merge($markers, $globalMarkers));
    }

	#[Route('/creation-selection/{idSession}/{idRegle}', name: 'aquitem_requeteur_regle_creation_selection', defaults: ['idSession' => '0', 'idRegle' => ''], options: ['expose' => true])]
    public function creationSelectionAction(Request $request, $idSession, $idRegle): Response|RedirectResponse
    {
        $session = $request->getSession();
        $regleSession = $session->get('regle-avantage-fid');

		if (!$regleSession) {
			return $this->redirectToRoute('aquitem_requeteur_regle_etapes');
		}

		if (!array_key_exists($idSession, $regleSession)) {
			return $this->redirectToRoute('aquitem_requeteur_regle_etapes');
		}

		$sites = $regleSession[$idSession]['sites'];
		$magasins = $regleSession[$idSession]['magasins'];

		$listSelectionsToCriterium = $this->getListSelectionsToCriterium();

		$currentSelection = null;
        $edition = false;
        $erreurSelection = "";

        if (isset($regleSession[$idSession]['tokenSelection']) && isset($regleSession[$idSession]['typeSelection']) && $regleSession[$idSession]['typeSelection'] == 2) {
            if (isset($regleSession[$idSession]['idPromotion'])) {
                $recupSelection = $this->webservicePromotion->afficherSelection(array(
                    'idPromotion' => $regleSession[$idSession]['idPromotion']
                ));
            } elseif ($regleSession[$idSession]['tokenSelection']) {
                $recupSelection = $this->webservicePromotion->afficherSelectionEnCreation(array(
                    'tokenSelection' => $regleSession[$idSession]['tokenSelection']
                ));
            }
            if ($recupSelection->hasError()) {
                $erreurSelection = $recupSelection->getErreurs()[0]['message'];
            } else {
                $selectionEnCours = $recupSelection->getValeurs()->toArray();
                $indexTypeSelection = array_search('typeSelection', array_column($selectionEnCours, 'nom'));
                $indexValeursSelection = array_search('selection', array_column($selectionEnCours, 'nom'));

                $typeSelection = (int) $selectionEnCours[$indexTypeSelection]['valeur'];
                if ($typeSelection == 2) {
                    $edition = true;
                }
                $currentSelection = $selectionEnCours[$indexValeursSelection]['valeur'];
                $currentSelection['selectionPromotion'] = true;
            }
        }

		$criteriumList = $this->webserviceSelection->listerCriteres(array(
			'filtrePourPromo' => 'true'
		));
		$criteriumListValues = $criteriumList->responseManager->getCriteriumListFromCriteres();
        $criteriumListValues[] = $listSelectionsToCriterium;
        $template = '@Requeteur/Regle/Partials/Promotion/selection-contacts-creation.html.twig';

        return $this->render($template, array(
            'erreurSelection' => $erreurSelection,
            'regleSession' => $regleSession,
            'sites' => $sites,
            'magasins' => $magasins,
            'etapes' => $this->getEtapes('avantage-fid'),
            'type' => 'avantage-fid',
            'idRegle' => $idRegle,
            'numEtape' => 3,
            'idSession' => $idSession,
            'pointsDeVente' => $this->webserviceExclusion->listerPointsDeVenteExclusion()->getValeurs()[0]['valeurs'],
            "criteriumList" => $criteriumListValues,
            "js_vars" => \json_encode(array(
                "selection" => $currentSelection,
                "edition" => $edition,
                "criteriumList" => $criteriumListValues,
                "operatorsList" => $criteriumList->responseManager->getOperatorsListFromCriteres(),
                "aggregationsList" => $criteriumList->responseManager->getAggregationsListFromCriteres(),
            )),
        ));
    }

	#[Route('/selections-contacts', name: 'aquitem_requeteur_regle_selections_contacts', options: ['expose' => true])]
    public function selectionsContactsAction(Request $request): Response
    {
        $searchKeywords = "";
        if ($request->isMethod('POST')) {
            foreach ($request->request->all() as $key => $data) {
                $datas[$key] = $data;
            }
            $searchKeywords = $datas['searchKeywords'];
        }
        /** @var WebserviceResponse $listeDossiers */
        $listeDossiers = $this->webserviceSelection->listerDossiers(array('texteRecherche' => $searchKeywords));

        /** @var ArrayCollection $valeurs */
        $valeurs = $listeDossiers->getValeurs();
        $markers['mesSelections'] = $valeurs[0]['valeurs'];
        $markers['searchKeywords'] = $searchKeywords;

        $template = '@Requeteur/Regle/Partials/Promotion/selection-contacts-liste.html.twig';
        return $this->render($template, $markers);
    }

	#[Route('/gestion-conflits/{type}/{idSession}/{idRegle}', name: 'aquitem_requeteur_regle_gestion_conflits', defaults: ['idSession' => '0', 'idRegle' => ''], options: ['expose' => true])]
    public function gestionConflitsAction(Request $request, $type, $idSession, $idRegle): RedirectResponse
    {
        $session = $request->getSession();
        $regleSession = $session->get('regle-' . $type);
        $conflits = [];
        if ($regleSession && array_key_exists($idSession, $regleSession)) {
            if ($request->isMethod('post')) {
                foreach ($request->request->all() as $key => $data) {
                    if ($key == "idsNiveaux" || $key == "valeursNiveaux" || $key == "typeNiveaux" || $key == "libNiveaux") {
                        if ($data == "all" || $data == "") {
                            $data = [];
                        } elseif ($key == "idsNiveaux" || $key == "typeNiveaux") {
                            $data = explode(',', $data);
                        } elseif ($key == "valeursNiveaux" || $key == "libNiveaux") {
                            $data = (array) json_decode($data);
                        }
                    }
                    $regleDatas[$key] = $data;
                }

                $regleSession[$idSession] = array_merge($regleSession[$idSession], $regleDatas);
                $session->set('regle-' . $type, $regleSession);
            }
            if ($type == "avantage-fid") {
                if ($idRegle) {
                    $verifConflits = $this->webservicePromotion->verifierModificationPromotion($regleSession[$idSession]);
                } else {
                    $verifConflits = $this->webservicePromotion->verifierPromotion($regleSession[$idSession]);
                }
            } elseif ($type == "bri") {
                if ($idRegle) {
                    $verifConflits = $this->webserviceBri->verifierModificationBRI($regleSession[$idSession]);
                } else {
                    $verifConflits = $this->webserviceBri->verifierBRI($regleSession[$idSession]);
                }
            } elseif ($type == "exclusion") {
                if ($idRegle) {
                    $verifConflits = $this->webserviceExclusion->verifierModificationExclusion($regleSession[$idSession]);
                } else {
                    $verifConflits = $this->webserviceExclusion->verifierExclusion($regleSession[$idSession]);
                }
            }
            if ($verifConflits->hasError()) {
                $conflits['erreur'] = $verifConflits->getErreurs()[0]['message'];
                $regleSession[$idSession]['conflits'] = $conflits;
                $session->set('regle-' . $type, $regleSession);
                if ($type == "avantage-fid") {
                    return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession, 'numEtape' => 4, 'etape' => $this->getEtapes($type, 4), 'idRegle' => $idRegle));
                } elseif ($type == "bri") {
                    return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession, 'numEtape' => 3, 'etape' => $this->getEtapes($type, 3), 'idRegle' => $idRegle));
                } elseif ($type == "exclusion") {
                    return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession, 'numEtape' => 2, 'etape' => $this->getEtapes($type, 2), 'idRegle' => $idRegle));
                }
            } else {
                $verifConflits = $verifConflits->getValeurs();
                if ($verifConflits[0]['xml']['etat'] == "KO") {
                    $conflits['erreur'] = $verifConflits[0]['xml']['erreur'];
                }
                if ($verifConflits[0]['xml']['resultat'] != "") {
                    $hasConflits = false;

                    foreach ($verifConflits[0]['xml']['resultat'] as $res) {
                        if (is_array($res['collectionPromoBlocante'])) {
                            $conflits['promotionsBloquantes'] = $res['collectionPromoBlocante']['promoblocante'];
                            if (!array_key_exists(0, $res['collectionPromoBlocante']['promoblocante'])) {
                                $conflits['promotionsBloquantes'] = array(0 => $res['collectionPromoBlocante']['promoblocante']);
                            }
                            // formatage dates + suppression regles en doublon
                            $doublon = false;
                            $keyConflitASupprimer = [];
                            $idPromo = $conflits['promotionsBloquantes'][0]['IDPROMOTION'];
                            foreach ($conflits['promotionsBloquantes'] as $key => $promoBloquante) {
                                $countIdRegle = array_count_values(array_column($conflits['promotionsBloquantes'], 'IDPROMOTION'))[$promoBloquante['IDPROMOTION']];
                                if ($countIdRegle > 1) {
                                    if ($doublon && $idPromo == $promoBloquante['IDPROMOTION']) {
                                        $keyConflitASupprimer[] = $key;
                                    }
                                    $doublon = true;
                                }
                                $idPromo = $promoBloquante['IDPROMOTION'];
                                $dateDebut = \DateTime::createFromFormat('d/m/Y H:i:s', $promoBloquante['DATEDEBUTCONFLIT'])->format('Y-m-d');
                                $formatedDateDebut = date_create($dateDebut);
                                $conflits['promotionsBloquantes'][$key]['DATEDEBUT'] = array(
                                    'jour' => date_format($formatedDateDebut, 'j'),
                                    'mois' => date_format($formatedDateDebut, 'n'),
                                    'annee' => date_format($formatedDateDebut, 'Y'),
                                );
                                $dateFin = \DateTime::createFromFormat('d/m/Y H:i:s', $promoBloquante['DATEFINCONFLIT'])->format('Y-m-d');
                                $formatedDateFin = date_create($dateFin);
                                $conflits['promotionsBloquantes'][$key]['DATEFIN'] = array(
                                    'jour' => date_format($formatedDateFin, 'j'),
                                    'mois' => date_format($formatedDateFin, 'n'),
                                    'annee' => date_format($formatedDateFin, 'Y'),
                                );
                                // recup createur promo en conflit
                                if ($type == "avantage-fid") {
                                    $loginRegle = $this->webservicePromotion->afficherLibelleLoginPromotion(array('login' => $promoBloquante['LOGIN']));
                                } elseif ($type == "bri") {
                                    $loginRegle = $this->webserviceBri->afficherLibelleLoginBRI(array('login' => $promoBloquante['LOGIN']));
                                } elseif ($type == "exclusion") {
                                    $loginRegle = $this->webserviceExclusion->afficherLibelleLoginExclusion(array('login' => $promoBloquante['LOGIN']));
                                }
                                $conflits['promotionsBloquantes'][$key]['createur'] = $loginRegle->getSingleValeur();
                            }
                            if ($keyConflitASupprimer !== []) {
                                foreach ($keyConflitASupprimer as $keyConflit) {
                                    unset($conflits['promotionsBloquantes'][$keyConflit]);
                                }
                            }
                            $hasConflits = true;
                        }

                        if (is_array($res['collectionPromoBloquee'])) {
                            $conflits['promotionsBloquees'] = $res['collectionPromoBloquee']['promobloquee'];
                            if (!array_key_exists(0, $res['collectionPromoBloquee']['promobloquee'])) {
                                $conflits['promotionsBloquees'] = array(0 => $res['collectionPromoBloquee']['promobloquee']);
                            }
                            // formatage dates + suppression regles en doublon
                            $doublon = false;
                            $keyConflitASupprimer = [];
                            foreach ($conflits['promotionsBloquees'] as $key => $promoBloquee) {
                                $countIdRegle = array_count_values(array_column($conflits['promotionsBloquees'], 'IDPROMOTION'))[$promoBloquee['IDPROMOTION']];
                                if ($countIdRegle > 1) {
                                    if ($doublon) {
                                        $keyConflitASupprimer[] = $key;
                                    }
                                    $doublon = true;
                                }

                                $dateDebut = \DateTime::createFromFormat('d/m/Y H:i:s', $promoBloquee['DATEDEBUTCONFLIT'])->format('Y-m-d');
                                $formatedDateDebut = date_create($dateDebut);
                                $conflits['promotionsBloquees'][$key]['DATEDEBUT'] = array(
                                    'jour' => date_format($formatedDateDebut, 'j'),
                                    'mois' => date_format($formatedDateDebut, 'n'),
                                    'annee' => date_format($formatedDateDebut, 'Y'),
                                );
                                $dateFin = \DateTime::createFromFormat('d/m/Y H:i:s', $promoBloquee['DATEFINCONFLIT'])->format('Y-m-d');
                                $formatedDateFin = date_create($dateFin);
                                $conflits['promotionsBloquees'][$key]['DATEFIN'] = array(
                                    'jour' => date_format($formatedDateFin, 'j'),
                                    'mois' => date_format($formatedDateFin, 'n'),
                                    'annee' => date_format($formatedDateFin, 'Y'),
                                );
                                // recup createur promo en conflit
                                if ($type == "avantage-fid") {
                                    $loginRegle = $this->webservicePromotion->afficherLibelleLoginPromotion(array('login' => $promoBloquee['LOGIN']));
                                } elseif ($type == "bri") {
                                    $loginRegle = $this->webserviceBri->afficherLibelleLoginBRI(array('login' => $promoBloquee['LOGIN']));
                                } elseif ($type == "exclusion") {
                                    $loginRegle = $this->webserviceExclusion->afficherLibelleLoginExclusion(array('login' => $promoBloquee['LOGIN']));
                                }
                                $conflits['promotionsBloquees'][$key]['createur'] = $loginRegle->getSingleValeur();
                            }
                            if ($keyConflitASupprimer !== []) {
                                foreach ($keyConflitASupprimer as $keyConflit) {
                                    unset($conflits['promotionsBloquees'][$keyConflit]);
                                }
                            }
                            $hasConflits = true;
                        }
                        if (is_array($res['collectionProduit'])) {
                            $regleSession[$idSession]['nbProduitsReels'] = (int) $res['collectionProduit']['NBPRODUITSREELS'];
                            $regleSession[$idSession]['nbProduitsInvalidesImport'] = (int) $res['collectionProduit']['NBPRODUITINVALIDES'];
                        } elseif (is_array($res['collectionNiveau'])) {
                            $regleSession[$idSession]['nbProduitsReels'] = (int) $res['collectionNiveau']['NBPRODUITSREELS'];
                        } elseif (is_array($res['collectionTousLesProduits'])) {
                            $regleSession[$idSession]['nbProduitsReels'] = (int) $res['collectionTousLesProduits']['NBPRODUITSREELS'];
                        }
                    }
                    $regleSession[$idSession]['conflits'] = $conflits;

                    $session->set('regle-' . $type, $regleSession);
                    if ($hasConflits) {
                        if ($type == "avantage-fid") {
                            return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession, 'numEtape' => 4, 'etape' => $this->getEtapes($type, 4), 'idRegle' => $idRegle));
                        } elseif ($type == "bri") {
                            return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession, 'numEtape' => 3, 'etape' => $this->getEtapes($type, 3), 'idRegle' => $idRegle));
                        } elseif ($type == "exclusion") {
                            return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession, 'numEtape' => 2, 'etape' => $this->getEtapes($type, 2), 'idRegle' => $idRegle));
                        }
                    }
                }
            }
            if ($type == "avantage-fid") {
                return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession, 'numEtape' => 5, 'etape' => $this->getEtapes($type, 5), 'idRegle' => $idRegle));
            } elseif ($type == "bri") {
                return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession, 'numEtape' => 4, 'etape' => $this->getEtapes($type, 4), 'idRegle' => $idRegle));
            } elseif ($type == "exclusion") {
                return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type, 'idSession' => $idSession, 'numEtape' => 3, 'etape' => $this->getEtapes($type, 3), 'idRegle' => $idRegle));
            }
        }
        if ($idRegle) {
            return $this->redirectToRoute('aquitem_requeteur_regle_edition', array('type' => $type, 'idSession' => $idSession, 'idRegle' => $idRegle));
        } else {
            return $this->redirectToRoute('aquitem_requeteur_regle_etapes', array('type' => $type));
        }
    }

    function skip_accents($str, $charset = 'utf-8'): string
    {
        $str = htmlentities($str, ENT_NOQUOTES, $charset);
        $str = preg_replace('#&([A-za-z])(?:acute|cedil|caron|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
        $str = preg_replace('#&([A-za-z]{2})(?:lig);#', '\1', $str);
        return preg_replace('#&[^;]+;#', '', $str);
    }

    private function getEtapes($type, $numEtape = NULL): array|string
    {
        if ($type == "avantage-fid") {
            $etapes = array(
                1 => 'informations',
                2 => 'points-de-vente',
                3 => 'selection-contacts',
                4 => 'produits',
                5 => 'condition',
                6 => 'avantage',
                7 => 'validation',
            );
        } elseif ($type == "bri") {
            $etapes = array(
                1 => 'informations',
                2 => 'points-de-vente',
                3 => 'produits',
                4 => 'reduction',
                5 => 'validation',
            );
        } elseif ($type == "exclusion") {
            $etapes = array(
                1 => 'informations',
                2 => 'produits',
                3 => 'validation',
            );
        }
        if ($numEtape) {
            return $etapes[$numEtape];
        }
        return $etapes;
    }

    private function getPointsDeVente($typeRegle): array
    {
        if ($typeRegle == "avantage-fid") {
            /** @var WebserviceResponse $pointsDeVente */
            return  $this->webservicePromotion->listerPointsDeVentePromotion()->getValeurs()[0]['valeurs'];
        }
        if ($typeRegle == "bri") {
            /** @var WebserviceResponse $pointsDeVente */
            return  $this->webserviceBri->listerPointsDeVenteBRI()->getValeurs()[0]['valeurs'];
        }
        if ($typeRegle == "exclusion") {
            /** @var WebserviceResponse $pointsDeVente */
            return  $this->webserviceExclusion->listerPointsDeVenteExclusion()->getValeurs()[0]['valeurs'];
        }
    }

    private function getSeuilExclusion($currentSession, $idRegle): ArrayCollection
    {
        $seuil = 0;
        // compteur exclusions
        if ($idRegle) {
            $seuil = $this->webserviceExclusion->recupererLeNombreDeProduitsDisponiblesEnModificationExclusion(array(
                'idExclusion' => $idRegle,
                'dateDebut' => $currentSession['dateDebut'],
                'dateFin' => $currentSession['dateDebut'],
                'site' => $currentSession['sites'],
                'magasin' => $currentSession['magasins'],
            ))->getValeurs();
        } else {
            $seuil = $this->webserviceExclusion->recupererLeNombreDeProduitsDisponiblesExclusion(array(
                'dateDebut' => $currentSession['dateDebut'],
                'dateFin' => $currentSession['dateDebut'],
                'site' => $currentSession['sites'],
                'magasin' => $currentSession['magasins'],
            ))->getValeurs();
        }
        return $seuil;
    }

    private function getCriteresProduits($typeRegle): array|bool
    {
        if ($typeRegle == "avantage-fid") {
            $criteres = $this->webservicePromotion->listerCriteresRechercheProduitsPromotion()->getSingleValeurs();
        } elseif ($typeRegle == "exclusion") {
            $criteres = $this->webserviceExclusion->listerCriteresRechercheProduitsExclusion()->getSingleValeurs();
        }
        $counter = count($criteres);
        for ($i = 0; $i < $counter; $i++) {
            $criteresProduits[$i] = array(
                'id' => $criteres[$i]['id'],
                'libelle' => $criteres[$i]['libelle'],
                'max' => count($criteres[$i]['valeurs']),
            );
        }
        return $criteresProduits;
    }

	public function getListSelectionsToCriterium(): array
	{
		$listeDossiers = $this->webserviceSelection->listerDossiers();

		/** @var SelectionsResponseManager $listeDossiersResponseManager */
		$listeDossiersResponseManager = $listeDossiers->responseManager;

		$listeSelections = $listeDossiersResponseManager->extractSimpleSelectionList();
		foreach ($listeSelections as $key => $sel) {
			$words = explode(' ', $sel['libelle']);
			if (count($words) > 1) {
				$lib = "";
				foreach ($words as $word) {
					$lib = $lib . ' ' . (strlen($word) > 18 ? substr($word, 0, 18) . '...' : $word);
				}
			} else {
				$lib = strlen($sel['libelle']) > 18 ? substr($sel['libelle'], 0, 18) . '...' : $sel['libelle'];
			}
			$listeSelections[$key]['libelle'] = $lib;
		}
		$listSelectionsToCriterium = [
			"id" => "0",
			"libelle" => "Sélections",
			"valeurs" => [
				[
					"nom" => "criteres",
					"valeurs" => $listeSelections
				]
			]
		];
		return $listSelectionsToCriterium;
	}
}
