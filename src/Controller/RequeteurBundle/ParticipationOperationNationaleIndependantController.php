<?php

namespace App\Controller\RequeteurBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;
use App\Services\WebserviceBundle\WebserviceParticipationOperationNationaleIndependant;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route("/participation-operation-nationale-independant")]
class ParticipationOperationNationaleIndependantController extends AbstractRequeteurController
{

    protected $webservicePartOpNatIndependant;

    public function __construct(WebserviceParticipationOperationNationaleIndependant $webservicePartOpNatIndependant)
    {
        $this->webservicePartOpNatIndependant = $webservicePartOpNatIndependant;
    }

	#[Route('/{referer}/liste-independants', defaults: ['referer' => 'website'], name: 'aquitem_requeteur_participation_independant', options: ['expose' => true])]
    public function indexAction($referer = 'website'): Response
    {
        $template = '@Requeteur/ParticipationOperationNationale/independant_participation.html.twig';
        return $this->render($template, array(
            'referer' => $referer,
            'prevMonth' => (new \App\Services\RequeteurBundle\DateTime())->modify('-1 month')->format('n'),
            'nextMonth' => (new \App\Services\RequeteurBundle\DateTime())->modify('+1 month')->format('n'),
        ));
    }

	#[Route('/{referer}/liste-participations-independant/{mois}/{annee}/{tri}/{filtre}', defaults: ['referer' => 'website'], name: 'aquitem_requeteur_participation_liste_independant', options: ['expose' => true])]
    public function listeParticipationsIndependantAction($referer = 'website', $mois = "", $annee = "", $tri = "", $filtre = ""): Response
    {
        if($mois == "") {
            $mois = date("n");
        }
        if($annee == "") {
            $annee = date("Y");
        }
        $souscription = "";
        if($tri == "souscrites") {
            $souscription = "true";
        }
        elseif($tri == "nonsouscrites") {
            $souscription = "false";
        }

        /** @var WebserviceResponse $operations */
        $operations = $this->webservicePartOpNatIndependant->listerOperationsIndependant(array(
            'souscription' => $souscription,
            'mois' => $mois,
            'annee' => $annee,
            'dateFinSouscription' => $filtre,
        ));

        $template = '@Requeteur/ParticipationOperationNationale/independant_participation_liste.html.twig';

        $markers = array(
            'operations' => $operations->getValeurs()[0]['valeurs'],
            'referer' => $referer,
            'moisMax' => (new \App\Services\RequeteurBundle\DateTime())->format('n'),
            'anneeMax' => (new \App\Services\RequeteurBundle\DateTime())->format('Y'),
        );

        return $this->render($template, $markers);
    }
}
