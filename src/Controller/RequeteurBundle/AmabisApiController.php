<?php

namespace App\Controller\RequeteurBundle;

use App\Services\AmabisBundle\Consumer\CheckAddress;
use App\Services\AmabisBundle\Consumer\CheckEmail;
use App\Services\AmabisBundle\Consumer\CheckNameCivility;
use App\Services\AmabisBundle\Consumer\CheckPhoneNumber;
use App\Services\AmabisBundle\Consumer\CheckPhoneNumberSyntax;
use App\Services\AmabisBundle\Consumer\SearchBuilding;
use App\Services\AmabisBundle\Consumer\SearchEmailDomain;
use App\Services\AmabisBundle\Consumer\SearchEmailUsername;
use App\Services\AmabisBundle\Consumer\SearchNames;
use App\Services\AmabisBundle\Consumer\SearchRoad;
use App\Services\AmabisBundle\Consumer\SearchZipCodeCity;
use App\Services\RequeteurBundle\AmabisLoggerManager;
use App\Services\WebserviceBundle\WebservicePortailEnseigne;
use DateTime;
use FOS\ElasticaBundle\Index\IndexManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\VarDumper\VarDumper;

#[Route("/amabisApi")]
class AmabisApiController extends AbstractRequeteurController
{

    private $amabisLoggerManager;
    private $indexManager;
    private $codeImputation;
    protected $webservice;
    private $authKey;
    private $servicesParameters;
    private $api_errors;

    public function __construct(AmabisLoggerManager $amabisLoggerManager, IndexManager $indexManager, WebservicePortailEnseigne $webservice) {
        $this->amabisLoggerManager = $amabisLoggerManager;
        $this->indexManager = $indexManager;
        $this->webservice = $webservice;
		$this->initAmaParams();
    }

    public function initAmaParams() {

        $magasins = $this->webservice->listerMagasins()->getSingleValeurs();
        $amaGeneral = $this->webservice->listerOptionsAideSaisie(array(
            'codeSite' => $magasins[0]["codeSite"],
            'codeMagasin' => $magasins[0]["codeMagasin"],
        ));
        $this->codeImputation = $amaGeneral->getValeurs()[2]['valeur'];
        $this->authKey = $amaGeneral->getValeurs()[0]['valeur'];
        $this->parsePackages($amaGeneral->getValeurs()[4]["valeurs"]);
        $this->parseErrors($amaGeneral->getValeurs()[3]["valeurs"]);

    }

    public function parsePackages($packages) {

        $sortedServices = [];
        foreach($packages as $package) {
           foreach($package["services"] as $service) {
               $sortedServices[$service["libelle"]] = $service["options"];
               $options = [];
                foreach($sortedServices[$service["libelle"]] as $option) {
                    $options[$option["libelle"]] = $option["valeur"];
                }
                $sortedServices[$service["libelle"]] = $options;
           }
        }
        $this->servicesParameters = $sortedServices;
    }

    public function parseErrors($errors) {
        $api_error = [];
        foreach($errors as $error) {
            $api_error[$error["id"]] = ["category" => $error["statut"], "label" => $error["description"]];
        }
        $this->api_errors = $api_error;
    }

	#[Route('/test')]
    public function searchElast(Request $request) {

        $finder = $this->container->get('fos_elastica.finder.app');
        // $finder = $this->container->get('fos_elastica.finder.app');
        $results = $finder->find('');
        VarDumper::dump($results);die;

        // VarDumper::dump($results);die;
    }

	#[Route('/search-zip-city', methods: ['POST'])]
    public function searchZipCity(Request $request, SearchZipCodeCity $searchZipCodeCity) {
        $requestContent = json_decode($request->getContent(), true);
        $wsParams = isset($this->servicesParameters["RECHCPV"]) ? $this->servicesParameters["RECHCPV"] : [];

        $results = $searchZipCodeCity->check(
            $this->codeImputation,
            $requestContent['search'],
            $this->authKey,
            $wsParams,
            true,
        );

        $this->callLogger($request->getClientIp(), $searchZipCodeCity::WEB_SERVICE_END_POINT, $searchZipCodeCity->getUrl(), $searchZipCodeCity->getParams($requestContent['search'], $wsParams), $this->codeImputation, $results);

        return new JsonResponse(
            $results
        );
    }

	#[Route('/search-road', methods: ['POST'])]
    public function searchRoad(Request $request, SearchRoad $searchRoad) {
        $requestContent = json_decode($request->getContent(), true);

        $wsParams = isset($this->servicesParameters["RECHVOI"]) ? $this->servicesParameters["RECHVOI"] : [];

        $results = $searchRoad->check(
            $this->codeImputation,
            $requestContent['clegeo'],
            $requestContent['clegeoagg'],
            $requestContent['search'],
            $this->authKey,
            $wsParams,
            null,
        );

        $this->callLogger($request->getClientIp(), $searchRoad::WEB_SERVICE_END_POINT, $searchRoad->getUrl(), $searchRoad->getParams($requestContent['clegeo'], $requestContent['clegeoagg'], null, $requestContent['search'], $wsParams), $this->codeImputation, $results);

        return new JsonResponse(
            $results
        );
    }

	#[Route('/search-building', methods: ['POST'])]
    public function searchBuilding(Request $request, SearchBuilding $searchBuilding) {
        $requestContent = json_decode($request->getContent(), true);

        $wsParams = isset($this->servicesParameters["RECHBAT"]) ? $this->servicesParameters["RECHBAT"] : [];

        $results = $searchBuilding->check(
            $this->codeImputation,
            $requestContent['clevoie'],
            $requestContent['numero'],
            $requestContent['search'],
            $this->authKey,
            $wsParams
        );

        $this->callLogger($request->getClientIp(), $searchBuilding::WEB_SERVICE_END_POINT, $searchBuilding->getUrl(), $searchBuilding->getParams($requestContent['clevoie'],$requestContent['numero'],$requestContent['search'], $wsParams), $this->codeImputation, $results);

        return new JsonResponse(
            $results
        );
    }

	#[Route('/search-email-username', methods: ['POST'])]
    public function searchEmailUsername(Request $request, SearchEmailUsername $searchEmailUsername) {
        $requestContent = json_decode($request->getContent(), true);

        $wsParams = isset($this->servicesParameters["AMA_USERMAIL_GET"]) ? $this->servicesParameters["AMA_USERMAIL_GET"] : [];

        $results = $searchEmailUsername->check(
            $this->codeImputation,
            $requestContent['search'],
            $requestContent['firstname'],
            $requestContent['lastname'],
            $this->authKey,
            $wsParams,
			true,
        );

        $this->callLogger($request->getClientIp(), $searchEmailUsername::WEB_SERVICE_END_POINT, $searchEmailUsername->getUrl(), $searchEmailUsername->getParams($requestContent['firstname'], $requestContent['lastname'], $requestContent['search'], $wsParams), $this->codeImputation, $results);

        return new JsonResponse(
            $results
        );
    }

	#[Route('/search-email-domain', methods: ['POST'])]
    public function searchEmailDomain(Request $request, SearchEmailDomain $searchEmailDomain) {
        $requestContent = json_decode($request->getContent(), true);

        $wsParams = isset($this->servicesParameters["RECHREF_DOMAINES"]) ? $this->servicesParameters["RECHREF_DOMAINES"] : [];

        $results = $searchEmailDomain->check(
            $this->codeImputation,
            $requestContent['username'],
            $requestContent['domain'],
            $this->authKey,
            $wsParams,
			true,
        );

        $this->callLogger($request->getClientIp(), $searchEmailDomain::WEB_SERVICE_END_POINT, $searchEmailDomain->getUrl(), $searchEmailDomain->getParams($requestContent['domain']), $this->codeImputation, $results);

        return new JsonResponse(
            $results
        );
    }

	#[Route('/check-email', methods: ['POST'])]
    public function checkEmail(Request $request, CheckEmail $checkEmail) {
        $requestContent = json_decode($request->getContent(), true);

        $results = $checkEmail->check(
            $this->codeImputation,
            $requestContent['email'],
            $this->authKey,
            []
        );

        $this->callLogger($request->getClientIp(), $checkEmail::WEB_SERVICE_END_POINT, $checkEmail->getUrl(), $checkEmail->getParams($requestContent['email']), $this->codeImputation, $results);

        return new JsonResponse(
            $results
        );
    }

	#[Route('/check-phone-number', methods: ['POST'])]
    public function checkPhoneNumber(Request $request, CheckPhoneNumberSyntax $checkPhoneNumberSyntax, CheckPhoneNumber $checkPhoneNumber) {
        $requestContent = json_decode($request->getContent(), true);

        // $globalDefinitions = $globalDefinitionsService->call();
        // $pays = $globalDefinitions->getPaysByCodeAndIndicatif($requestContent['codePays']);

        $reference = $this->webservice->indicatifNumeriqueToTexte(array(
            'referenceNumerique' => str_replace("-", "", $requestContent["codePays"])
        ));

        if(!isset($reference->getValeurs()[0]["valeurs"][0])) {
            return new JsonResponse([
                'noCountry' => true
            ]);
        }

        $pays = $reference->getValeurs()[0]["valeurs"][0];

        $syntaxResult = $checkPhoneNumberSyntax->check(
            $this->codeImputation,
            $requestContent['number'],
            $this->authKey,
            $pays,
            []
        );

        $validationResult = [];
        if(isset($syntaxResult['is_valid']) && $syntaxResult['is_valid'] == true && $syntaxResult['number_type'] == 'MOBILE') {
            $validationResult = $checkPhoneNumber->check(
                $this->codeImputation,
                $requestContent['number'],
                $this->authKey,
                []
            );
        }

        $this->callLogger($request->getClientIp(), $checkPhoneNumberSyntax::WEB_SERVICE_END_POINT, $checkPhoneNumberSyntax->getUrl(), $checkPhoneNumber->getParams($requestContent['number']), $this->codeImputation, [$syntaxResult, $validationResult]);

        return new JsonResponse([
            'syntax' => $syntaxResult,
            'validation' => $validationResult
        ]);
    }

	#[Route('/check-address', methods: ['POST'])]
    public function checkAddress(Request $request, CheckAddress $checkAddress) {
        $requestContent = json_decode($request->getContent(), true);

        $wsParams = isset($this->servicesParameters["RNVP"]) ? $this->servicesParameters["RNVP"] : [];

        $result = $checkAddress->checkAddress(
            $this->codeImputation,
            $requestContent['complement'],
            $requestContent['batiment'],
            $requestContent['lieuDit'],
            $requestContent['voie'],
            $requestContent['codePostalVille'],
            $requestContent['pays'],
            $this->authKey,
            $wsParams,
             $this->api_errors,
             true,
             [],
             true
        );

        $params = $checkAddress->getParamsAdress($requestContent['voie'], $requestContent['lieuDit'], $requestContent['batiment'], $requestContent['complement'], $requestContent['codePostalVille'], $requestContent['pays'], $wsParams);

        $this->callLogger($request->getClientIp(), $checkAddress::WEB_SERVICE_END_POINT, $checkAddress->getUrl(), $params, $this->codeImputation, $result["result"]);

        return new JsonResponse(
            $result["formatedResult"]
        );
    }

	#[Route('/check-address-from-result', methods: ['POST'])]
    public function checkAddressFromResult(Request $request, CheckAddress $checkAddress) {
        $requestContent = json_decode($request->getContent(), true);

        $wsParams = isset($this->servicesParameters["RNVP"]) ? $this->servicesParameters["RNVP"] : [];

        $result = $checkAddress->checkFromResult(
            $this->codeImputation,
            $requestContent['adresse'],
            $this->authKey,
            $wsParams,
             $this->api_errors,
             true,
             [],
             true
        );

        $this->callLogger($request->getClientIp(), $checkAddress::WEB_SERVICE_END_POINT, $checkAddress->getUrl(), $checkAddress->getParamsResults($requestContent['adresse'], $wsParams ), $this->codeImputation, $result["result"]);

        return new JsonResponse(
            $result["formatedResult"],
        );
    }

	#[Route('/check-name-civility', methods: ['POST'])]
    public function checkNameCivility(Request $request, CheckNameCivility $checkNameCivility) {
        $requestContent = json_decode($request->getContent(), true);

        $wsParams = isset($this->servicesParameters["ANALNOM"]) ? $this->servicesParameters["ANALNOM"] : [];

        $result = $checkNameCivility->check(
            $this->codeImputation,
            $requestContent['prenom'],
            $requestContent['nom'],
            $this->authKey,
            $wsParams,
            []
        );

        $this->callLogger($request->getClientIp(), $checkNameCivility::WEB_SERVICE_END_POINT, $checkNameCivility->getUrl(), $checkNameCivility->getParams($requestContent['prenom'],$requestContent['nom'], $wsParams), $this->codeImputation, $result);

        return new JsonResponse(
            $result
        );
    }

	#[Route('/search-firstName', methods: ['POST'])]
    public function searchFirstName(Request $request, SearchNames $searchNames) {
        $requestContent = json_decode($request->getContent(), true);

        $wsParams = isset($this->servicesParameters["RECHREF_PRENOMS"]) ? $this->servicesParameters["RECHREF_PRENOMS"] : [];

        $results = $searchNames->checkPrenom(
            $this->codeImputation,
            $requestContent['search'],
            $this->authKey,
            $wsParams,
			true,
        );


        $this->callLogger($request->getClientIp(), $searchNames::WEB_SERVICE_END_POINT, $searchNames->getUrl(), $searchNames->getParams($requestContent['search'], "prenoms", $wsParams), $this->codeImputation, $results);

        return new JsonResponse(
            $results
        );
    }

	#[Route('/search-lastName')]
    public function searchLastName(Request $request, SearchNames $searchNames) {
        $requestContent = json_decode($request->getContent(), true);

        $wsParams = isset($this->servicesParameters["RECHREF_PATRONYMES"]) ? $this->servicesParameters["RECHREF_PATRONYMES"] : [];

        $results = $searchNames->checkPatronyme(
            $this->codeImputation,
            $requestContent['search'],
            $this->authKey,
            $wsParams,
			true,
        );

        $this->callLogger($request->getClientIp(), $searchNames::WEB_SERVICE_END_POINT, $searchNames->getUrl(),  $searchNames->getParams($requestContent['search'], "patronymes", $wsParams), $this->codeImputation, $results);

        return new JsonResponse(
            $results
        );
    }

    public function callLogger($ipUser, $wsName, $urlCalled, $params, $codeImputation, $response) {

        $date = new DateTime();
        $hour = $date->format('H:i:s');
        $date = $date->format('Y-m-d');

        $idEnseigne = $this->getUser()->getIdRequeteur();
        $enseigneName = $this->getUser()->getRequeteur();
        $user = $this->getUser()->getUsername();

        $this->amabisLoggerManager->logStructurer($date, $hour, $idEnseigne, $enseigneName, $user, $ipUser, $urlCalled, $wsName, $params, $response, $this->codeImputation, null);
    }
}
