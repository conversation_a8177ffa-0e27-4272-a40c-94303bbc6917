<?php

namespace App\Controller\RequeteurBundle;

use App\Services\WebserviceBundle\ImageManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\KernelInterface;

#[Route("/mosaico")]
class MosaicoController extends AbstractController
{
    /**
     * Permet de charger le html provenant d'une url
     */
	#[Route("/html/", name: "aquitem_requeteur_mosaico_html", options: ["expose" => true], methods: ["GET"])]
    public function htmlAction(Request $request): Response
    {
        $url = $request->query->get('url');
        if (substr($url, 0, 8) !== "https://" && substr($url, 0, 7) !== "http://") {
            $url =  "https://" . $url;
        }
        $html = file_get_contents($url);
        return new Response($html);
    }

    /**
     * Permet de créer des images placeholder
     */
	#[Route("/img/", name: "aquitem_requeteur_mosaico_img", options: ["expose" => true], methods: ["GET"])]
    public function imgAction(Request $request, ImageManager $imageManager): Response|RedirectResponse
    {
        $method = $request->query->get("method");
        $params = explode(",", $request->query->get("params"));
        $width = (int) $params[0];
        $height = (int) $params[1];
        if ($method === "placeholder") {
            $image = $imageManager->createPlaceholder($width, $height);
            $response = new Response();
            $response->headers->set('Content-Type', 'image/png');
            $response->setContent($image);
            return $response;
        } elseif ($method === "resize") {
            return $this->redirect($request->query->get('src'));
        }
        return new Response();
    }

    /**
     * Permet de lister les images et d'en uploader
     */
	#[Route("/upload/{editor}", name: "aquitem_requeteur_mosaico_upload", defaults: ["editor" => "ckeditor"], options: ["expose" => true], methods: ["GET", "POST"])]
    public function uploadAction(Request $request, ImageManager $imageManager, $editor): JsonResponse
    {
        $files = [];
        $message = "";
        $config = $imageManager->getConfig();
        // GET = Retourne la liste des images
        if ($request->getMethod() === "GET") {
            $images = $imageManager->list($this->getUser());
            $path = sprintf("%s/%s/", $this->getUser()->getIdRequeteur(), $this->getUser()->getId());
            $urlAccesHttp = $config['UrlAccesHttp'];
            if (substr($urlAccesHttp, -1, 1) !== '' && substr($urlAccesHttp, -1, 1) !== '0') {
                $urlAccesHttp = $config['UrlAccesHttp'] . '/';
            }
            // dd($path, $urlAccesHttp);
            foreach ($images as $key => $image) {
                if ($key < count($images)) {

                    $file = [
                        "name" => $image,
                        "url" => $image,
                        "thumbnailUrl" => $image,
                        "width" => getimagesize($image) ? getimagesize($image)[0] : 0,
                        "height" => getimagesize($image) ? getimagesize($image)[1] : 0,
                    ];
                    $files[] = $file;
                }
            }
        } elseif ($request->getMethod() === "POST") {
            if ($request->files->count() !== 0) {
                try {
                    $file = $imageManager->upload($request->files->get('files')[0], $this->getUser(), $editor);
                    $files[] = [
                        "name" => $file["name"],
                        "url" => $file["url"],
                        "thumbnailUrl" => $file["thumbnailUrl"],
                    ];
                } catch (\Exception $e) {
                    return new JsonResponse(array("files" => $files, "message" => "js.fileAlreadyExist"));
                };
            }
        }
        //return new JsonResponse(array("files" => $files));
        return new JsonResponse(array("files" => $files, "message" => $message));
    }

	#[Route("/mosaico-template", name: "aquitem_requeteur_mosaico_get_template", options: ["expose" => true], methods: ["GET"])]
    public function getTemplateAction(Request $request, KernelInterface $kernel): JsonResponse
    {
        $template = 'template-versafix-alienor-1.html';
        $user = $this->getUser();
        $templateExist = file_exists($kernel->getProjectDir() . '/public/js/vendor/mosaico/templates/versafix-alienor-1/template-versafix-alienor-' . $user->getIdRequeteur() . '.html');
        if ($templateExist) {
            $template = 'template-versafix-alienor-' . $user->getIdRequeteur() . '.html';
        }

        if (isset($request->getSession()->get('current_user_profil')['langue']) && $request->getSession()->get('current_user_profil')['langue'] == "en") {
            $template = 'template-versafix-alienor-en.html';
        }

        return new JsonResponse(array("template" => $template));
    }

    /**
     * Permet de créer des images à partir d'existante
     */
	#[Route("/img-resize/{imgSrc}/{widthMax}/{prefix}", defaults: ["prefix" => ""], name: "aquitem_requeteur_mosaico_img_resize", options: ["expose" => true], methods: ["GET"])]
    public function imgResizeAction(Request $request, ImageManager $imageManager): Response
    {
        $user = $this->getUser();
        $routeParams = $request->attributes->get('_route_params');
        $imgSrc = urldecode($routeParams['imgSrc']);

        $image = $imageManager->createNewImgResizedFromExisting($imgSrc, $routeParams['widthMax'], $routeParams['prefix']);
        $file = new File($request->server->get('DOCUMENT_ROOT') . $request->getBasePath() . '/' . $image['src']);
        $hash = substr(hash_file('md5', $file->getPathname()), 0, 10);
        $path = sprintf("/%s/%s/%s", $user->getIdRequeteur(), $user->getId(), $hash);

        $localFile = $request->server->get('DOCUMENT_ROOT') . $request->getBasePath() . '/' . $image['src'];

        $imageUploaded = $imageManager->uploadFileToFtp($image, 0, "", $hash, $localFile, $this->getUser());

        // supprimer l'image locale du serveur
        unlink($localFile);

        $response = new Response();
        $response->setContent($imageUploaded);
        return $response;
    }
}
