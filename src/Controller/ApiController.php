<?php

namespace App\Controller;

use App\Command\RefreshAllServicesCommand;
use App\Entity\WebHost\WebHost;
use App\Message\FetchSwarmpitMessage;
use App\Message\RedeployServicesMessage;
use App\Service\CsvExporter;
use App\Service\MercureUpdater;
use App\Service\RegistryAPI;
use App\Service\SwarmpitAPI;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Process\Process;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api')]
class ApiController extends AbstractController
{
    public const URL_REGEX = '([A-z0-9-\.])+';

    public function __construct(
        private readonly SwarmpitAPI $api,
        private readonly MercureUpdater $updater
    ) {
    }

    #[Route('/services')]
    public function index(
        #[Autowire(param: 'kernel.project_dir')] string $projectDir,
        #[MapQueryParameter('force')] bool $force = false
    ): JsonResponse {
        if ($force) {
            $process = new Process(['php', $projectDir . '/bin/console', RefreshAllServicesCommand::getDefaultName()]);
            $process->run();
        }

        $result = $this->api->getAllServices();

        return $this->json($result);
    }

    #[Route('/services/{serverName}/{serviceId}')]
    public function service(string $serverName, string $serviceId): JsonResponse
    {
        $code = 200;
        try {
            $this->api->setNoCache(true);
            $server = $this->api->getServer($serverName);
            $service = $this->api->getServiceAndTasksInfos($server, $serviceId, true);
            $service = $this->api->formatService($server, $service);

            $this->updater->updateService($service);
            $response = $service;
        } catch (\Exception $exception) {
            $this->updater->sendError($serviceId, $exception->getMessage());
            $response = [
                'id' => $serviceId,
                'exception' => $exception->getMessage(),
            ];
            $code = 503;
        }

        return $this->json($response, $code);
    }

    #[Route('/details/{serverName}/{service}')]
    public function details(string $serverName, string $service): JsonResponse
    {
        $details = $this->api->getDetails($serverName, $service);

        return $this->json($details);
    }

    #[Route('/redeploy/{serverName}/{serviceId}', methods: 'GET', requirements: ['serverName' => self::URL_REGEX, 'serviceId' => self::URL_REGEX])]
    public function redeploy(string $serverName, string $serviceId, Request $request, MessageBusInterface $bus): JsonResponse
    {
        $bus->dispatch(new RedeployServicesMessage([
            ['serverName' => $serverName, 'serviceId' => $serviceId],
        ], $request->query->get('tag')));

        return $this->json([
            'status' => 'OK',
        ]);
    }

    #[Route('/redeploy', methods: 'POST')]
    public function redeployBatch(Request $request, MessageBusInterface $bus): JsonResponse
    {
        $bus->dispatch(new RedeployServicesMessage($request->toArray(), $request->query->get('tag')));

        return $this->json([
            'status' => 'OK',
        ]);
    }

    #[Route('/refresh', methods: 'POST')]
    public function refreshBatch(Request $request, MessageBusInterface $bus): JsonResponse
    {
        $bus->dispatch(new FetchSwarmpitMessage($request->toArray(), $request->query->get('tag')));

        return $this->json([
            'status' => 'OK',
        ]);
    }

    #[Route('/registry/tags/{image}', requirements: ['image' => '.+'])]
    public function registryTags(string $image, RegistryAPI $registryAPI): JsonResponse
    {
        $result = $registryAPI->getTags($image);

        return $this->json($result);
    }

    #[Route('/composer-usage', methods: ['POST'])]
    public function composerUsage(Request $request): JsonResponse
    {
        /** @var array{ package: string } $payload */
        $payload = $request->toArray();

        if (!isset($payload['package'])) {
            throw new BadRequestHttpException('Missing parameter : package');
        }

        $services = $this->api->getAllServices();
        $result = [];

        foreach ($services['services'] as $service) {
            if (isset($service['details']['composer']['installed']) && isset($service['details']['git']['url'])) {
                if (false !== array_search($payload['package'], array_column($service['details']['composer']['installed'], 'name'))) {
                    if (str_contains($service['details']['git']['url'], 'gitlab.alienor.net')) {
                        $result[] = str_replace('https://gitlab.alienor.net/', '', $service['details']['git']['url']);
                    }
                }
            }
        }

        $result = array_values(array_unique($result));

        sort($result);

        return $this->json($result);
    }

    #[Route('/download-csv', methods: ['POST'])]
    public function downloadCsv(Request $request, CsvExporter $csvExporter): Response
    {
        $payload = $request->toArray();

        $ids = [];
        if (isset($payload['ids'])) {
            $ids = $payload['ids'];
        }

        $csvContent = $csvExporter->generateCsv($ids);

        $response = new Response($csvContent);
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="tdb-swarm.csv"');

        return $response;
    }

    #[\Symfony\Component\Routing\Annotation\Route('/webHosts/data')]
    public function webHostsData(ManagerRegistry $doctrine): JsonResponse
    {
        // TODO crééer une classe de Repository dédiée
        $webHosts = $doctrine->getManagerForClass(WebHost::class)->createQueryBuilder()
            ->select('w, u, c')
            ->from(WebHost::class, 'w')
            ->leftJoin('w.urls', 'u')
            ->leftJoin('w.configuration', 'c')
            ->orderBy('w.name', 'ASC')
            ->getQuery()
            ->getResult()
        ;

        // Transformer les entités en tableau pour l'API
        $data = [];
        foreach ($webHosts as $webHost) {
            $urls = [];
            foreach ($webHost->getUrls() as $url) {
                $urls[] = [
                    'id' => $url->getId(),
                    'url' => $url->getUrl(),
                    'healthCheckReport' => [
                        'status' => $url->getHealthCheckReport()?->status?->value,
                    ],
                    'sslCertificateReport' => [
                        'isValid' => $url->getSslCertificateReport()?->isValid,
                    ],
                ];
            }

            $data[] = [
                'id' => $webHost->getId(),
                'name' => $webHost->getName(),
                'environnement' => $webHost->getEnvironnement()?->value,
                'expectedVisibility' => $webHost->getExpectedVisibility()?->value,
                'gitlabRemoteUrl' => $webHost->getGitlabRemoteUrl(),
                'gitlabActiveBranch' => $webHost->getGitlabActiveBranch(),
                'lastCommitDate' => $webHost->getLastCommitDate(),
                'configuration' => [
                    'type' => $webHost->getConfiguration()?->getType(),
                    'stack' => $webHost->getConfiguration() instanceof \App\Entity\WebHost\WebHostConfiguration\DockerSwarmConfiguration
                        ? $webHost->getConfiguration()->getStack()
                        : null,
                ],
                'urls' => $urls,
            ];
        }

        return $this->json(['webHosts' => $data]);
    }
}
