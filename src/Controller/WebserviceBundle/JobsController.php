<?php

namespace App\Controller\WebserviceBundle;

use App\Services\WebserviceBundle\WebserviceJobs;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RequestStack;

#[Route("/api/jobs")]
class JobsController extends AbstractWebserviceController
{
    public function __construct(private WebserviceJobs $webserviceJobs) {}

	#[Route('/validation-op', name: 'aquitem_webservice_jobs_validation_op', options: ['expose' => true])]
    public function validationOpAction(RequestStack $requestStack): JsonResponse
    {
        $session = $requestStack->getSession();
        $infosGarage = $session->get('infosGarage');
        $jobsThemes = $session->get('jobsThemes');
        $cible = [];
        foreach($jobsThemes as $theme) {
            array_push($cible, [
                "idJob" => $theme['id'],
                "cible" => $theme['cible']
            ]);
        }
        $parameters = [
            "codeSite" => $infosGarage["codeSite"],
            "codeMagasin" => $infosGarage["codeMagasin"],
            "codeProgramme" => $infosGarage["codeProgramme"],
            "cible" => $cible
        ];

        return $this->returnJson($this->webserviceJobs->validationOp($parameters));
    }

}
