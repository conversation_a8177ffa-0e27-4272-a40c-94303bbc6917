<?php

namespace App\Controller\WebserviceBundle;

use App\Services\WebserviceBundle\WebserviceParticipationOperationNationale;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

#[Route("/api/participation-operation-nationale")]
class ParticipationOperationNationaleController extends AbstractWebserviceController
{
    public function __construct(private WebserviceParticipationOperationNationale $participation) {}

	#[Route("/actualiser-participation", name: "aquitem_webservice_actualiserParticipation", options: ["expose" => true])]
    public function actualiserParticipationAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->participation->actualiserParticipation($request->request->all()));
    }

	#[Route("/annuler-participation", name: "aquitem_webservice_annulerParticipation", options: ["expose" => true])]
    public function annulerParticipationAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->participation->annulerParticipation($request->request->all()));
    }

	#[Route("/apercu-operation/{idOperation}", name: "aquitem_webservice_apercuParticipation", options: ["expose" => true])]
    public function apercuOperationAction($idOperation): Response
    {
        $response = new Response();
        $response->headers->set('Content-Type', 'application/pdf');
        $disposition = $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_INLINE,
            'OPN_' . $idOperation . '.pdf'
        );
        $response->headers->set('Content-Disposition', $disposition);
        $response->setContent($this->participation->apercuOperation(array('idOperation' => $idOperation)));

        return $response;
    }

	#[Route("/enregistrer-participation", name: "aquitem_webservice_enregistrerParticipation", options: ["expose" => true])]
    public function enregistrerParticipationAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->participation->enregistrerParticipation($request->request->all()));
    }

	#[Route("/lister-operations", name: "aquitem_webservice_listerOperations", options: ["expose" => true])]
    public function listerOperationsAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->participation->listerOperations($request->request->all()));
    }

	#[Route("/lister-operations-administrateur", name: "aquitem_webservice_listerOperationsAdministrateur", options: ["expose" => true])]
    public function listerOperationsAdministrateurAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->participation->listerOperationsAdministrateur($request->request->all()));
    }

	#[Route("/ouvrir-operation", name: "aquitem_webservice_ouvrirParticipationOperation", options: ["expose" => true])]
    public function ouvrirOperationAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->participation->ouvrirOperation($request->request->all()));
    }

	#[Route("/participations-operation/{idOperation}/{fileName}", name: "aquitem_webservice_telechargerParticipations", options: ["expose" => true])]
    public function telechargerParticipationsAction($idOperation, $fileName): Response
    {
        $response = new Response();
        $response->headers->set('Content-Type', 'application/vnd.ms-excel');
        $disposition = $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_INLINE,
            $fileName . '.xls'
        );
        $response->headers->set('Content-Disposition', $disposition);
        $response->setContent($this->participation->telechargerParticipations(array('idOperation' => $idOperation)));

        return $response;
    }

	#[Route("/visuel-operation/{idOperation}", name: "aquitem_webservice_visuelParticipationOperation", options: ["expose" => true])]
    public function visuelOperationAction($idOperation): Response
    {
        $response = new Response();
        $response->headers->set('Content-Type', 'image/jpeg');
        $disposition = $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_INLINE,
            'OPN_' . $idOperation . '.jpg'
        );
        $response->headers->set('Content-Disposition', $disposition);
        $response->setContent($this->participation->visuelOperation(array('idOperation' => $idOperation)));

        return $response;
    }
}
