<?php

namespace App\Controller\WebserviceBundle;

use App\Services\WebserviceBundle\WebservicePnr;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;

#[Route("/pnr")]
class PnrController extends AbstractWebserviceController
{
    public function __construct(private WebservicePnr $pnr) {}

	#[Route("/searchItems", name: "aquitem_webservice_searchItems", options: ["expose" => true])]
    public function searchItemsAction(Request $request): JsonResponse
    {
        return $this->returnJson($this->pnr->searchItems($request->request->all()));
    }
}
