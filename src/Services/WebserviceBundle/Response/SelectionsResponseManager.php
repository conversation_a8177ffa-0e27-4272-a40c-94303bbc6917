<?php

namespace App\Services\WebserviceBundle\Response;

class SelectionsResponseManager extends AbstractResponseManager implements ResponseManagerInterface
{
    CONST MES_DOSSIERS = 'lesDossiers';
    CONST PARTAGES_EXPEDITEUR = 'lesPartagesExpediteur';
    CONST PARTAGES_BENEFICIAIRE = 'lesPartagesBeneficiaire';

    /**
     * Retourne la liste des dossiers au format [[id, libelle]]
     * @return array
     */
    public function extractFolderList(): array
    {
        $valeurs = $this->webserviceResponse->getValeurs();
        $dossiers = array_reduce($valeurs->toArray(), function ($acc, $item) {
            return $acc = $item['nom'] === self::MES_DOSSIERS ? $item['valeurs'] : $acc;
        }, null);
        if(!$dossiers) {
            return false;
        }
        $dossiers = array_filter($dossiers, function ($dossier) {
            return (trim($dossier['libelle']) !== "") && (trim($dossier['id']) !== "");
        });
        return array_map(function ($dossier) {
            return array(
                "id" => $dossier['id'],
                "libelle" => $dossier["libelle"]
            );
        }, $dossiers);
    }

    /**
     * Retourne la listes des sélections au format [[id], [libelle], [description], [archivable], [modifiable], [supprimable], [selectionAvancee]]
     * @return array
     */
    public function extractSelectionList(): array
    {
        $valeurs = $this->webserviceResponse->getValeurs();
        $dossiers = array_reduce($valeurs->toArray(), function ($acc, $item) {
            return $acc = $item['nom'] === self::MES_DOSSIERS ? $item['valeurs'] : $acc;
        }, null);

        $selectionsList = array();
        if($dossiers) {
            foreach ($dossiers as $dossier) {
                $selections = $dossier['valeurs'][0]['valeurs'];
                $selectionsList = array_merge($selectionsList, $selections);
            }
        }
        return $selectionsList;
    }

    public function extractSimpleSelectionList(){
    	return array_values(array_filter($this->extractSelectionList(), function($selection){
            $horsFid = "false";
    	    if(array_key_exists('selectionHorsFid', $selection)) {
    	        $horsFid = $selection['selectionHorsFid'];
            }
    		return $horsFid === "false" && $selection['selectionAvancee'] === "false" && $selection['selectionExterne'] === "false" && $selection['quotasActifs'] == "false";
		}));
	}
}
