<?php

namespace App\Services\WebserviceBundle\Response;


class ModuleExclusionsResponseManager extends AbstractResponseManager implements ResponseManagerInterface
{
    /**
     * @return array
     */
    public function setFormatedExclusionDatas(): array
    {
        $promotionDatas = $this->webserviceResponse->getValeurs()[0]['xml']['resultat']['promotion'];
        /* MAGASIN */
        $magasins = "";
        $sites = "";
        $importMagasins = "0";
        $collectionMagasins = "";
        $idsMagasins = "";
        $codesSiteMagasin = [];
        if(isset($promotionDatas['collectionMagasin']['magasin'])) {
            $collectionMagasins = $promotionDatas['collectionMagasin']['magasin'];
            if (is_array($collectionMagasins) && isset($collectionMagasins['MAGASIN'])) {
                $magasins = $collectionMagasins['MAGASIN'];
                $sites = $collectionMagasins['SITE'];
            }
        }
        /* PRODUITS */
        // niveaux
        $idsNiveaux = [];
        $valeursNiveaux = [];
        $collectionNiveaux = "";
        if(isset($promotionDatas['collectionNiveau']['niveau'])) {
            $collectionNiveaux = $promotionDatas['collectionNiveau']['niveau'];
            if(!isset($collectionNiveaux[0])) {
                $collectionNiveaux = array(0 => $promotionDatas['collectionNiveau']['niveau']);
            }
            $idsNiveaux = array_column($collectionNiveaux, 'NIVEAU');
            $valeursNiveaux = array_column($collectionNiveaux, 'VALEUR');
            /* TODO : ajouter les libelles des ids et des valeurs quand ils seront ajoutés au WS */
        }
        // import codes
        $produits = "";
        $importProduits = "0";
        $collectionProduits = "";
        $nbProduitsImport = 0;
        if(isset($promotionDatas['collectionProduit']['produit'])) {
            $nbProduitsImport = $promotionDatas['collectionProduit']['NBPRODUITSREELS'];
            $collectionProduits = $promotionDatas['collectionProduit']['produit'];
            if(is_array($collectionProduits)) {
                $importProduits = "1";
                // si 1 seul produit, le retour du WS n'a pas la même structure....
                if(isset($collectionProduits['CODEPRODUIT'])) {
                    $produits = $collectionProduits['CODEPRODUIT'];
                } else {
                    $produits = implode(',', array_column($collectionProduits, 'CODEPRODUIT'));
                }
            }
        }
        /* CONFLITS REGLES BLOQUANTES */
        $conflits = [];
        if(isset($promotionDatas['collectionPromoBlocante']['promoblocante'])) {
            $conflits['promotionsBloquantes'] = $promotionDatas['collectionPromoBlocante']['promoblocante'];
            if(!array_key_exists(0,$promotionDatas['collectionPromoBlocante']['promoblocante'])) {
                $conflits['promotionsBloquantes'] = array(0 => $promotionDatas['collectionPromoBlocante']['promoblocante']);
            }
            // formatage dates + suppression regles en doublon
            $doublon = false;
            $keyConflitASupprimer = [];
            foreach($conflits['promotionsBloquantes'] as $key=>$promoblocante) {
                $countIdRegle = array_count_values(array_column($conflits['promotionsBloquantes'], 'IDPROMOTION'))[$promoblocante['IDPROMOTION']];
                if($countIdRegle > 1) {
                    if($doublon) {
                        $keyConflitASupprimer[] = $key;
                    }
                    $doublon = true;
                }
                $dateDebut = \DateTime::createFromFormat('d/m/Y H:i:s',$promoblocante['DATEDEBUT'])->format('Y-m-d');
                $formatedDateDebut = date_create($dateDebut);
                $conflits['promotionsBloquantes'][$key]['DATEDEBUT'] = array(
                    'jour' => date_format($formatedDateDebut, 'j'),
                    'mois' => date_format($formatedDateDebut, 'n'),
                    'annee' => date_format($formatedDateDebut, 'Y'),
                );
                $dateFin = \DateTime::createFromFormat('d/m/Y H:i:s',$promoblocante['DATEFIN'])->format('Y-m-d');
                $formatedDateFin = date_create($dateFin);
                $conflits['promotionsBloquantes'][$key]['DATEFIN'] = array(
                    'jour' => date_format($formatedDateFin, 'j'),
                    'mois' => date_format($formatedDateFin, 'n'),
                    'annee' => date_format($formatedDateFin, 'Y'),
                );
            }
            if($keyConflitASupprimer !== []) {
                foreach($keyConflitASupprimer as $keyConflit) {
                    unset($conflits['promotionsBloquantes'][$keyConflit]);
                }
            }
        }
        /* CONFLITS REGLES BLOQUEES */
        if(isset($promotionDatas['collectionPromoBloquee']['promobloquee'])) {
            $conflits['promotionsBloquees'] = $promotionDatas['collectionPromoBloquee']['promobloquee'];
            if(!array_key_exists(0,$promotionDatas['collectionPromoBloquee']['promobloquee'])) {
                $conflits['promotionsBloquees'] = array(0 => $promotionDatas['collectionPromoBloquee']['promobloquee']);
            }
            // formatage dates + suppression regles en doublon
            $doublon = false;
            $keyConflitASupprimer = [];
            foreach($conflits['promotionsBloquees'] as $key=>$promoBloquee) {
                $countIdRegle = array_count_values(array_column($conflits['promotionsBloquees'], 'IDPROMOTION'))[$promoBloquee['IDPROMOTION']];
                if($countIdRegle > 1) {
                    if($doublon) {
                        $keyConflitASupprimer[] = $key;
                    }
                    $doublon = true;
                }
                $dateDebut = \DateTime::createFromFormat('d/m/Y H:i:s',$promoBloquee['DATEDEBUT'])->format('Y-m-d');
                $formatedDateDebut = date_create($dateDebut);
                $conflits['promotionsBloquees'][$key]['DATEDEBUT'] = array(
                    'jour' => date_format($formatedDateDebut, 'j'),
                    'mois' => date_format($formatedDateDebut, 'n'),
                    'annee' => date_format($formatedDateDebut, 'Y'),
                );
                $dateFin = \DateTime::createFromFormat('d/m/Y H:i:s',$promoBloquee['DATEFIN'])->format('Y-m-d');
                $formatedDateFin = date_create($dateFin);
                $conflits['promotionsBloquees'][$key]['DATEFIN'] = array(
                    'jour' => date_format($formatedDateFin, 'j'),
                    'mois' => date_format($formatedDateFin, 'n'),
                    'annee' => date_format($formatedDateFin, 'Y'),
                );
            }
            if($keyConflitASupprimer !== []) {
                foreach($keyConflitASupprimer as $keyConflit) {
                    unset($conflits['promotionsBloquees'][$keyConflit]);
                }
            }
        }
        $nbProduitsReels = 0;
        if(is_array($promotionDatas['collectionProduit'])) {
            $nbProduitsReels = (int) $promotionDatas['collectionProduit']['NBPRODUITSREELS'];
        }
        elseif(is_array($promotionDatas['collectionNiveau'])) {
            $nbProduitsReels = (int) $promotionDatas['collectionNiveau']['NBPRODUITSREELS'];
        }
        elseif(is_array($promotionDatas['collectionTousLesProduits'])) {
            $nbProduitsReels = (int) $promotionDatas['collectionTousLesProduits']['NBPRODUITSREELS'];
        }

        $suppressionDesProduitsEnConflits = "FALSE";
        if($promotionDatas['FORCEBLOCAGE']) {
            $suppressionDesProduitsEnConflits = "TRUE";
        }
        return array(
            'idExclusion' => $promotionDatas['IDOPERATION'],
            'regleModifiable' => $promotionDatas['MODIFIABLE'],
            'titre' => $promotionDatas['TITRE'],
            'titreOriginal' => $promotionDatas['TITRE'],
            'descriptif' => isset($promotionDatas['DESCRIPTIF']) ? $promotionDatas['DESCRIPTIF'] : "",
            'dateDebut' => \DateTime::createFromFormat('d/m/Y H:i:s',$promotionDatas['collectionPeriode']['periode']['DATEDEBUT'])->format('d/m/Y'),
            'dateFin' => \DateTime::createFromFormat('d/m/Y H:i:s',$promotionDatas['collectionPeriode']['periode']['DATEFIN'])->format('d/m/Y'),
            'niveauUser' => $promotionDatas['NIVEAUUSER'],
            'magasins' => $magasins,
            'sites' => $sites,
            'idsMagasins' => $idsMagasins,
            'idsNiveaux' => $idsNiveaux,
            'valeursNiveaux' => $valeursNiveaux,
            'codesArticles' => $produits,
            'nbProduitsImport' => $nbProduitsImport,
            'suppressionDesProduitsEnConflits' => $suppressionDesProduitsEnConflits,
            'nbProduitsReels' => $nbProduitsReels,
            'minimumAchat' => $promotionDatas['MINIMUMACHAT'],
            'minimumAchatType' => $promotionDatas['IDTYPEMNTMINIMUN'],
            'gain' => $promotionDatas['GAIN'],
            'coef' => $promotionDatas['COEFF'],
            'importMagasins' => $importMagasins,
            'importProduits' => $importProduits,
            'typeNiveaux' => [],
            'libNiveaux' => [],
            'conflits' => $conflits,
        );
    }
}