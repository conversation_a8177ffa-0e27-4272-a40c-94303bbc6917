<?php

namespace App\Services\WebserviceBundle\Response;

class GestionModelesResponseManager extends AbstractResponseManager implements ResponseManagerInterface
{
    CONST TAG_PREFIX = '{';
    CONST TAG_SUFFIX = '}';
    CONST DESINSCRIPTION_PREFIX = 'prefixePageDesinscription@@@';
    CONST DESINSCRIPTION_SUFFFIX = '@@@suffixePageDesinscription';
    CONST MIROIR_PREFIX = 'prefixePageMiroir@@@';
    CONST MIROIR_SUFFFIX = '@@@suffixePageMiroir';
    CONST FORMAT_TAG_EMAIL = '%s-%d';

    CONST TYPE_EDITEUR_HTML = 0;
    CONST TYPE_EDITEUR_MOSAICO = 1;

    /**
     * @return array
     */
    public function getModeleDetails(): array
    {
        $columnName = 'nom';
        $modele = array();
        $modeleCollection = $this->webserviceResponse->getValeurs();
        $itemsDetails = $modeleCollection->map(function($element) use($columnName){
            return $element[$columnName];
        });
        foreach($itemsDetails as $item) {
            $modele[$item] = $modeleCollection->get($itemsDetails->indexOf($item));
        }
        return $modele;
    }

    public function getDefaultsTags($message, $tags, $group = 'tagsSmsMessage') {
        preg_match_all('#\\'.GestionModelesResponseManager::TAG_PREFIX.'([^'.GestionModelesResponseManager::TAG_SUFFIX.']+)\\'.GestionModelesResponseManager::TAG_SUFFIX.'#', $message, $matches);
        $matches[1] = array_unique($matches[1]);
        $defaultTags = array();
        if (count($tags) > 0) {
            foreach($matches[1] as $key=>$matche) {
                $tagFound = false;
                foreach($tags as $tag){
                    if($tag['id'] == $matche) {
                        $tagFound = true;
                        $idTag = array_key_exists('idTag', $tag) ? $tag['idTag'] : '';
                        $lib = $tag['libelle'];
                        $taille = $tag['tailleMaximale'];
                        $valeurParDefaut = $tag['valeurParDefaut'];
                        if (array_key_exists('valeurParDefautUtilisateur', $tag)) {
                            $valeurParDefautUtilisateur = $tag['valeurParDefautUtilisateur'];
                        }
                        else {
                            $valeurParDefautUtilisateur = $valeurParDefaut;
                        }
                        $valeurApercu = array_key_exists('valeurApercu', $tag) ? $tag['valeurApercu'] : '';
                    }
                }
                $taillePrefixe = strlen(GestionModelesResponseManager::TAG_PREFIX);
                $tailleSuffixe = strlen(GestionModelesResponseManager::TAG_SUFFIX);
                $tailleTag = strlen($matche);

                if ($tagFound) {
                    $defaultTags[] = array(
                        'id' => $matche,
                        'idTag' => $idTag,
                        'libelle' => $lib,
                        'valeurParDefaut' => $valeurParDefaut,
                        'valeurParDefautUtilisateur' => $valeurParDefautUtilisateur,
                        'valeurApercu' => $valeurApercu,
                        'tailleMaximale' => $taille,
                        'tailleTag' => $taillePrefixe+$tailleTag+$tailleSuffixe,
                        'group' => $group);
                }
            }
        }
        /* tri par ordre alpha */
        if($defaultTags !== []) {
            $keys = array_keys($defaultTags);
            foreach($defaultTags as $k => $v) {
                $libelles[$k] = strtolower($v['libelle']);
            }

            array_multisort($libelles, SORT_ASC, $defaultTags);
            $defaultTags = array_combine($keys, $defaultTags);
        }

        return $defaultTags;
    }

    public function getDefaultsOriginalsTags($originalsTags, $message, $tags, $group) {
        $defaultTags = $this->getDefaultsTags($message, $tags, $group);
        foreach ($originalsTags as $originalTag) {
            foreach ($defaultTags as $key => $defaultTag) {
                if ($defaultTag['idTag'] == $originalTag['id']) {
                    $defaultTags[$key]['valeurParDefaut'] = $originalTag['valeurParDefaut'];
                }
                $defaultTags[$key]['group'] = $group;
            }
        }
        return $defaultTags;
    }

    public function replaceTags($router, $modele, $keyMessage = 'message', $keyTag = 'lesTagsDuModele', $htmlWrap = false): string
    {
        $tags = array();
        $messageOrigin = '';
        if (array_key_exists($keyMessage, $modele)) {
            $messageOrigin = $modele[$keyMessage]['valeur'];
            if (array_key_exists($keyTag, $modele)) {
                $tags = $modele[$keyTag]['valeurs'];
            }
        }

        $message = $messageOrigin;
        if(count($tags) > 0) {
            if (!$htmlWrap) {
                foreach($tags as $tag) {
                    if ($keyMessage != 'sujet' && $modele['typeEditeur']['valeur'] == "2") {
                        $message = str_replace(self::TAG_PREFIX.$tag['id'].self::TAG_SUFFIX, "{{".$tag['libelle']."}}", $message);
                    } elseif ($keyMessage == 'messageSMS') {
                        $message = str_replace(self::TAG_PREFIX.$tag['id'].self::TAG_SUFFIX, self::TAG_PREFIX.$tag['libelle'].self::TAG_SUFFIX, $message);
                    } else {
                        $message = str_replace(self::TAG_PREFIX.$tag['id'].self::TAG_SUFFIX, $this->formatTagForUser($tag, $router, $htmlWrap), $message);
                    }
                }
            }
            else {
                $pattern = '/({((?<=\{)(?!\s*\{)[^{}]+)})/';
                $message = preg_replace_callback(
                    $pattern,
                    function ($matches) use ($tags, $htmlWrap, $messageOrigin, $router, $keyMessage, $modele) {
                        $id = $matches[2];
                        $lib = $matches[1];
                        foreach($tags as $tag){
                            if($tag['id'] == $id) {
                                $imgJsonMatches = [];
                                $linkImgJsonMatches = [];
                                $linkPattern = '/href="[^"]*(' . $lib . ')[^"]*"/';
                                if ($keyMessage == 'donneesJson') {
                                    $imgJsonPattern = '/\\"src\\":\\"[^"]*(' . $lib . ')[^"]*"/';
                                    preg_match($imgJsonPattern, $messageOrigin, $imgJsonMatches);
                                    $linkImgJsonPattern = '/\\"link\\":\\"[^"]*(' . $lib . ')[^"]*"/';
                                    preg_match($linkImgJsonPattern, $messageOrigin, $linkImgJsonMatches);
                                    $linkPattern = '/href=\\\\\\\\\\\\\\"[^"]*(' . $lib . ')[^"]*"/';
                                }
                                $imgPattern = '/src="[^"]*(' . $lib . ')[^"]*"/';
                                preg_match($imgPattern, $messageOrigin, $imgMatches);
                                preg_match($linkPattern, $messageOrigin, $linkMatches);
                                $preHeaderPattern = '/<div[^>]+class="pre\-header\-text"[^>]*>.*(' . $lib . ').*<\/div>/';
                                preg_match($preHeaderPattern, $messageOrigin, $preHeaderMatches);

                                if ($modele['typeEditeur']['valeur'] == "2") {
                                    if (count($imgMatches) || count($linkMatches) || count($linkImgJsonMatches) || count($imgJsonMatches) || count($preHeaderMatches)) {
                                        $lib = self::TAG_PREFIX.$tag['libelle']."-".str_replace("ref", "", $tag["id"]).self::TAG_SUFFIX;
                                    } else {
                                        $idTag = isset($tag["idTag"]) ? $tag["idTag"] : $tag["id"];
                                        $lib = "<span alt='".self::TAG_PREFIX.$tag['libelle']."-".str_replace("ref", "", $tag["id"]).self::TAG_SUFFIX."' data-id='".$idTag."'>{{".$tag["libelle"]."}}</span>";
                                    }
                                } elseif (count($imgMatches) || count($linkMatches) || count($linkImgJsonMatches) || count($imgJsonMatches) || count($preHeaderMatches)) {
                                    $lib = $this->formatTagForUser($tag, $router, false);
                                } else {
                                    $lib = $this->formatTagForUser($tag, $router, $htmlWrap);
                                    if ($keyMessage == 'donneesJson') {
                                        $lib = str_replace('"','\\\\\"',$lib);
                                    }
                                }
                            }
                        }
                        return $lib;
                    },
                    $messageOrigin
                );
            }
        }
        return $message;
    }

    /**
     * @param array $tag
     * @return string
     */
    public function formatTagForUser($tag, $router, $htmlWrap = false) {
        if (isset($tag['idTag'])) {
            $formatTag = sprintf(self::FORMAT_TAG_EMAIL, $tag['libelle'], (int) str_replace('ref', '', $tag['id']));
        }
        else {
            $formatTag = $tag['libelle'];
        }
        if ($htmlWrap) {
            if (isset($tag['idTag'])) {
                $formatTag = '<img alt="'.self::TAG_PREFIX.$formatTag.self::TAG_SUFFIX.'" class="generated-image-tags" data-id="'.$tag['idTag'].'" src="'. $router->generate('aquitem_requeteur_image_tags', array('libelle' => $formatTag)) .'" style="border:0px;display:inline-block;">';
            }
            else {
                $formatTag = '<img alt="'.self::TAG_PREFIX.$formatTag.self::TAG_SUFFIX.'" class="generated-image-tags" data-id="'.$tag['id'].'" src="'. $router->generate('aquitem_requeteur_image_tags', array('libelle' => $formatTag)) .'" style="border:0px;display:inline-block;">';
            }
        }
        else {
			$formatTag = self::TAG_PREFIX.$formatTag.self::TAG_SUFFIX;
		}
        return $formatTag;
    }

    public function replacePreHeaderTags($router, $modele, $keyMessage = 'message', $keyTag = 'lesTagsDuModele'): string
    {
        $tags = array();
        $messageOrigin = '';
        if (array_key_exists($keyMessage, $modele)) {
            $messageOrigin = $modele[$keyMessage]['valeur'];
            if (array_key_exists($keyTag, $modele)) {
                $tags = $modele[$keyTag]['valeurs'];
            }
        }

        $message = $messageOrigin;
        if(count($tags) > 0) {
            $pattern = '/({((?<=\{)(?!\s*\{)[^{}]+)})/';
            $message = preg_replace_callback(
                $pattern,
                function ($matches) use ($tags, $messageOrigin, $router, $keyMessage) {
                    $id = $matches[2];
                    $lib = $matches[1];
                    foreach($tags as $tag){
                        if($tag['id'] == $id) {
                            $preHeaderPattern = '/<div[^>]+class="pre\-header\-text"[^>]*>.*(' . $lib . ').*<\/div>/';
                            preg_match($preHeaderPattern, $messageOrigin, $preHeaderMatches);
                            if (count($preHeaderMatches) !== 0) {
                                $lib = $this->formatTagForUser($tag, $router, false);
                            }
                        }
                    }
                    return $lib;
                },
                $messageOrigin
            );
        }
        return $message;
    }
}
