<?php
namespace App\Services\WebserviceBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;

/**
 * @extends WebserviceBase<\App\Services\WebserviceBundle\Response\ModuleCarteCadeauResponseManager>
 */
class WebserviceCartesCadeaux extends WebserviceBase
{
    CONST ID = "ModuleCarteCadeau";
    CONST SERVICE_CARTES_CADEAUX_ACTIVER = "activer";
    CONST SERVICE_CARTES_CADEAUX_ANNULER = "annuler";
    CONST SERVICE_CARTES_CADEAUX_CONFIG = "chargerParametresConfiguration";
    CONST SERVICE_CARTES_CADEAUX_LISTE_PDV = "listerPointsDeVente";
    CONST SERVICE_CARTES_CADEAUX_PROLONGER = "prolonger";
    CONST SERVICE_CARTES_CADEAUX_RECHERCHER = "rechercher";
    CONST SERVICE_CARTES_CADEAUX_UTILISER = "utiliser";
    CONST SERVICE_CARTES_CADEAUX_TRANSACTIONS = "listerTransactions";
    CONST SERVICE_CARTES_CADEAUX_CHAMPS_RECHERCHE = "listerParametresRechercheTransactions";
    CONST SERVICE_CARTES_CADEAUX_EXPORT_TRANSACTIONS = "exporterTransactions";

    protected $id = self::ID;

    /**
     * @param array $parameters {
     *     @type string numeroCarte
     *     @type float montantActivation
     *     @type string dateActivation
     *     @type string site
     *     @type string magasin
     *     @type ipUser
     *     @type idSession
     *     @type userAgent
     * }
     * @return WebserviceResponse
     */
    public function activer(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_ACTIVER, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type string numeroCarte
     *     @type string idOperationAnnulee
     *     @type string site
     *     @type string magasin
     *     @type ipUser
     *     @type idSession
     *     @type userAgent
     * }
     * @return WebserviceResponse
     */
    public function annuler(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_ANNULER, $parameters);
    }

    /**
     * @return WebserviceResponse
     */
    public function chargerParametresConfiguration() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_CONFIG);
    }

    /**
     * @return WebserviceResponse
     */
    public function listerPointsDeVente() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_LISTE_PDV);
    }

    /**
     * @param array $parameters {
     *     @type string numeroCarte
     *     @type string dateProlongation
     *     @type string site
     *     @type string magasin
     *     @type ipUser
     *     @type idSession
     *     @type userAgent
     * }
     * @return WebserviceResponse
     */
    public function prolonger(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_PROLONGER, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type string numeroCarte
     * }
     * @return WebserviceResponse
     */
    public function rechercher(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_RECHERCHER, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type string numeroCarte
     *     @type float montantUtilisation
     *     @type string dateActivation
     *     @type string site
     *     @type string magasin
     *     @type ipUser
     *     @type idSession
     *     @type userAgent
     * }
     * @return WebserviceResponse
     */
    public function utiliser(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_UTILISER, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type string dateDebut
     *     @type string dateFin
     *     @type string site
     *     @type string magasin
     *     @type int nbResults
     *     @type int startResult
     * }
     * @return WebserviceResponse
     */
    public function listerTransactions(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_TRANSACTIONS, $parameters);
    }

    /**
     * @return WebserviceResponse
     */
    public function listerParametresRechercheTransactions() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_CHAMPS_RECHERCHE);
    }

    /**
     * @return string
     */
    public function exporterTransactions(array $parameters) {
        return $this->postRequest(self::SERVICE_CARTES_CADEAUX_EXPORT_TRANSACTIONS, $parameters, 'xls');
    }
}