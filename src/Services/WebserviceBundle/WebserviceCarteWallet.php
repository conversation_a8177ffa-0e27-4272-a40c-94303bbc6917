<?php

namespace App\Services\WebserviceBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;

/**
 * @see http://aqui-jboss-dev.aquitem.net/aquitem/servicesWeb/Wallet?id=WlisteServices
 */
class WebserviceCarteWallet extends WebserviceBase
{
    const ID = "Wallet";
    const SERVICE_AFFICHER_PERSONNALISATION = "afficherPersonnalisation";
    const SERVICE_ANNULER_PERSONNALISATION = "annulerPersonnalisation";
    const SERVICE_ENREGISTRER_PERSONNALISATION = "enregistrerPersonnalisation";
    const SERVICE_HISTORIQUE_PERSONNALISATION = "historiquePersonnalisation";

    protected $id = self::ID;

    /**
     * @return WebserviceResponse
     */
    public function afficherPersonnalisation(): WebserviceResponse
    {
        return $this->postRequest(self::SERVICE_AFFICHER_PERSONNALISATION);
    }

    /**
     * @return WebserviceResponse
     */
    public function annulerPersonnalisation(): WebserviceResponse
    {
        return $this->postRequest(self::SERVICE_ANNULER_PERSONNALISATION);
    }

    /**
     * @param array $parameters {
     *     @type string titre
     *     @type string dateDebut
     *     @type string heureDebut
     *     @type string dateFin
     *     @type string heureFin
     *     @type string messageNotification
     *     @type string urlVisuel
     * }
     * @return WebserviceResponse
     */
    public function enregistrerPersonnalisation(array $parameters): WebserviceResponse
    {
        return $this->postRequest(self::SERVICE_ENREGISTRER_PERSONNALISATION, $parameters);
    }

    /**
     * @return WebserviceResponse
     */
    public function historiquePersonnalisation(): WebserviceResponse
    {
        return $this->postRequest(self::SERVICE_HISTORIQUE_PERSONNALISATION);
    }
}
