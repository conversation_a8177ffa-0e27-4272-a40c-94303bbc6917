<?php


namespace App\Services\WebserviceBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;
use Symfony\Component\VarDumper\VarDumper;

/**
 * @extends WebserviceBase<\App\Services\WebserviceBundle\Response\ModuleClientResponseManager>
 */
class WebservicePortailEnseigne extends WebserviceBase
{
    CONST ID = "ModuleClient";
    CONST SERVICE_LISTER_CRITERES_RECHERCHE = "listerCriteresRecherche";
    CONST SERVICE_RECHERCHER_CLIENT = "rechercherClient";
    CONST SERVICE_AFFICHER_ENTETE_CLIENT = "afficherEnteteClient";
    CONST SERVICE_AFFICHER_DONNEES_CLIENT = "afficherDonneesClient";
    CONST SERVICE_AFFICHER_FIDELITE_CLIENT = "afficherFidelisationClient";
    CONST SERVICE_LISTER_FILTRES_FIDELITE_CLIENT = "listerFiltresFidelisationClient";
    CONST SERVICE_MODIFIER_FICHE_CLIENT = "modifierFicheClient";
    CONST SERVICE_LISTER_MAGASINS = "listerMagasins";
    CONST SERVICE_VERIFIER_CODE_CARTE_CLIENT = "verifierCodeCarteClient";
    CONST SERVICE_FUSIONNER_CLIENT = "fusionnerClient";
    CONST SERVICE_AFFICHER_DONNEES_CLIENT_FUSION = "afficherDonneesClientPourFusion";
    CONST SERVICE_RECUPERER_ID_CLIENT = "recupererIdClient";
	CONST SERVICE_AFFICHER_DETAIL_TICKET = "afficherDetailTicket";
    CONST SERVICE_CREER_CLIENT = "creerClient";
    CONST SERVICE_SAISIE_NOUVEAU_CLIENT = "saisieNouveauClient";
    CONST SERVICE_LISTER_OPTIONS_AIDE_SAISIE = "listerOptionsAideSaisie";
    CONST SERVICE_AJOUTER_BONUS = "ajouterBonus";
    CONST SERVICE_AJOUTER_BONUS_VIA_CODE_CARTE = "ajouterBonusViaCodeCarte";
    CONST SERVICE_AJOUTER_VENTE = "ajouterVente";
    CONST SERVICE_AJOUTER_VENTE_VIA_CODE_CARTE = "ajouterVenteViaCodeCarte";
    CONST SERVICE_AJOUTER_RETOUR_PRODUIT_VIA_CODE_CARTE = "retourProduitViaCodeCarte";
    CONST SERVICE_AJOUTER_RETOUR_PRODUIT = "retourProduit";
    CONST SERVICE_LISTER_FAMILLES_PRODUITS = "listerFamillesDeProduits";
    CONST SERVICE_LISTER_MOTIFS_BONUS = "listerMotifsBonus";
    CONST SERVICE_VERIFIER_CODE_COUPON = "verifierCodeCheque";
    CONST SERVICE_LISTER_MOTIFS_RETOUR_PRODUIT = "listerMotifsRetourProduit";
    CONST SERVICE_LISTER_MOTIFS_RETRAIT_BONUS = "listerMotifsRetranchementBonus";
    CONST SERVICE_RETRAIT_BONUS = "retrancherBonus";
    CONST SERVICE_RETRAIT_BONUS_VIA_CODE_CARTE = "retrancherBonusViaCodeCarte";
    CONST SERVICE_INDICATIF_TELEPHONE_NUMERIQUE = "convertirIndicatifTelephoniqueNumerique";
    CONST SERVICE_INDICATIF_TELEPHONE_TEXTUEL = "convertirIndicatifTelephoniqueTextuel";
    CONST SERVICE_CREER_ET_FUSIONNER_CLIENT = "creerEtFusionnerClient";
    CONST SERVICE_MODIFIER_ET_FUSIONNER_CLIENT = "modifierEtFusionnerFicheClient";
    CONST SERVICE_ANONYMISER_CLIENT = "anonymiserClient";
    CONST SERVICE_AFFICHER_OFFRES_EN_COURS = "afficherOffresEnCours";


    protected $id = self::ID;

    /**
     * @return WebserviceResponse
     */
    public function listerCriteresRecherche() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_CRITERES_RECHERCHE);
    }

    /**
     * @param array $parameters {
     *     @type array<int> idsCriteres
     *     @type array<string> valeurs
     *     @type array<string> typesDonnees
     *     @type array<string> colsDonnees
     *     @type int startResult
     *     @type int nbResults
     * }
     * @return WebserviceResponse
     */
    public function rechercherClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_RECHERCHER_CLIENT, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClient
     * }
     * @return WebserviceResponse
     */
    public function afficherEnteteClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AFFICHER_ENTETE_CLIENT, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClient
     * }
     * @return WebserviceResponse
     */
    public function afficherDonneesClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AFFICHER_DONNEES_CLIENT, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClient
     *     @type array<int> idsFiltres
     *     @type array<string> valeursFiltres
     *     @type int startResult
     *     @type int nbResults
     * }
     * @return WebserviceResponse
     */
    public function afficherFidelisationClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AFFICHER_FIDELITE_CLIENT, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClient
     * }
     * @return WebserviceResponse
     */
    public function listerFiltresFidelisationClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_FILTRES_FIDELITE_CLIENT, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClient
     *     @type array<string> idsRubriques
     *     @type array<string> referenceEnregistrement
     *     @type array<int> idsDonnees
     *     @type array<string> typesDonnees
     *     @type array<string> colsDonnees
     *     @type array<string> valeurs
     * }
     * @return WebserviceResponse
     */
    public function modifierFicheClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_MODIFIER_FICHE_CLIENT, $parameters);
    }


    /**
     * @return WebserviceResponse
     */
    public function listerMagasins() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_MAGASINS);
    }

    /**
     * @param array $parameters {
     *     @type int codeCarteClient
     *     @type bool nouvelleCarte
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     * }
     * @return WebserviceResponse
     */
    public function verifierCodeCarteClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_VERIFIER_CODE_CARTE_CLIENT, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int codeCarteClientSource
     *     @type int codeCarteClientDestination
     *     @type string nouvelleCarteDestination
     *     @type string codeSite
     *     @type string codeMagasin
     * }
     * @return WebserviceResponse
     */
    public function fusionnerClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_FUSIONNER_CLIENT, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClient
     * }
     * @return WebserviceResponse
     */
    public function afficherDonneesClientPourFusion(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AFFICHER_DONNEES_CLIENT_FUSION, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int codeCarteClient
     * }
     * @return WebserviceResponse
     */
    public function recupererIdClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_RECUPERER_ID_CLIENT, $parameters);
    }

	/**
	 * @param array $parameters {
	 *     @type int idClient
	 *     @type int idVente
	 * }
	 * @return WebserviceResponse
	 */
	public function afficherDetailTicket(array $parameters) : WebserviceResponse {
		return $this->postRequest(self::SERVICE_AFFICHER_DETAIL_TICKET, $parameters);
	}

    /**
     * @param array $parameters {
     *     @type int codeCarteClient
     *     @type int codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     *     @type array<string> idsRubriques
     *     @type array<int> idsDonnees
     *     @type array<string> typesDonnees
     *     @type array<string> colsDonnees
     *     @type array<string> valeurs
     * }
     * @return WebserviceResponse
     */
    public function creerClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CREER_CLIENT, $parameters);
    }

    /**
     * @return WebserviceResponse
     */
    public function saisieNouveauClient() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_SAISIE_NOUVEAU_CLIENT);
    }

    /**
     * @return WebserviceResponse
     */
    public function listerOptionsAideSaisie(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_OPTIONS_AIDE_SAISIE, $parameters);
    }



    /**
     * @param array $parameters {
     *     @type int idClient
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     *     @type int quantiteBonus
     *     @type string idMotifBonus
     * }
     * @return WebserviceResponse
     */
    public function ajouterBonus(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AJOUTER_BONUS, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type string codeCarteClient
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     *     @type int quantiteBonus
     *     @type string idMotifBonus
     * }
     * @return WebserviceResponse
     */
    public function ajouterBonusViaCodeCarte(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AJOUTER_BONUS_VIA_CODE_CARTE, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClient
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     *     @type int quantiteBonus
     *     @type string idMotifBonus
     *     @type string dateVente
     *     @type float montantVente
     *     @type array<string> idsFamillesProduits
     *     @type array<string> codesCheques
     *     @type array<string> validationsCheques
     * }
     * @return WebserviceResponse
     */
    public function ajouterVente(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AJOUTER_VENTE, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type string codeCarteClient
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     *     @type int quantiteBonus
     *     @type string idMotifBonus
     *     @type string dateVente
     *     @type float montantVente
     *     @type array<string> idsFamillesProduits
     *     @type array<string> codesCheques
     *     @type array<string> validationsCheques
     * }
     * @return WebserviceResponse
     */
    public function ajouterVenteViaCodeCarte(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AJOUTER_VENTE_VIA_CODE_CARTE, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type string codeCarteClient
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     *     @type string idMotifRetourProduit
     *     @type string dateVente
     *     @type float montantVente
     *     @type array<string> idsFamillesProduits
     * }
     * @return WebserviceResponse
     */
    public function ajouterRetourProduitViaCodeCarte(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AJOUTER_RETOUR_PRODUIT_VIA_CODE_CARTE, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type string idClient
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     *     @type string idMotifRetourProduit
     *     @type string dateVente
     *     @type float montantVente
     *     @type array<string> idsFamillesProduits
     * }
     * @return WebserviceResponse
     */
    public function ajouterRetourProduit(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AJOUTER_RETOUR_PRODUIT, $parameters);
    }


    /**
     * @return WebserviceResponse
     */
    public function listerFamillesDeProduits() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_FAMILLES_PRODUITS);
    }

    /**
     * @return WebserviceResponse
     */
    public function listerMotifsBonus() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_MOTIFS_BONUS);
    }

    /**
     * @return WebserviceResponse
     */
    public function listerMotifsRetourProduit() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_MOTIFS_RETOUR_PRODUIT);
    }

    /**
     * @param array $parameters {
     *     @type string codeCarteClient
     *     @type string codeCheque
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     * }
     * @return WebserviceResponse
     */
    public function verifierCodeCheque(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_VERIFIER_CODE_COUPON, $parameters);
    }

    /**
     * @return WebserviceResponse
     */
    public function listerMotifsRetranchementBonus() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_MOTIFS_RETRAIT_BONUS);
    }

    /**
     * @param array $parameters {
     *     @type int idClient
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     *     @type int quantiteRetranchementBonus
     *     @type int idMotifRetranchementBonus
     * }
     * @return WebserviceResponse
     */
    public function retrancherBonus(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_RETRAIT_BONUS, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int codeCarteClient
     *     @type string codeVendeur
     *     @type string codeSite
     *     @type string codeMagasin
     *     @type int quantiteRetranchementBonus
     *     @type int idMotifRetranchementBonus
     * }
     * @return WebserviceResponse
     */
    public function retrancherBonusViaCodeCarte(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_RETRAIT_BONUS_VIA_CODE_CARTE, $parameters);
    }

    public function indicatifNumeriqueToTexte(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_INDICATIF_TELEPHONE_NUMERIQUE, $parameters);
    }

    /**
     * @return WebserviceResponse
     */
    public function indicatifTexteToNumerique(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_INDICATIF_TELEPHONE_TEXTUEL, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClientAFusionner
     *     @type string typologieClient
     *     @type int codeCarteClient
     *     @type int codeVendeur
     *     @type string codeSiteClient
     *     @type string codeMagasinClient
     *     @type array<string> idsRubriques
     *     @type array<int> idsDonnees
     *     @type array<string> typesDonnees
     *     @type array<string> colsDonnees
     *     @type array<string> valeurs
     * }
     * @return WebserviceResponse
     */
    public function creerEtFusionnerClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_CREER_ET_FUSIONNER_CLIENT, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClientAFusionner
     *     @type int idClient
     *     @type array<string> idsRubriques
     *     @type array<int> idsDonnees
     *     @type array<string> typesDonnees
     *     @type array<string> colsDonnees
     *     @type array<string> valeurs
     * }
     * @return WebserviceResponse
     */
    public function modifierEtFusionnerFicheClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_MODIFIER_ET_FUSIONNER_CLIENT, $parameters);
    }

    /**
     * @param array $parameters {
     *     @type int idClient
     * }
     * @return WebserviceResponse
     */
    public function anonymiserClient(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_ANONYMISER_CLIENT, $parameters);
    }

    public function afficherOffresEnCours(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_AFFICHER_OFFRES_EN_COURS, $parameters);
    }
}
