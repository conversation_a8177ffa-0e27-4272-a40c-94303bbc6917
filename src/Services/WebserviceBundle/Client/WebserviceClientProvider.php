<?php

namespace App\Services\WebserviceBundle\Client;

use Symfony\Contracts\HttpClient\HttpClientInterface;

class WebserviceClientProvider
{
	private ?HttpClientInterface $authenticatedClient = null;

	public function __construct(
		private readonly HttpClientInterface $client,
	) {
	}

	public function getClient(): HttpClientInterface
	{
		return $this->authenticatedClient ?? $this->client;
	}

	public function addCookie(string $cookie): void
	{
//		$this->client = $this->client->withOptions([
//			'headers' => $cookies,
//		]);
	}
}
