<?php

namespace App\Services\WebserviceBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;
use App\Exception\WebserviceBundle\WebserviceException;
use Symfony\Component\HttpClient\TraceableHttpClient;
use Symfony\Component\Stopwatch\Stopwatch;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\Mime\Part\Multipart\FormDataPart;
use Symfony\Component\HttpFoundation\RequestStack;

class WebserviceBase
{
    const ID = "";

    protected $id = self::ID;

    protected $client;

    /**
     * @var \App\Services\WebserviceBundle\SymfonySessionCookieJar
     */
    protected $cookieJar;

    /**
     * @var \Symfony\Component\Stopwatch\Stopwatch
     */
    private $stopWatch;

    public $baseUrl;

    public $requestStack;

    /**
     * WebserviceBase constructor.
     * @param                                        $baseUrl
     * @param SymfonySessionCookieJar                $cookieJar
     * @param \Symfony\Component\Stopwatch\Stopwatch $stopWatch
     */
    public function __construct(HttpClientInterface $client, string $baseUrl, SymfonySessionCookieJar $cookieJar, Stopwatch $stopWatch, RequestStack $requestStack)
    {
        $this->baseUrl = $baseUrl;
        $this->stopWatch = $stopWatch;
        $this->cookieJar = $cookieJar;
        $this->client = $client;
        $this->requestStack = $requestStack;
    }

    /**
     * @return HttpClientInterface
     */
    public function getClient(): HttpClientInterface
    {
		// TODO : comprendre pourquoi le client est-il un TraceableHttpClient
        if (!$this->client instanceof HttpClientInterface || $this->client instanceof TraceableHttpClient) {
            $this->cookieJar->load();
            $cookies = [];
            foreach ($this->cookieJar->all() as $cookie) {
                $cookies["Cookie"] = $cookie;
            }
			$this->client = $this->client->withOptions([
				'headers' => $cookies,
			]);
//            $this->client = HttpClient::create([
//                'headers' => $cookies,
//            ]);
        }
        return $this->client;
    }

    /**
     * @param string $serviceId
     * @param array $parameters
     * @return WebserviceResponse|string
     */
    public function postRequest(string $serviceId, array $parameters = array(), $type = 'json'): WebserviceResponse | string
    {
        $parameters = array_merge($parameters, array("id" => $serviceId));
        $headers = ['Content-Type' => 'application/x-www-form-urlencoded'];
        $body = $this->buildQuery($parameters);

        if($serviceId == "validationOp") {
            $headers = ['Content-Type' => 'application/json'];
            $body = json_encode($parameters);
        }

        $cookie = null;
        $this->stopWatch->start('webservice_call');
        try {
            $response = $this->getClient()->request(
                'POST',
                $this->baseUrl . $this->id,
                [
                    'body' => $body,
                    'headers' => $headers
                ]
            );
            // save cookie ws
            if (isset($response->getHeaders()['set-cookie'])) {
                $cookie = $response->getHeaders()['set-cookie'];
            }
        } catch (\Exception $exception) {
			throw $exception;
            throw new WebserviceException();
        }
        $this->stopWatch->stop('webservice_call');
        $this->cookieJar->save($cookie);

        $webserviceResponse = $this->parseResponse($response, $type);
        if ($webserviceResponse instanceof WebserviceResponse) {
            $class = '\\App\\Services\\WebserviceBundle\\Response\\' . $this->id . 'ResponseManager';
            $webserviceResponse->responseManager = new $class($webserviceResponse);
        }

        $jsonString = $type === "json" ? $webserviceResponse->getResponse() : $webserviceResponse;

        // Prepare log entry
        $logEntry = array(
            'request' => array(
                'serviceId' => $serviceId,
                'parameters' => $parameters,
            ),
            'response' => $jsonString,
        );

        return $webserviceResponse;
    }

    /**
     * @param string $serviceId
     * @param array $parameters
     * @return WebserviceResponse|string
     */
    public function postRequestFiles(string $serviceId, array $parameters = array(), $type = 'json'): WebserviceResponse | string
    {
        $parameters = array_merge($parameters, array("id" => $serviceId));
        $formData = new FormDataPart($parameters);
        $this->stopWatch->start('webservice_call');
        $cookie = null;
        try {
            $response = $this->getClient()->request(
                'POST',
                $this->baseUrl . $this->id,
                [
                    'headers' => $formData->getPreparedHeaders()->toArray(),
                    'body' => $formData->bodyToIterable(),
                ]
            );
            // save cookie ws
            if (isset($response->getHeaders()['set-cookie'])) {
                $cookie = $response->getHeaders()['set-cookie'];
            }
        } catch (\Exception $exception) {
            throw new WebserviceException();
        }

        $this->stopWatch->stop('webservice_call');
        $this->cookieJar->save($cookie);

        $webserviceResponse = $this->parseResponse($response, $type);
        if ($webserviceResponse instanceof WebserviceResponse) {
            $class = '\\App\\Services\\WebserviceBundle\\Response\\' . $this->id . 'ResponseManager';
            $webserviceResponse->responseManager = new $class($webserviceResponse);
        }
/*
        $jsonString = $type === "json" ? $webserviceResponse->getResponse() : $webserviceResponse;

        // Prepare log entry
        $logEntry = array(
            'request' => array(
                'serviceId' => $serviceId,
                'parameters' => $parameters,
            ),
            'response' => $jsonString,
        );

        // Encode the log entry as JSON
        $logEntryJson = json_encode($logEntry, JSON_PRETTY_PRINT);

        $timestamp = microtime(true);

        // Format the timestamp with minutes, seconds, and milliseconds
        $dateWithMilliseconds = date("Y-m-d_H-i-s", $timestamp) . "_" . sprintf("%03d", ($timestamp - floor($timestamp)) * 1000);

        // Specify the JSON log file
        $logFileName = '../ListenerLogs/file' . $dateWithMilliseconds . '.json';

        // If it doesn't exist, create a new file with the log entry enclosed in square brackets
        file_put_contents($logFileName, '[' . $logEntryJson . ']' . PHP_EOL);
*/
        return $webserviceResponse;
    }
    /**
     * @param ResponseInterface $response
     * @return WebserviceResponse|string
     */
    public function parseResponse($response, $type = 'json'): WebserviceResponse | string
    {
        $content = $response->getContent();
        if ($type == 'json') {
            $content = json_decode($content, true);
            if (!$content) {
                throw new WebserviceException();
            }
            return WebserviceResponse::fromResponse($content);
        } else {
            return $content;
        }
    }

    /**
     * @see http://stackoverflow.com/a/8171667/4607710
     * @param array $parameters
     * @return mixed
     */
    public function buildQuery(array $parameters = array()) {
        $query = http_build_query($parameters, "", '&');
        // TODO supprimer la condition après intégration des WS par JO
        return $parameters['id'] === "listerReseau" ? $query : preg_replace('/%5B(?:[0-9]|[1-9][0-9]+)%5D=/', '=', $query);
        //return preg_replace('/%5B(?:[0-9]|[1-9][0-9]+)%5D=/', '=', $query);
    }
}
