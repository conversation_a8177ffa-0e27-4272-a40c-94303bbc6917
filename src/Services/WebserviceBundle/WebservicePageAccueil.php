<?php

namespace App\Services\WebserviceBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;

/**
 * @see http://aqui-jboss-dev.aquitem.net/aquitem/servicesWeb/PageAccueil?id=WlisteServices
 * @extends WebserviceBase<\App\Services\WebserviceBundle\Response\PageAccueilResponseManager>
 */
class WebservicePageAccueil extends WebserviceBase
{
    CONST ID = "PageAccueil";
    CONST SERVICE_ENREGISTRER_WIDGETS = "EnregistrerWidgets";
    CONST SERVICE_LISTER_CODES_SITES_MAGASINS = "listerCodesSitesMagasins";

    protected $id = self::ID;

    /**
     * @param array $parameters {
     *     @type <array>int idsWidgets
     * }
     * @return WebserviceResponse
     */
    public function EnregistrerWidgets(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_ENREGISTRER_WIDGETS, $parameters);
    }

    /**
     * @return WebserviceResponse
     */
    public function listerCodesSitesMagasins() : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_CODES_SITES_MAGASINS, []);
    }
}