<?php

namespace App\Services\WebserviceBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;

/**
 * @see http://aqui-jboss-dev.aquitem.net/aquitem/servicesWeb/Contacts?id=WlisteServices
 */
class WebserviceContacts extends WebserviceBase
{
    CONST ID = "Contacts";
    CONST SERVICE_AJOUTER_CONTACT = "ajouterContact";
    CONST SERVICE_CHARGER_CONTACT = "chargerContact";
    CONST SERVICE_LISTER_CONTACTS = "listerContacts";
    CONST SERVICE_MODIFIER_CONTACT = "modifierContact";
    CONST SERVICE_SUPPRIMER_CONTACT = "supprimerContact";

    protected $id = self::ID;

    /**
     * @param array $parameters {
     *      @type string nom
     *      @type string prenom
     *      @type string email
     *      @type int telMobile
     * }
     * @return WebserviceResponse
     */
    public function ajouterContact(array $parameters): WebserviceResponse
    {
        return $this->postRequest(self::SERVICE_AJOUTER_CONTACT, $parameters);
    }

    /**
     * @param array $parameters {
     *      @type int idContact
     * }
     * @return WebserviceResponse
     */
    public function chargerContact(array $parameters): WebserviceResponse
    {
        return $this->postRequest(self::SERVICE_CHARGER_CONTACT, $parameters);
    }

    /**
     * @param array $parameters {
     *      @type string contactableEmail
     *      @type string contactableSMS
     * }
     * @return WebserviceResponse
     */
    public function listerContacts(array $parameters): WebserviceResponse
    {
        return $this->postRequest(self::SERVICE_LISTER_CONTACTS, $parameters);
    }

    /**
     * @param array $parameters {
     *      @type int idContact
     *      @type string nom
     *      @type string prenom
     *      @type string email
     *      @type int telMobile
     * }
     * @return WebserviceResponse
     */
    public function modifierContact(array $parameters): WebserviceResponse
    {
        return $this->postRequest(self::SERVICE_MODIFIER_CONTACT, $parameters);
    }

    /**
     * @param array $parameters {
     *      @type int idContact
     * }
     * @return WebserviceResponse
     */
    public function supprimerContact(array $parameters): WebserviceResponse
    {
        return $this->postRequest(self::SERVICE_SUPPRIMER_CONTACT, $parameters);
    }
}
