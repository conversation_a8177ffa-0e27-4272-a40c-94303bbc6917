<?php

namespace App\Services\WebserviceBundle;

use Symfony\Component\BrowserKit\Cookie;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\BrowserKit\CookieJar;

class SymfonySessionCookieJar extends CookieJar
{
    /** @var string session key */
    private $sessionKey;

    /** @var bool Control whether to persist session cookies or not. */
    private $storeSessionCookies;

    /** @var RequestStack */
    private $requestStack;

    /**
     * Create a new SessionCookieJar object
     *
     * @param \Symfony\Component\HttpFoundation\Session\Session $session
     * @param string                                            $sessionKey          Session key name to store the cookie
     *                                                                               data in session
     * @param bool                                              $storeSessionCookies Set to true to store session cookies
     *                                                                               in the cookie jar.
     */
    public function __construct(RequestStack $requestStack, $sessionKey, $storeSessionCookies = false)
    {
        $this->requestStack = $requestStack;
        $this->sessionKey = $sessionKey;
        $this->storeSessionCookies = $storeSessionCookies;
    }

    /**
     * Saves cookies to session when shutting down
     */
    public function __destruct()
    {
        $this->save([]);
    }

    /**
     * Save cookies to the client session
     */
    public function save($cookie)
    {
        // save cookie ws
        if($cookie && !headers_sent()) {
            $this->requestStack->getSession()->set('session_key', json_encode($cookie));
        }
    }

    /**
     * Load the contents of the client session into the data array
     */
    public function load()
    {
        if (!$this->requestStack->getSession()->has($this->sessionKey)) {
            return;
        }
        $data = json_decode($this->requestStack->getSession()->get($this->sessionKey), true);
        if (is_array($data)) {
            foreach ($data as $cookie) {
                $this->set(Cookie::fromString($cookie));
            }
        } elseif (strlen($data) !== 0) {
            throw new \RuntimeException("Invalid cookie data");
        }
    }

}
