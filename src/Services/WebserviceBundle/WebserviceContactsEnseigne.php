<?php

namespace App\Services\WebserviceBundle;

use App\Entity\WebserviceBundle\WebserviceResponse;

/**
 * @see http://aqui-jboss-dev.aquitem.net/aquitem/servicesWeb/ContactsEnseigne?id=WlisteServices
 */
class WebserviceContactsEnseigne extends WebserviceBase
{
    CONST ID = "ContactsEnseigne";
    CONST SERVICE_ENREGISTRER_CONTACTS_ENSEIGNE = "enregistrerContacts";
    CONST SERVICE_LISTER_CONTACTS_ENSEIGNE = "listerContacts";

    protected $id = self::ID;

    /**
     * @param array $parameters {
     *     @type <array>int idsWidgets
     * }
     * @return WebserviceResponse
     */
    public function enregistrerContacts(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_ENREGISTRER_CONTACTS_ENSEIGNE, $parameters);
    }

    /**
     * @return WebserviceResponse
     */
    public function listerContacts(array $parameters) : WebserviceResponse {
        return $this->postRequest(self::SERVICE_LISTER_CONTACTS_ENSEIGNE, $parameters);
    }
}