<?php

namespace App\Services\WebserviceBundle;

use App\Controller\RequeteurBundle\SelectionController;
use App\Entity\WebserviceBundle\WebserviceResponse;
use Doctrine\Common\Collections\ArrayCollection;

/**
 * @see http://aqui-jboss-dev.aquitem.net/aquitem/servicesWeb/OperationsNationales?id=WlisteServices
 * @extends WebserviceBase<\App\Services\WebserviceBundle\Response\OperationsNationalesResponseManager>
 */
class WebserviceOperationNationale extends WebserviceBase
{
	CONST ID = "OperationsNationales";
	CONST SERVICE_VISUEL_OPERATION = "visuelOperation";
	CONST SERVICE_APERCU_OPERATION = "apercuOperation";
	CONST SERVICE_PARAMETRER_OPERATION = "parametrerOperation";
	CONST SERVICE_ENREGISTRER_SELECTION_COMPLEMENTAIRE = "enregistrerSelectionComplementaire";
	CONST SERVICE_CREER_SELECTION_COMPLEMENTAIRE = "creerSelectionComplementaire";
	CONST SERVICE_ANNULER_SELECTION_COMPLEMENTAIRE = "annulerSelectionComplementaire";
	CONST SERVICE_MODIFIER_TARIF = "modifierTarif";
	CONST SERVICE_PARAMETRER_OPERATION_ET_TYPE_MAILING = "parametrerOperationEtTypeMailing";

	protected $id = self::ID;

	/**
	 * @param array $parameters {
	 *     @type int idOperation
	 * }
	 * @return ?string
	 */
	public function visuelOperation(array $parameters): ?string
	{
		return $this->postRequest(self::SERVICE_VISUEL_OPERATION, $parameters, 'text');
	}

	/**
	 * @param array $parameters {
	 *     @type int idOperation
	 * }
	 * @return ?string
	 */
	public function apercuOperation(array $parameters): ?string
	{
		return $this->postRequest(self::SERVICE_APERCU_OPERATION, $parameters, 'pdf');
	}


	/**
	 * @param array $parameters {
	 *     @type int idOperation
	 *     @type string codeOffre
	 *     @type bool deDoublonnage
	 *     @type string apercuPDF
	 *     @type object visuelJPG
	 *     @type bool participationAutomatique
	 *     @type bool alerteEmailParticipationAutomatique
	 *     @type string dateFinSouscription
	 *     @type int idCritereTri
	 *     @type string libelleSelectionPrincipale
	 *     @type string pourcentageEchantillonControleSelectionPrincipale
	 *     @type float pourcentageEchantillonControleSelectionComplementaire
	 * }
	 * @return WebserviceResponse
	 */
	public function parametrerOperation(array $parameters) : WebserviceResponse {
		return $this->postRequestFiles(self::SERVICE_PARAMETRER_OPERATION, $parameters);
	}

	/**
	 * @param array $parameters {
	 *     @type int idOperation
	 *     @type int idSelection
	 * }
	 * @return WebserviceResponse
	 */
	public function enregistrerSelectionComplementaire(array $parameters) : WebserviceResponse {
		return $this->postRequest(self::SERVICE_ENREGISTRER_SELECTION_COMPLEMENTAIRE, $parameters);
	}

	/**
	 * @param array $parameters
	 * @return WebserviceResponse
	 */
	public function creerSelectionComplementaire(array $parameters) : WebserviceResponse {
		return $this->postRequest(self::SERVICE_CREER_SELECTION_COMPLEMENTAIRE, $parameters);
	}

	/**
	 * @param array $parameters
	 * @return WebserviceResponse
	 */
	public function annulerSelectionComplementaire(array $parameters) : WebserviceResponse {
		return $this->postRequest(self::SERVICE_ANNULER_SELECTION_COMPLEMENTAIRE, $parameters);
	}

	/**
	 * @param array $parameters {
	 *     @type int idOperation
	 *     @type int idTarif
	 * }
	 * @return WebserviceResponse
	 */
	public function modifierTarif(array $parameters) : WebserviceResponse {
		return $this->postRequest(self::SERVICE_MODIFIER_TARIF, $parameters);
	}

	/**
	 * @param array $parameters {
	 *     @type int idOperation
	 *     @type string codeOffre
	 *     @type bool deDoublonnage
	 *     @type string apercuPDF
	 *     @type object visuelJPG
	 *     @type bool participationAutomatique
	 *     @type bool alerteEmailParticipationAutomatique
	 *     @type string dateFinSouscription
	 *     @type int idCritereTri
	 *     @type string libelleSelectionPrincipale
	 *     @type string pourcentageEchantillonControleSelectionPrincipale
	 *     @type float pourcentageEchantillonControleSelectionComplementaire
	 *     @type bool supprimerVisuelOperation
	 *     @type bool supprimerApercuOperation
	 *     @type int idCodeOperation
	 *     @type int idTypeMailing
	 * }
	 * @return WebserviceResponse
	 */
	public function parametrerOperationEtTypeMailing(array $parameters) : WebserviceResponse {
		return $this->postRequestFiles(self::SERVICE_PARAMETRER_OPERATION_ET_TYPE_MAILING, $parameters);
	}

}
