<?php
namespace App\Services\RequeteurBundle;

use App\Services\WebserviceBundle\WebserviceOperation;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Bundle\SecurityBundle\Security;
use App\Services\RequeteurBundle\ElasticSearch;
use App\Services\WebserviceBundle\WebserviceContactsEnseigne;
use App\Services\WebserviceBundle\WebservicePageAccueil;
use App\Services\WebserviceBundle\WebserviceParticipationOperationNationale;
use Symfony\Contracts\Translation\TranslatorInterface;

class HomePageManager
{
    private $session;
    private $user;
    private $userProfil;
    private $webserviceOp;
    private $webserviceParticipationOpNat;
    private $webservicePageAccueil;
    private $webserviceContactsEnseigne;
    private $elasticSearch;

    public $myDataviz;
    public $periodes;
    public $profilAdminSiege;
    public $codesSitesMagasins = "";
    public $datavizIndexClient;
    public $datavizIndexOperation;
    public $datavizOperation;
    public $canAccessElasticDataviz;
    public $translator;

    public function __construct(Security $security, RequestStack $requestStack, MyDataviz $myDataviz, WebserviceOperation $webserviceOp, ElasticSearch $elasticSearch, WebserviceParticipationOperationNationale $webserviceParticipationOpNat, WebservicePageAccueil $webservicePageAccueil, WebserviceContactsEnseigne $webserviceContactsEnseigne, TranslatorInterface $translator)
    {
        $this->user = $security->getUser();
        $session = $requestStack->getSession();
        $this->userProfil = $session->get('current_user_profil');
        $this->session = $session;
        if($this->user instanceof \Symfony\Component\Security\Core\User\UserInterface) {
            $this->profilAdminSiege = $this->user->checkDroitsSiege() || $this->user->checkDroitsAdminAquitem();
        }
        $this->webserviceOp = $webserviceOp;
        $this->webserviceParticipationOpNat = $webserviceParticipationOpNat;
        $this->webservicePageAccueil = $webservicePageAccueil;
        $this->webserviceContactsEnseigne = $webserviceContactsEnseigne;
        $this->elasticSearch = $elasticSearch;
        $this->datavizIndexClient = isset($this->userProfil["dataviz_index_client"]) ? $this->userProfil["dataviz_index_client"] : null;
        $this->datavizIndexOperation = isset($this->userProfil["dataviz_index_operation"]) ? $this->userProfil["dataviz_index_operation"] : null;
        $this->datavizOperation = isset($this->userProfil["dataviz_operation"]) ? $this->userProfil["dataviz_operation"] : null;
        $this->canAccessElasticDataviz = (bool) $this->elasticSearch->connectionSuccess($this->datavizIndexClient);

        if(!$this->profilAdminSiege) {
            $codes = $this->webservicePageAccueil->listerCodesSitesMagasins()->getValeurs();
            if(isset($codes[1]['valeurs'])) {
                if(is_array($codes[1]['valeurs'])) {
                    $this->codesSitesMagasins = json_encode($codes[1]['valeurs']);
                }
                else {
                    $this->canAccessElasticDataviz = false;
                }
            }
        }
        $this->periodes = $myDataviz->periodes();
        $this->translator = $translator;
    }

    public function getAllDatas()
    {
        $datas = [];
        /* RACCOURCIS */
        $datas = array_merge($datas, $this->getRaccourcisDatas());

        /* MES OPERATIONS */
        $datas = array_merge($datas, $this->getOpLocalesDatas());

        /* MES OPERATIONS NATIONALES A SOUSCRIPTION */
        $datas = array_merge($datas, $this->getOpNatDatas());

        /* CONTACTS */
        $contactsEnseigne = $this->webserviceContactsEnseigne->listerContacts(['niveauUser' => $this->session->get('niveauUser')])->getValeurs()[0]['valeurs'];
        $datas['contactsEnseigne'] = $contactsEnseigne;

        $datas['canAccessElasticDataviz'] = $this->canAccessElasticDataviz;

        return $datas;

    }

    public function getMarkersNbEncartes($etat)
    {
        $markers = [];
        $markers['etat'] = $etat;
        $nbMagasinsActifsPourMoyenne = 0;
        if($etat == 'actifs') {
            $qNbMagasinsActifsPourMoyenne = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_MAGASINS_ACTIFS, [
                $this->periodes['AUJOURDHUI_ANNEE_PREC'],
                $this->periodes['FIN_AUJOURDHUI']
            ]);
            if($qNbMagasinsActifsPourMoyenne) {
                $nbMagasinsActifsPourMoyenne = $qNbMagasinsActifsPourMoyenne->getData()['aggregations']['query']['query']['VENTES_MAGASIN_cardinality']['value'];
            }
            else {
                return ['refresh' => true, 'etat' => $etat];
            }
            // nb encartés actifs
            $nbEncartesActifs = 0;
            if($this->profilAdminSiege) {
                $qNbEncartesActifs = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_CLIENTS_ENCARTES_ACTIFS, [
                    $this->periodes['AUJOURDHUI_ANNEE_PREC'],
                    $this->periodes['FIN_AUJOURDHUI']
                ]);
                if($qNbEncartesActifs) {
                    $nbEncartesActifs = $qNbEncartesActifs->getData()['aggregations']['query']['query']['VENTES_CODECLIENT_reverse_nested']['doc_count'];
                    $markers['nbEncartesActifs'] = $nbEncartesActifs;
                }
                else {
                    return ['refresh' => true, 'etat' => $etat];
                }
            }
            else {
                $qNbEncartesActifs = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_CLIENTS_ENCARTES_ACTIFS_MAG, [
                    $this->periodes['AUJOURDHUI_ANNEE_PREC'],
                    $this->periodes['FIN_AUJOURDHUI'],
                    $this->codesSitesMagasins
                ]);
                if($qNbEncartesActifs) {
                    $nbEncartesActifs = $qNbEncartesActifs->getData()['aggregations']['query']['query']['VENTES_CODECLIENT_reverse_nested']['doc_count'];
                    $markers['nbEncartesActifs'] = $nbEncartesActifs;
                }
                else {
                    return ['refresh' => true, 'etat' => $etat];
                }
            }
            $nbEncartesActifsPourMoyenne = 0;
            $qNbEncartesActifsPourMoyenne = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_CLIENTS_ENCARTES_ACTIFS, [
                $this->periodes['AUJOURDHUI_ANNEE_PREC'],
                $this->periodes['FIN_AUJOURDHUI']
            ]);
            if($qNbEncartesActifsPourMoyenne) {
                $nbEncartesActifsPourMoyenne = $qNbEncartesActifsPourMoyenne->getData()['aggregations']['query']['query']['VENTES_CODECLIENT_reverse_nested']['doc_count'];
                $markers['moyenneNationaleActifs'] = $nbMagasinsActifsPourMoyenne ? $nbEncartesActifsPourMoyenne / $nbMagasinsActifsPourMoyenne : "N/A";
            }
            else {
                return ['refresh' => true, 'etat' => $etat];
            }
        }
        else {
            // nb encartes inactifs
            $nbMagasinsPourMoyenne = 0;
            $qNbMagasinsPourMoyenne = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_MAGASINS);
            if($qNbMagasinsPourMoyenne) {
                $nbMagasinsPourMoyenne = $qNbMagasinsPourMoyenne->getData()['aggregations']['CLIENTMAGASIN_cardinality']['value'];
            }
            else {
                return ['refresh' => true, 'etat' => $etat];
            }
            $nbEncartes = 0;
            $qNbEncartes = $this->elasticSearch->search($this->datavizIndexClient, $this->profilAdminSiege ? MyDataviz::NB_CLIENTS_ENCARTES : MyDataviz::NB_CLIENTS_ENCARTES_MAG, $this->codesSitesMagasins);
            if($qNbEncartes) {
                $nbEncartes = $qNbEncartes->getData()['hits']['total']['value'];
                $markers['nbEncartes'] = $nbEncartes;
            }
            else {
                return ['refresh' => true, 'etat' => $etat];
            }
            $nbEncartesPourMoyenne = 0;
            $qNbEncartesPourMoyenne = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_CLIENTS_ENCARTES);
            if($qNbEncartesPourMoyenne) {
                $nbEncartesPourMoyenne = $qNbEncartesPourMoyenne->getData()['hits']['total']['value'];
                $markers['moyenneNationale'] = $nbMagasinsPourMoyenne ? $nbEncartesPourMoyenne / $nbMagasinsPourMoyenne : "N/A";
            }
            else {
                return ['refresh' => true, 'etat' => $etat];
            }
        }
        return $markers;
    }

    public function getMarkersClientsActifs()
    {
        // clients actifs 12 mois
        $clientsActifs12mois = 0;
        if($this->profilAdminSiege) {
            $qClientsActifs12mois = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_CLIENTS_ACTIFS_12MOIS, [
                $this->periodes['AUJOURDHUI_ANNEE_PREC'],
                $this->periodes['FIN_AUJOURDHUI'],
                $this->periodes['AUJOURDHUI_ANNEE_PREC_PREC'],
                $this->periodes['FIN_AUJOURDHUI_ANNEE_PREC']
            ]);
        }
        else {
            $qClientsActifs12mois = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_CLIENTS_ACTIFS_12MOIS_MAG, [
                $this->periodes['AUJOURDHUI_ANNEE_PREC'],
                $this->periodes['FIN_AUJOURDHUI'],
                $this->codesSitesMagasins,
                $this->periodes['AUJOURDHUI_ANNEE_PREC_PREC'],
                $this->periodes['FIN_AUJOURDHUI_ANNEE_PREC'],
                $this->codesSitesMagasins
            ]);
        }
        if($qClientsActifs12mois) {
            $clientsActifs12mois = $qClientsActifs12mois->getData();
            $dClientsActifs12mois = $clientsActifs12mois['aggregations']['query']['query']['doc_count'];
            $nbClientsActifs12mois1 = "N/A";
            $nbClientsActifs12mois2 = "N/A";
            if($dClientsActifs12mois) {
                $nbClientsActifs12mois1 = $clientsActifs12mois['aggregations']['query']['query']['VENTES_CODECLIENT_reverse_nested']['doc_count'];
                $nbClientsActifs12mois2 = $clientsActifs12mois['aggregations']['query']['query2']['VENTES_CODECLIENT_reverse_nested']['doc_count'];
            }
            $progressionActifs = "N/A";
            if($nbClientsActifs12mois1 !== "N/A" && $nbClientsActifs12mois2 !== "N/A") {
                $progressionActifs = (($nbClientsActifs12mois1 - $nbClientsActifs12mois2) / $nbClientsActifs12mois2) * 100;
            }
        }
        else {
            return ['refresh' => true];
        }
        return [
            'nbClientsActifs12mois1' => $nbClientsActifs12mois1,
            'nbClientsActifs12mois2' => $nbClientsActifs12mois2,
            'progressionActifs' => $progressionActifs,
        ];
    }

    public function getMarkersNouveauxEncartes()
    {
        $nouveauxEncartes = 0;
        if($this->profilAdminSiege) {
            $qNouveauxEncartes = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_NOUVEAUX_ENCARTES, [
                $this->periodes['AUJOURDHUI_ANNEE_PREC'],
                $this->periodes['FIN_AUJOURDHUI'],
                $this->periodes['AUJOURDHUI_ANNEE_PREC_PREC'],
                $this->periodes['FIN_AUJOURDHUI_ANNEE_PREC']
            ]);
        }
        else {
            $qNouveauxEncartes = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_NOUVEAUX_ENCARTES_MAG, [
                $this->periodes['AUJOURDHUI_ANNEE_PREC'],
                $this->periodes['FIN_AUJOURDHUI'],
                $this->periodes['AUJOURDHUI_ANNEE_PREC_PREC'],
                $this->periodes['FIN_AUJOURDHUI_ANNEE_PREC'],
                $this->codesSitesMagasins,
            ]);
        }
        if($qNouveauxEncartes) {
            $nouveauxEncartes = $qNouveauxEncartes->getData();
            $nbNouveauxEncartesP1 = $nouveauxEncartes['aggregations']['period_1']['doc_count'];
            $nbNouveauxEncartesP2 = $nouveauxEncartes['aggregations']['period_2']['doc_count'];
            $progressionNouveauxEncartes = "N/A";
            if($nbNouveauxEncartesP1 !== "N/A" && $nbNouveauxEncartesP2 !== "N/A") {
                $progressionNouveauxEncartes = $nbNouveauxEncartesP2 > 0 ? ((($nbNouveauxEncartesP1 - $nbNouveauxEncartesP2) / $nbNouveauxEncartesP2) * 100) : 0;
            }
        }
        else {
            return ['refresh' => true];
        }
        return [
            'nbNouveauxEncartesP1' => $nbNouveauxEncartesP1,
            'nbNouveauxEncartesP2' => $nbNouveauxEncartesP2,
            'progressionNouveauxEncartes' => $progressionNouveauxEncartes,
        ];
    }

    public function getMarkersNbMagasinsAge()
    {
        $nbMagasinsLong = false;
        $nbMagasinsTresLong = false;
        // nb magasins
        $nbMagasinsQuery = $this->elasticSearch->search($this->datavizIndexClient, $this->profilAdminSiege ? MyDataviz::NB_MAGASINS : MyDataviz::NB_MAGASINS_MAGASINS, $this->codesSitesMagasins);
        if($nbMagasinsQuery) {
            $nbMagasins = $nbMagasinsQuery->getData()['aggregations']['CLIENTMAGASIN_cardinality']['value'];
            if(strlen($nbMagasins) == 3) {
                $nbMagasinsLong = true;
            }
            if(strlen($nbMagasins) == 4) {
                $nbMagasinsTresLong = true;
            }
            $ageMoyen = 0;
            if(!$this->profilAdminSiege) {
                $ageMoyenQuery = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::AGE_MOYEN, $this->codesSitesMagasins);
                if($ageMoyenQuery) {
                    $ageMoyen = $ageMoyenQuery->getData()['aggregations']['AGE_avg']['value'];
                }
                else {
                    return ['refresh' => true];
                }
            }
        }
        else {
            return ['refresh' => true];
        }
        return [
            'ageMoyen' => ceil($ageMoyen),
            'nbMagasins' => $nbMagasins,
            'nbMagasinsLong' => $nbMagasinsLong,
            'nbMagasinsTresLong' => $nbMagasinsTresLong,
        ];
    }

    public function getMarkersContactables($etat)
    {
        if($etat == 'actifs') {
            if($this->profilAdminSiege) {
                $contactablesActifs = $this->getContactables($etat, MyDataviz::NB_CONTACTABLES_ACTIFS, [
                    $this->periodes['AUJOURDHUI_ANNEE_PREC'], $this->periodes['FIN_AUJOURDHUI'], 'CONTACT_COURRIER',
                    $this->periodes['AUJOURDHUI_ANNEE_PREC'], $this->periodes['FIN_AUJOURDHUI'], 'CONTACT_EMAIL',
                    $this->periodes['AUJOURDHUI_ANNEE_PREC'], $this->periodes['FIN_AUJOURDHUI'], 'CONTACT_SMS']);
            }
            else {
                $contactablesActifs = $this->getContactables($etat, MyDataviz::NB_CONTACTABLES_ACTIFS_MAG, [
                    $this->periodes['AUJOURDHUI_ANNEE_PREC'], $this->periodes['FIN_AUJOURDHUI'], $this->codesSitesMagasins, 'CONTACT_COURRIER',
                    $this->periodes['AUJOURDHUI_ANNEE_PREC'], $this->periodes['FIN_AUJOURDHUI'], $this->codesSitesMagasins, 'CONTACT_EMAIL',
                    $this->periodes['AUJOURDHUI_ANNEE_PREC'], $this->periodes['FIN_AUJOURDHUI'], $this->codesSitesMagasins, 'CONTACT_SMS']);
            }
            if($contactablesActifs) {
                $markers = [
                    'nbContactablesActifsCourrier' => $contactablesActifs['nbContactables']['courrier'],
                    'totalContactableActifsCourrier' => $contactablesActifs['totalContactables']['courrier'],
                    'pourcentContactablesCourrierActifs' => $contactablesActifs['pourcentContactables']['courrier'],
                    'nbContactablesActifsEmail' => $contactablesActifs['nbContactables']['email'],
                    'totalContactableActifsEmail' => $contactablesActifs['totalContactables']['email'],
                    'pourcentContactablesEmailActifs' => $contactablesActifs['pourcentContactables']['email'],
                    'nbContactablesActifsSms' => $contactablesActifs['nbContactables']['sms'],
                    'totalContactableActifsSms' => $contactablesActifs['totalContactables']['sms'],
                    'pourcentContactablesSmsActifs' => $contactablesActifs['pourcentContactables']['sms'],
                ];
            }
            else {
                $markers = ['refresh' => true];
            }
        }
        else {
            $contactables = $this->getContactables($etat, $this->profilAdminSiege ? MyDataviz::NB_CONTACTABLES : MyDataviz::NB_CONTACTABLES_MAG, $this->profilAdminSiege ? ['CONTACT_COURRIER','CONTACT_EMAIL','CONTACT_SMS'] : ['CONTACT_COURRIER','CONTACT_EMAIL','CONTACT_SMS',$this->codesSitesMagasins]);
            if($contactables) {
                $markers = [
                    'nbContactablesCourrier' => $contactables['nbContactables']['courrier'],
                    'totalContactableCourrier' => $contactables['totalContactables']['courrier'],
                    'pourcentContactablesCourrier' => $contactables['pourcentContactables']['courrier'],
                    'nbContactablesEmail' => $contactables['nbContactables']['email'],
                    'totalContactableEmail' => $contactables['totalContactables']['email'],
                    'pourcentContactablesEmail' => $contactables['pourcentContactables']['email'],
                    'nbContactablesSms' => $contactables['nbContactables']['sms'],
                    'totalContactableSms' => $contactables['totalContactables']['sms'],
                    'pourcentContactablesSms' => $contactables['pourcentContactables']['sms'],
                ];
            }
            else {
                $markers = ['refresh' => true];
            }
        }
        $markers['etat'] = $etat;
        return $markers;
    }

    public function getMarkersPanierMoyen($periode)
    {
        if($periode == 1) {
            $markers = $this->getPanierMoyenDatas([
                [$this->periodes['CA_DEBUT_MOIS_ANNEE'], $this->periodes['CA_HIER']],
                [$this->periodes['CA_DEBUT_MOIS_ANNEE_PREC'], $this->periodes['CA_HIER_ANNEE_PREC']]
            ]);
        }
        if($periode == 2) {
            $markers = $this->getPanierMoyenDatas([
                [$this->periodes['CA_DEBUT_MOIS_PREC_ANNEE'], $this->periodes['CA_FIN_MOIS_PREC_ANNEE']],
                [$this->periodes['CA_DEBUT_MOIS_PREC_ANNEE_PREC'], $this->periodes['CA_FIN_MOIS_PREC_ANNEE_PREC']]
            ]);
        }
        if($periode == 3) {
            $markers = $this->getPanierMoyenDatas([
                [$this->periodes['AUJOURDHUI_ANNEE_PREC'], $this->periodes['FIN_AUJOURDHUI']],
                [$this->periodes['AUJOURDHUI_ANNEE_PREC_PREC'], $this->periodes['FIN_AUJOURDHUI_ANNEE_PREC']]
            ]);
        }
        if($markers) {
            $markers['libMoisPrecAnnee'] = $this->periodes['LIB_DEBUT_MOIS_PREC_ANNEE'];
            $markers['libMoisPrecAnneePrec'] = $this->periodes['LIB_DEBUT_MOIS_PREC_ANNEE_PREC'];
        }
        else {
            $markers['refresh'] = true;
        }
        $markers['periode'] = $periode;
        return $markers;
    }

    public function getMarkersFrequenceAnnuelle($periode)
    {
        if($periode == 1) {
            $markers = $this->getFrequencesDatas([
                [$this->periodes['DEBUT_ANNEE'], $this->periodes['FIN_ANNEE']],
                [$this->periodes['DEBUT_ANNEE_PREC'], $this->periodes['FIN_ANNEE_PREC']]
            ]);
        }
        if($periode == 2) {
            $markers = $this->getFrequencesDatas([
                [$this->periodes['DEBUT_ANNEE_PREC'], $this->periodes['FIN_ANNEE_PREC']],
                [$this->periodes['DEBUT_ANNEE_PREC_PREC'], $this->periodes['FIN_ANNEE_PREC_PREC']]
            ]);
        }
        if($periode == 3) {
            $markers = $this->getFrequencesDatas([
                [$this->periodes['AUJOURDHUI_ANNEE_PREC'], $this->periodes['FIN_AUJOURDHUI']],
                [$this->periodes['AUJOURDHUI_ANNEE_PREC_PREC'], $this->periodes['FIN_AUJOURDHUI_ANNEE_PREC']]
            ]);
        }
        if(!$markers) {
            $markers['refresh'] = true;
        }
        $markers['periode'] = $periode;
        return $markers;
    }

    public function getMarkersChiffreAffaires($periode)
    {
        if($periode == 1) {
            $markers = $this->getCADatas([
                [$this->periodes['CA_DEBUT_MOIS_ANNEE'], $this->periodes['CA_HIER']],
                [$this->periodes['CA_DEBUT_MOIS_ANNEE_PREC'], $this->periodes['CA_HIER_ANNEE_PREC']]
            ]);
        }
        if($periode == 2) {
            $markers = $this->getCADatas([
                [$this->periodes['CA_DEBUT_MOIS_PREC_ANNEE'], $this->periodes['CA_FIN_MOIS_PREC_ANNEE']],
                [$this->periodes['CA_DEBUT_MOIS_PREC_ANNEE_PREC'], $this->periodes['CA_FIN_MOIS_PREC_ANNEE_PREC']]
            ]);
        }
        if($periode == 3) {
            $markers = $this->getCADatas([
                [$this->periodes['AUJOURDHUI_ANNEE_PREC'], $this->periodes['FIN_AUJOURDHUI']],
                [$this->periodes['AUJOURDHUI_ANNEE_PREC_PREC'], $this->periodes['FIN_AUJOURDHUI_ANNEE_PREC']]
            ]);
        }
        if($markers) {
            $markers['periodes'] = $this->periodes;
        }
        else {
            $markers['refresh'] = true;
        }
        $markers['periode'] = $periode;

        return $markers;
    }
    private function getOpLocalesDatas()
    {
        $opEnCours = array();
        $opTerminees = array();
        $tauxRetourActif = false;
        if($this->user->checkDroitsOperations()) {
            // RemontÃ©e des 5 opÃ©rations en cours et des 5 opÃ©rations Ã  venir
            $aujourdhuiObj = new \DateTime();
            $aujourdhui = $aujourdhuiObj->format('d/m/Y');
            $opEnCoursDatas = array(
                'triAsc' => 'false',
                'positionPremiereOperation' => 0,
                'nbOperationsRetournees' => 10,
                'colonneTri' => 'dateDebut',
                'dateOperationMin' => $aujourdhui,
                'dateOperationMax' => $aujourdhui,
                'idTypeOperation' => 0,
                'idStatuts' => array('2','3','7','8'),
                'idCanaux' => ['1', '2', '3'],
            );
            $opEnCours = $this->webserviceOp->rechercherOperationsCompletes($opEnCoursDatas)->getValeurs()[0]['valeurs'];
            $opTermineesDatas = array(
                'triAsc' => 'false',
                'positionPremiereOperation' => 0,
                'nbOperationsRetournees' => 10,
                'colonneTri' => 'dateDebut',
                'idTypeOperation' => 0,
                'idStatuts' => array('6'),
                'idCanaux' => ['1', '2', '3'],
            );
            $allopTerminees = $this->webserviceOp->rechercherOperationsCompletes($opTermineesDatas)->getValeurs()[0]['valeurs'];
            if(!empty($allopTerminees)) {
                foreach($allopTerminees as $key=>$op) {
                    if(isset($op['dateFinOperation'])) {
                        $dateFinOperation = new \DateTime($op['dateFinOperation']);
                        $tsDateFin = $dateFinOperation->getTimestamp();
                        if($tsDateFin < time()) {
                            $opTerminees[] = $op;
                        }
                    }
                }
            }
            if($this->datavizOperation) {
                $canAccessDatavizOperation = $this->elasticSearch->connectionSuccess($this->datavizIndexOperation);
                $tauxRetourActif = true;
                if($opTerminees !== [] && $canAccessDatavizOperation) {
                    foreach($opTerminees as $k=>$op) {
                        $clientsCibles = $this->elasticSearch->search($this->datavizIndexOperation, MyDataviz::CLIENTS_CIBLES, [$op['codeMailing']])->getData()['aggregations']['query']['query']['doc_count'];
                        if(!$this->profilAdminSiege) {
                            $clientsCiblesMag = $this->elasticSearch->search($this->datavizIndexOperation, MyDataviz::CLIENTS_CIBLES_MAG, [$op['codeMailing'], $this->codesSitesMagasins])->getData()['aggregations']['query']['query']['doc_count'];
                        }
                        $clientsCiblesCA = $this->elasticSearch->search($this->datavizIndexOperation, MyDataviz::CLIENTS_CIBLES_CA, [$op['codeMailing']])->getData()['aggregations']['query']['query']['doc_count'];
                        if(!$this->profilAdminSiege) {
                            $clientsCiblesCAMag = $this->elasticSearch->search($this->datavizIndexOperation, MyDataviz::CLIENTS_CIBLES_CA_MAG, [$op['codeMailing'], $this->codesSitesMagasins])->getData()['aggregations']['query']['query']['doc_count'];
                        }
                        if($this->profilAdminSiege) {
                            $taux = $clientsCibles && $clientsCiblesCA ? ($clientsCiblesCA / $clientsCibles) * 100 : 0;
                        }
                        else {
                            $taux = $clientsCiblesMag && $clientsCiblesCAMag ? ($clientsCiblesCAMag / $clientsCiblesMag) * 100 : 0;
                        }

                        $opTerminees[$k]['tauxRetour'] = ceil($taux);
                    }
                }
                if(!empty($opEnCours) && $canAccessDatavizOperation) {
                    foreach($opEnCours as $k=>$op) {
                        $clientsCibles = $this->elasticSearch->search($this->datavizIndexOperation, MyDataviz::CLIENTS_CIBLES, [$op['codeMailing']])->getData()['aggregations']['query']['query']['doc_count'];
                        $clientsCiblesCA = $this->elasticSearch->search($this->datavizIndexOperation, MyDataviz::CLIENTS_CIBLES_CA, [$op['codeMailing']])->getData()['aggregations']['query']['query']['doc_count'];
                        $taux = $clientsCibles && $clientsCiblesCA ? ($clientsCiblesCA / $clientsCibles) * 100 : 0;
                        $opEnCours[$k]['tauxRetour'] = ceil($taux);
                    }
                }
            }
        }

        return ['tauxRetourActif' => $tauxRetourActif, 'opEnCours' => $opEnCours, 'opTerminees' => $opTerminees];
    }

    private function getOpNatDatas() {
        if($this->user->checkDroitsSiege() || $this->user->checkDroitsAdminAquitem()) {
            $opNatDatas = array(
                'triAsc' => 'true',
                'enCours' => 'true',
                'positionPremiereOperation' => 0,
                'nbOperationsRetournees' => 20,
                'colonneTri' => 'dateFinSouscription',
            );
            $opNat = $this->webserviceParticipationOpNat->listerOperationsAdministrateur($opNatDatas)->getValeurs()[0]['valeurs'];
        }
        else {
            $opNatDatas = array(
                'triAsc' => 'true',
                'colonneTri' => 'dateFinSouscription',
            );
            $opNat = $this->webserviceParticipationOpNat->listerOperations($opNatDatas)->getValeurs()[0]['valeurs'];
            foreach($opNat as $k=>$op) {
                if(strtotime($op['dateFinSouscription']) < time()) {
                    unset($opNat[$k]);
                }
            }
        }
        return ['opNat' => $opNat];
    }

    public function getContactables($etat, $query, $params = [])
    {
        $contactables = $totalContactables = $pourcentContactables = [];
        $contactablesQuery = $this->elasticSearch->search($this->datavizIndexClient, $query, $params);
        if($contactablesQuery) {
            $contactablesQuery = $contactablesQuery->getData();
            if($etat != 'actifs') {
                $contactables = [
                    'email' => $contactablesQuery['aggregations']['queryEmail']['buckets'],
                    'courrier' => $contactablesQuery['aggregations']['queryCourrier']['buckets'],
                    'sms' => $contactablesQuery['aggregations']['querySms']['buckets'],
                ];
                $totalContactables = [
                    'email' => $contactablesQuery['hits']['total']['value'],
                    'courrier' => $contactablesQuery['hits']['total']['value'],
                    'sms' => $contactablesQuery['hits']['total']['value'],
                ];
            }
            else { // filtre contactables actifs
                $contactables = [
                    'email' => $contactablesQuery['aggregations']['queryEmail']['query']['buckets'],
                    'courrier' => $contactablesQuery['aggregations']['queryCourrier']['query']['buckets'],
                    'sms' => $contactablesQuery['aggregations']['querySms']['query']['buckets'],
                ];
                $totalContactables = [
                    'email' => $contactablesQuery['aggregations']['queryEmail']['doc_count'],
                    'courrier' => $contactablesQuery['aggregations']['queryCourrier']['doc_count'],
                    'sms' => $contactablesQuery['aggregations']['querySms']['doc_count'],
                ];
            }
            $bucketContactables  = array_map(function($canal) {
                $values = array_filter($canal, function($var) {
                    return $var['key'] == 1;
                });
                $firstKey = array_key_first($values);
                return $values[$firstKey];
            }, $contactables);
            if($bucketContactables !== []) {
                $nbContactables = [
                    'email' => $bucketContactables['email']['doc_count'],
                    'courrier' => $bucketContactables['courrier']['doc_count'],
                    'sms' => $bucketContactables['sms']['doc_count'],
                ];
                $pourcentContactables = [
                    'email' => ($nbContactables['email'] * 100) / $totalContactables['email'],
                    'courrier' => ($nbContactables['courrier'] * 100) / $totalContactables['courrier'],
                    'sms' => ($nbContactables['sms'] * 100) / $totalContactables['sms'],
                ];
            }
        }
        else {
            return false;
        }
        return ['pourcentContactables' => $pourcentContactables, 'nbContactables' => $nbContactables, 'totalContactables' => $totalContactables];
    }

    public function getPanierMoyenDatas($periodesPanier)
    {
        $panierMoyen = 0;
        if($this->profilAdminSiege) {
            $qPanierMoyen = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::PANIER_MOYEN, [
                $periodesPanier[0][0],
                $periodesPanier[0][1],
                $periodesPanier[1][0],
                $periodesPanier[1][1]
            ]);
        }
        else {
            $qPanierMoyen = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::PANIER_MOYEN_MAG, [
                $periodesPanier[0][0], $periodesPanier[0][1],
                $this->codesSitesMagasins,
                $periodesPanier[1][0],
                $periodesPanier[1][1],
                $this->codesSitesMagasins
            ]);
        }
        if($qPanierMoyen) {
            $panierMoyen = $qPanierMoyen->getData();
            $dPanierMoyen_1 = $panierMoyen['aggregations']['query']['query']['doc_count'];
            $panierMoyen_1 = $dPanierMoyen_1 > 0 ? $panierMoyen['aggregations']['query']['query']['VENTES_TTC_avg']['value'] : "N/A";
            $dPanierMoyen_2 = $panierMoyen['aggregations']['query']['query2']['doc_count'];
            $panierMoyen_2 = $dPanierMoyen_2 > 0 ? $panierMoyen['aggregations']['query']['query2']['VENTES_TTC_avg']['value'] : "N/A";
            $progressionPanier = "N/A";
            if(is_numeric($panierMoyen_1) && is_numeric($panierMoyen_2)) {
                $progressionPanier = (($panierMoyen_1 - $panierMoyen_2) / $panierMoyen_2) * 100;
            }
        }
        else {
            return false;
        }
        return [
            'panierMoyen_1' => $panierMoyen_1,
            'panierMoyen_2' => $panierMoyen_2,
            'progressionPanier' => $progressionPanier,
        ];
    }

    public function getFrequencesDatas($periodesFrequence)
    {
        $tickets = 0;
        if($this->profilAdminSiege) {
            $qTickets = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::TICKETS, [
                $periodesFrequence[0][0],
                $periodesFrequence[0][1],
                $periodesFrequence[1][0],
                $periodesFrequence[1][1]
            ]);
            $qCartes = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_CARTES_VENTES, [
                $periodesFrequence[0][0],
                $periodesFrequence[0][1],
                $periodesFrequence[1][0],
                $periodesFrequence[1][1]
            ])->getData();
        }
        else {
            $qTickets = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::TICKETS_MAG, [
                $periodesFrequence[0][0],
                $periodesFrequence[0][1],
                $this->codesSitesMagasins,
                $periodesFrequence[1][0],
                $periodesFrequence[1][1],
                $this->codesSitesMagasins
            ]);
            $qCartes = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::NB_CARTES_VENTES_MAG, [
                $periodesFrequence[0][0],
                $periodesFrequence[0][1],
                $this->codesSitesMagasins,
                $periodesFrequence[1][0],
                $periodesFrequence[1][1],
                $this->codesSitesMagasins
            ])->getData();
        }
        if($qTickets) {
            $tickets = $qTickets->getData();
            $dCartes1 = $qCartes['aggregations']['query']['query']['doc_count'];
            $dCartes2 = $qCartes['aggregations']['query']['query2']['doc_count'];
            $tickets_1 = $tickets['aggregations']['query']['query']['doc_count'];
            $tickets_2 = $tickets['aggregations']['query']['query2']['doc_count'];
            $nbCartes_1 = $dCartes1 > 0 ? (isset($qCartes['aggregations']['query']['query']['VENTES_CODECLIENT_reverse_nested']['doc_count']) ? $qCartes['aggregations']['query']['query']['VENTES_CODECLIENT_reverse_nested']['doc_count'] : $qCartes['aggregations']['query']['query']['VENTES_CODECLIENT_cardinality']['value']) : "N/A";
            $nbCartes_2 = $dCartes2 > 0 ? (isset($qCartes['aggregations']['query']['query2']['VENTES_CODECLIENT_reverse_nested']['doc_count']) ? $qCartes['aggregations']['query']['query2']['VENTES_CODECLIENT_reverse_nested']['doc_count'] : $qCartes['aggregations']['query']['query2']['VENTES_CODECLIENT_cardinality']['value']) : "N/A";
            $frequence_1 = $frequence_2 = $progressionFrequence = "N/A";
            if(is_numeric($tickets_1) && is_numeric($nbCartes_1)) {
                $frequence_1 = $nbCartes_1 > 0 ? $tickets_1 / $nbCartes_1 : 0;
            }
            if(is_numeric($tickets_2) && is_numeric($nbCartes_2)) {
                $frequence_2 = $nbCartes_2 > 0 ? $tickets_2/$nbCartes_2 : 0;
            }
            if(is_numeric($frequence_1) && is_numeric($frequence_2)) {
                $progressionFrequence = $frequence_2 > 0 ? ((($frequence_1 - $frequence_2) / $frequence_2) * 100) : 0;
            }
        }
        else {
            return false;
        }
        return [
            'frequence_1' => $frequence_1,
            'frequence_2' => $frequence_2,
            'progressionFrequence' => $progressionFrequence,
        ];
    }

    public function getCADatas($periodeCA)
    {
        $ca = 0;
        if($this->profilAdminSiege) {
            $qCa = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::CA, [
                $periodeCA[0][0],
                $periodeCA[0][1],
                $periodeCA[1][0],
                $periodeCA[1][1],
                $this->codesSitesMagasins
            ]);
        }
        else {
            $qCa = $this->elasticSearch->search($this->datavizIndexClient, MyDataviz::CA_MAG, [
                $periodeCA[0][0],
                $periodeCA[0][1],
                $this->codesSitesMagasins,
                $periodeCA[1][0],
                $periodeCA[1][1],
                $this->codesSitesMagasins
            ]);
        }
        if($qCa) {
            $ca = $qCa->getData();
            $dCa_1 = $ca['aggregations']['query']['query']['query']['doc_count'];
            $ca_1 = $dCa_1 > 0 ? $ca['aggregations']['query']['query']['query']['VENTES_LIGNES_CA_sum']['value'] : "N/A";
            $dCa_2 = $ca['aggregations']['query']['query2']['query']['doc_count'];
            $ca_2 = $dCa_2 > 0 ? $ca['aggregations']['query']['query2']['query']['VENTES_LIGNES_CA_sum']['value'] : "N/A";
            $progressionCa = "N/A";
            if(is_numeric($ca_1) && is_numeric($ca_2)) {
                $progressionCa = $ca_2 > 0 ? ((($ca_1 - $ca_2) / $ca_2) * 100) : 0;
            }
        }
        else {
            return false;
        }
        return [
            'ca_1' => $ca_1,
            'ca_2' => $ca_2,
            'progressionCa' => $progressionCa,
        ];
    }

    public function getMarkersVentesGraph()
    {
        $ventesPeriode1 = $ventesPeriode2 = [];
        $ventes = 0;
        $qVentes = $this->elasticSearch->search($this->datavizIndexClient, $this->profilAdminSiege ? MyDataviz::VENTES : MyDataviz::VENTES_MAG, [
            $this->periodes['DEBUT_MOIS_ANNEE_PREC_PREC'],
            $this->periodes['FIN_MOIS_ANNEE'],
            $this->codesSitesMagasins
        ]);
        if($qVentes) {
            $ventes = $qVentes->getData()['aggregations']['query']['query']['query']['buckets'];
            foreach($ventes as $key=>$vente) {
                if($key > 0) {
                    $year = date('Y', $vente['key']/1000);
                    $mois = date('n', $vente['key']/1000);
                    if($key <= 12) {
                        $moisPeriode2 = $this->translator->trans("global.mois." . $mois);
                        if($key == 1) {
                            $legend1VentesPeriode2 = $moisPeriode2 . ' ' . $year;
                        }
                        if($key == 12 || array_key_last($ventes)) {
                            $legend2VentesPeriode2 = $moisPeriode2 . ' ' . $year;
                        }
                        $ventesPeriode2[] = [
                            'x' => mb_strtoupper($moisPeriode2),
                            'y' => $vente['VENTES_TTC_sum']['value'],
                        ];
                    }
                    else {
                        $moisPeriode1 = $this->translator->trans("global.mois." . $mois);
                        if($key == 13) {
                            $legend1VentesPeriode1 = $moisPeriode1 . ' ' . $year;
                        }
                        if($key == array_key_last($ventes)) {
                            $legend2VentesPeriode1 = $moisPeriode1 . ' ' . $year;
                        }
                        $ventesPeriode1[] = [
                            'x' => mb_strtoupper($moisPeriode1),
                            'y' => $vente['VENTES_TTC_sum']['value'],
                        ];
                    }
                }
            }
        }
        else {
            return ['refresh' => true];
        }
        return [
            'ventesPeriode1' => json_encode($ventesPeriode1),
            'ventesPeriode2' => json_encode($ventesPeriode2),
            'legend1VentesPeriode1' => $legend1VentesPeriode1,
            'legend2VentesPeriode1' => $legend2VentesPeriode1,
            'legend1VentesPeriode2' => $legend1VentesPeriode2,
            'legend2VentesPeriode2' => $legend2VentesPeriode2,
        ];
    }

    public function getRaccourcisDatas()
    {
        $allShortcuts = $this->initShortcuts();
        $userShortcuts = $this->session->get('widgetsAccueil');
        $myShortcuts = [];
        if(!empty($userShortcuts)) {
            foreach($userShortcuts as $shortcut) {
                $idShortcut = $shortcut['ref'];
                $myShortcuts[$idShortcut] = $allShortcuts[$idShortcut];
                unset($allShortcuts[$idShortcut]);
            }
        }
        else {
            $j = 0;
            foreach($allShortcuts as $key=>$shortcut) {
                if(!empty($shortcut) && $j < 5) {
                    $myShortcuts[$key] = $shortcut;
                    unset($allShortcuts[$key]);
                    $j++;
                }
            }
        }
        $allShortcuts = array_filter($allShortcuts, function($v) {
            return !empty($v);
        });
        foreach($allShortcuts as $key=>$shortcut) {
            $allShortcuts[$key]['id'] = $key;
        }
        usort($allShortcuts, function($a, $b) {
            return $a['sorting'] <=> $b['sorting'];
        });
        return ['allShortcuts' => $allShortcuts, 'myShortcuts' => $myShortcuts,];
    }

    private function initShortcuts()
    {
        $allShortcuts = [];
        for($i=0;$i<19;$i++) {
            $allShortcuts[$i] = [];
        }
        if($this->user->checkDroitsModuleClient()) {
            $allShortcuts[0] = [
                "label" => "homepage.raccourcis.items.mesClients",
                "icon" => "clients",
                "route" => "aquitem_requeteur_portail_enseigne_recherche",
                "params" => [],
                "sorting" => 14,
            ];
            if($this->user->checkDroitsCreationClient()) {
                $allShortcuts[1] = [
                    "label" => "homepage.raccourcis.items.creerClient",
                    "icon" => "creer_client",
                    "route" => "aquitem_requeteur_portail_enseigne_creer_client",
                    "params" => [],
                    "sorting" => 15,
                ];
            }
        }
        if($this->user->checkDroitsSelections()) {
            $allShortcuts[2] = [
                "label" => "homepage.raccourcis.items.creerSelectionCriteres",
                "icon" => "creer_selection",
                "route" => "aquitem_requeteur_creation",
                "params" => [],
                "sorting" => 1,
            ];
            $allShortcuts[8] = [
                "label" => "homepage.raccourcis.items.mesSelections",
                "icon" => "selection",
                "route" => "aquitem_requeteur_selection",
                "params" => [],
                "sorting" => 0,
            ];
            if($this->user->checkDroitsOperations()) {
                $allShortcuts[3] = [
                    "label" => "homepage.raccourcis.items.creerOp",
                    "icon" => "creer_op",
                    "route" => "#",
                    "params" => [],
                    "class" => "btn-creer-operation",
                    "sorting" => 6,
                ];
                $allShortcuts[9] = [
                    "label" => "homepage.raccourcis.items.opPonctuelles",
                    "icon" => "op",
                    "route" => "aquitem_requeteur_operation",
                    "params" => ["type" => 0],
                    "sorting" => 3,
                ];
                if($this->user->checkDroitsOpNatCreer()) {
                    $allShortcuts[10] = [
                        "label" => "homepage.raccourcis.items.opSouscription",
                        "icon" => "OP_souscription",
                        "route" => "aquitem_requeteur_operation",
                        "params" => ["type" => 1],
                        "sorting" => 4,
                    ];
                }
                if($this->user->checkDroitsSerieCreer()) {
                    $allShortcuts[17] = [
                        "label" => "homepage.raccourcis.items.opRecurrentes",
                        "icon" => "op_recurrente",
                        "route" => "aquitem_requeteur_operation",
                        "params" => ["type" => 2],
                        "sorting" => 5,
                    ];
                }
            }
        }
        if($this->user->checkDroitsPromotions() || $this->user->checkDroitsBri() || $this->user->checkDroitsExclusions()) {
			$multiRegles = false;
			$regleRouteParams = [];
			if (($this->user->checkDroitsPromotions() && $this->user->checkDroitsBri())
				|| ($this->user->checkDroitsPromotions() && ($this->user->checkDroitsExclusions() && $this->session->get('canAccessExclusions')))
				|| ($this->user->checkDroitsBri() && ($this->user->checkDroitsExclusions() && $this->session->get('canAccessExclusions')))) {
				$multiRegles = true;
			}
			if($multiRegles) {
				$regleRoute = "aquitem_requeteur_regle_choix_regle";
			}
			else {
				if($this->user->checkDroitsPromotions()) {
					$regleRoute = "aquitem_requeteur_regle_etapes";
				}
				if($this->user->checkDroitsBri()) {
					$regleRoute = "aquitem_requeteur_regle_etapes";
					$regleRouteParams = ['type' => 'bri'];
				}
				if($this->user->checkDroitsExclusions()) {
					$regleRoute = "aquitem_requeteur_regle_etapes";
					$regleRouteParams = ['type' => 'exclusion'];
				}
			}

            $allShortcuts[4] = [
                "label" => "homepage.raccourcis.items.creerRegle",
                "icon" => "creer_regle",
                "route" => $regleRoute,
                "params" => $regleRouteParams,
                "sorting" => 13,
            ];
        }
        if($this->user->checkDroitsPromotions()) {
            $allShortcuts[11] = [
                "label" => "homepage.raccourcis.items.brc",
                "icon" => "bon_cagnotte",
                "route" => "aquitem_requeteur_regle_liste",
                "params" => ['type' => 'avantage-fid'],
                "sorting" => 11,
            ];
        }
        if($this->user->checkDroitsBri()) {
            $allShortcuts[12] = [
                "label" => "homepage.raccourcis.items.bri",
                "icon" => "bon_immediat",
                "route" => "aquitem_requeteur_regle_liste",
                "params" => ['type' => 'bri'],
                "sorting" => 12,
            ];
        }
        if($this->user->checkDroitsAjoutBonus()) {
            $label = "homepage.raccourcis.items.modifierPoints";
            $labelParams = strtolower($this->session->get('type_bonus'));
            if (strtolower($this->session->get('type_bonus')) == "cagnotte" && (isset($this->session->get('current_user_profil')['langue']) && $this->session->get('current_user_profil')['langue'] == "en")) {
                $label = "homepage.raccourcis.items.modifierCagnotte";
                $labelParams = [];
            }
            $allShortcuts[5] = [
                "label" => $label,
                "labelParams" => $labelParams,
                "icon" => "modifier_points_cagnotte",
                "route" => "aquitem_requeteur_portail_enseigne_ajout_bonus",
                "params" => [],
                "sorting" => 18,
            ];
        }
        if($this->session->get('current_user_menu') == 'true' && !$this->user->independantNonFranchise()) {
            $allShortcuts[6] = [
                "label" => "homepage.raccourcis.items.souscrireOperation",
                "icon" => "souscrire",
                "route" => "aquitem_requeteur_participation",
                "params" => [],
                "sorting" => 7,
            ];
        }
        if($this->user->checkDroitsDocumentsLecture()) {
            $allShortcuts[7] = [
                "label" => "homepage.raccourcis.items.docsEtStats",
                "icon" => "doc_stat",
                "route" => "aquitem_requeteur_documents_liste",
                "params" => [],
                "sorting" => 9,
            ];
        }
        if($this->user->checkDroitsExclusions()) {
            $allShortcuts[13] = [
                "label" => "homepage.raccourcis.items.mesExclusions",
                "icon" => "exclusion",
                "route" => "aquitem_requeteur_regle_liste",
                "params" => ['type' => 'exclusion'],
                "sorting" => 12,
            ];
        }
        if($this->user->canAccessDataviz()) {
            $allShortcuts[14] = [
                "label" => "homepage.raccourcis.items.myDataviz",
                "icon" => "dataviz",
                "route" => "aquitem_requeteur_open_dataviz",
                "params" => [],
                "sorting" => 8,
            ];
            $allShortcuts[18] = [
                "label" => "homepage.raccourcis.items.creerSelectionVisuelle",
                "icon" => "creer_dataviz",
                "route" => "aquitem_requeteur_open_dataviz",
                "params" => [],
                "sorting" => 2,
            ];
        }
        if($this->user->checkDroitsAjoutVente()) {
            $allShortcuts[15] = [
                "label" => "homepage.raccourcis.items.creerVente",
                "icon" => "creer_vente",
                "route" => "aquitem_requeteur_portail_enseigne_creer_vente",
                "params" => [],
                "sorting" => 16,
            ];
        }
        if($this->user->checkDroitsRetourProduit()) {
            $allShortcuts[16] = [
                "label" => "homepage.raccourcis.items.retourProduit",
                "icon" => "Retour_achat",
                "route" => "aquitem_requeteur_portail_enseigne_retour_produit",
                "params" => [],
                "sorting" => 17,
            ];
        }
        if($this->user->checkDroitsGestionModeles()) {
            $allShortcuts[19] = [
                "label" => "homepage.raccourcis.items.modeles",
                "icon" => "modeles",
                "route" => "aquitem_requeteur_modele",
                "params" => [],
                "sorting" => 19,
            ];
        }
        if($this->user->checkDroitsAdminAquitem()) {
            $allShortcuts[20] = [
                "label" => "homepage.raccourcis.items.contacts",
                "icon" => "contacts",
                "route" => "aquitem_requeteur_mes_contacts",
                "params" => [],
                "sorting" => 20,
            ];
        }
        if($this->user->checkDroitsCartesCadeaux()) {
            $allShortcuts[21] = [
                "label" => "homepage.raccourcis.items.cartesCadeaux",
                "icon" => "cadeau",
                "route" => "aquitem_requeteur_cartes_cadeaux_recherche",
                "params" => [],
                "sorting" => 75,
            ];
        }
        if($this->user->canAccesPersonnalisationWallets()) {
            $allShortcuts[22] = [
                "label" => "homepage.raccourcis.items.persoWallet",
                "icon" => "wallet",
                "route" => "aquitem_requeteur_carte_wallet_liste",
                "params" => [],
                "sorting" => 80,
            ];
        }

        if($this->user->checkDroitsConsultationSouscriptionJobs()) {
            $allShortcuts[100] = [
                "label" => "homepage.raccourcis.items.ADreseau",
                "icon" => "dataviz",
                "route" => "aquitem_requeteur_jobs_reseau",
                "params" => [],
                "sorting" => 200,
            ];
            $allShortcuts[101] = [
                "label" => "homepage.raccourcis.items.ADsouscriptions",
                "icon" => "OP_souscription",
                "route" => "aquitem_requeteur_jobs_souscriptions",
                "params" => [],
                "sorting" => 200,
            ];
            $allShortcuts[102] = [
                "label" => "homepage.raccourcis.items.ADdetails",
                "icon" => "op",
                "route" => "aquitem_requeteur_jobs_details_souscriptions",
                "params" => [],
                "sorting" => 200,
            ];
        }
        if($this->user->checkDroitsPointsDeVente()) {
            $allShortcuts[110] = [
                "label" => "homepage.raccourcis.items.pdv",
                "icon" => "points-de-vente",
                "route" => "aquitem_requeteur_pdv_recherche",
                "params" => [],
                "sorting" => 200,
            ];
        }

        return $allShortcuts;
    }

    public function userCheckSelectionOnly()
    {
        if($this->user->checkDroitsSelections() &&
            !$this->user->checkDroitsOperations() &&
            $this->session->get('current_user_menu') == 'false' &&
            !$this->user->checkDroitsModuleClient() &&
            !$this->user->canAccessDataviz() &&
            !$this->user->checkDroitsDocumentsLecture() &&
            !$this->user->checkDroitsAjoutVente() &&
            !$this->user->checkDroitsAjoutBonus() &&
            !$this->user->checkDroitsRetourProduit() &&
            !$this->user->checkDroitsPromotions() &&
            !$this->user->checkDroitsExclusions() &&
            !$this->user->checkDroitsBri() &&
            !$this->user->canAccesPersonnalisationWallets() &&
            !$this->user->checkDroitsCartesCadeaux() &&
            !$this->user->checkDroitsSouscriptionJobs() &&
            !$this->user->checkDroitsConsultationSouscriptionJobs()) {
            return true;
        }
        return false;
    }

    public function userCheckParticipationOnly()
    {
        if($this->session->get('current_user_menu') == 'true' &&
            !$this->user->checkDroitsSelections() &&
            !$this->user->checkDroitsOperations() &&
            !$this->user->checkDroitsModuleClient() &&
            !$this->user->canAccessDataviz() &&
            !$this->user->checkDroitsDocumentsLecture() &&
            !$this->user->checkDroitsAjoutVente() &&
            !$this->user->checkDroitsAjoutBonus() &&
            !$this->user->checkDroitsRetourProduit() &&
            !$this->user->checkDroitsPromotions() &&
            !$this->user->checkDroitsExclusions() &&
            !$this->user->checkDroitsBri() &&
            !$this->user->canAccesPersonnalisationWallets() &&
            !$this->user->checkDroitsCartesCadeaux() &&
            !$this->user->checkDroitsSouscriptionJobs() &&
            !$this->user->checkDroitsConsultationSouscriptionJobs()) {
            return true;
        }
        return false;
    }

    public function userCheckClientsOnly()
    {
        if($this->user->checkDroitsModuleClient() &&
            !$this->user->checkDroitsSelections() &&
            $this->session->get('current_user_menu') == 'false' &&
            !$this->user->checkDroitsOperations() &&
            !$this->user->canAccessDataviz() &&
            !$this->user->checkDroitsDocumentsLecture() &&
            !$this->user->checkDroitsAjoutVente() &&
            !$this->user->checkDroitsAjoutBonus() &&
            !$this->user->checkDroitsRetourProduit() &&
            !$this->user->checkDroitsPromotions() &&
            !$this->user->checkDroitsExclusions() &&
            !$this->user->checkDroitsBri() &&
            !$this->user->canAccesPersonnalisationWallets() &&
            !$this->user->checkDroitsCartesCadeaux() &&
            !$this->user->checkDroitsSouscriptionJobs() &&
            !$this->user->checkDroitsConsultationSouscriptionJobs()) {
            return true;
        }
        return false;
    }

    public function userCheckDatavizOnly()
    {
        if($this->user->canAccessDataviz() &&
            !$this->user->checkDroitsSelections() &&
            $this->session->get('current_user_menu') == 'false' &&
            !$this->user->checkDroitsOperations() &&
            !$this->user->checkDroitsModuleClient() &&
            !$this->user->checkDroitsDocumentsLecture() &&
            !$this->user->checkDroitsAjoutVente() &&
            !$this->user->checkDroitsAjoutBonus() &&
            !$this->user->checkDroitsRetourProduit() &&
            !$this->user->checkDroitsPromotions() &&
            !$this->user->checkDroitsExclusions() &&
            !$this->user->checkDroitsBri() &&
            !$this->user->canAccesPersonnalisationWallets() &&
            !$this->user->checkDroitsCartesCadeaux() &&
            !$this->user->checkDroitsSouscriptionJobs() &&
            !$this->user->checkDroitsConsultationSouscriptionJobs()) {
            return true;
        }
    }

    public function userCheckDocsOnly()
    {
        if($this->user->checkDroitsDocumentsLecture() &&
            !$this->user->checkDroitsSelections() &&
            $this->session->get('current_user_menu') == 'false' &&
            !$this->user->checkDroitsOperations() &&
            !$this->user->canAccessDataviz() &&
            !$this->user->checkDroitsModuleClient() &&
            !$this->user->checkDroitsAjoutVente() &&
            !$this->user->checkDroitsAjoutBonus() &&
            !$this->user->checkDroitsRetourProduit() &&
            !$this->user->checkDroitsPromotions() &&
            !$this->user->checkDroitsExclusions() &&
            !$this->user->checkDroitsBri() &&
            !$this->user->canAccesPersonnalisationWallets() &&
            !$this->user->checkDroitsCartesCadeaux() &&
            !$this->user->checkDroitsSouscriptionJobs() &&
            !$this->user->checkDroitsConsultationSouscriptionJobs()) {
            return true;
        }
        return false;
    }
}
