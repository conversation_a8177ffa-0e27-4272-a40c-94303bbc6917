<?php

namespace App\Services\AmabisBundle\Consumer;

/**
 * CheckPhoneNumberSyntax est un consumer du service "ama_mobile_check" de l'API Amabis
 * 
 * Le service "ama_mobile_check" vérifier l'existence
 * d'un numéro de téléphone fixe ou mobile
 */
class CheckPhoneNumber extends Base
{
    const HTTP_METHOD = "POST";
    const WEB_SERVICE_END_POINT = "ama_mobile_check";
    const ENABLE_SAVING_LOGS = true;

    public function check(string $imputation, string $number, $authKey, array $additionalLogParams = []) {
        $response = $this->request($this->getParams($number), $imputation, $authKey, $additionalLogParams)->toArray();
        return $response['result'] ?? [];
    }

    public function getParams($number) {
        return [
            'gsmNumber' => $number,
            'country' => 'FRA'
        ];
    }

    public function getUrl() {
        return $this->url;
    }
}