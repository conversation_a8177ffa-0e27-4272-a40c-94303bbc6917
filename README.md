## Portail & Tools IA
*Portée du projet mouvante*
*Portail de centralisation des accès aux outils internes et surtout plateforme d'accès à des solutions d'IA modulaires et auto-hébergées*

### Connexion SSO
*Ce projet utilise le realm Keycloak "dev-pp" pour la gestion des utilisateurs.*
*Pour plus d'informations sur le fonctionnement de keycloak [voir la documentation du POC](https://gitlab.alienor.net/dev-interne/poc-symfony-sso/-/blob/main/README.md?ref_type=heads)*

Liens utiles :
- [Interface d'administration du realm](https://accounts.alienor.net/admin/dev-pp/console/)
- [Portail utilisateur de Keycloak](https://accounts.alienor.net/realms/dev-pp/account/)
- [Documentation Keycloak](https://www.keycloak.org/docs/latest/authorization_services/index.html#_getting_started_overview)

Sujets :
- refaire des tâches et chiffrages pour PTIA
- chiffrage et découpage de tâches pour TDB Swarm
- asynchrone (quand on travaillera avec de gros fichiers)
- RAG
- SSO
- SSO template au point mort
- upload règles de validation
